rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function hasRole(role) {
      return isAuthenticated() && 
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.roles.hasAny([role]);
    }
    
    function isAdmin() {
      return hasRole('admin');
    }

    function isModerator() {
      return hasRole('moderator') || isAdmin();
    }

    function isValidContentType(contentType) {
      let validTypes = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/svg+xml',
        'application/pdf',
        'text/plain',
        'application/json'
      ];
      return contentType in validTypes;
    }

    function isValidFileSize(size, maxSize) {
      return size < maxSize;
    }

    function isParticipantInTrade(tradeId) {
      return firestore.get(/databases/(default)/documents/trades/$(tradeId))
        .data.participantIds.hasAny([request.auth.uid]);
    }

    // Profile images
    match /users/{userId}/profile/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isOwner(userId) &&
        isValidContentType(request.resource.contentType) &&
        isValidFileSize(request.resource.size, 5 * 1024 * 1024); // 5MB limit
    }

    // Trade evidence
    match /trades/{tradeId}/evidence/{fileName} {
      allow read: if isAuthenticated() && (
        isParticipantInTrade(tradeId) || isModerator()
      );
      allow create: if isAuthenticated() &&
        isParticipantInTrade(tradeId) &&
        isValidContentType(request.resource.contentType) &&
        isValidFileSize(request.resource.size, 20 * 1024 * 1024); // 20MB limit
      allow delete: if isModerator();
    }

    // Public assets
    match /public/{fileName} {
      allow read: if true;
      allow write: if isAdmin() &&
        isValidContentType(request.resource.contentType) &&
        isValidFileSize(request.resource.size, 10 * 1024 * 1024); // 10MB limit
    }

    // System backups
    match /backups/{fileName} {
      allow read, write: if isAdmin();
    }

    // Temporary uploads
    match /temp/{userId}/{fileName} {
      allow read, write: if isOwner(userId) &&
        isValidContentType(request.resource.contentType) &&
        isValidFileSize(request.resource.size, 50 * 1024 * 1024) && // 50MB limit
        // Temporary files are automatically deleted after 24 hours
        request.time < resource.timeCreated + duration.value(24, 'h');
    }
  }
}
