#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

# Make security hooks executable
chmod +x .husky/pre-commit-security-rules

# Run security rules validation first
if ! ./.husky/pre-commit-security-rules; then
    echo "❌ Security rule validation failed. Fix issues before committing."
    exit 1
fi

# Run type checking
if ! npm run type-check; then
    echo "❌ Type checking failed. Fix type errors before committing."
    exit 1
fi

# Run linting
if ! npm run lint; then
    echo "❌ <PERSON>ting failed. Fix lint errors before committing."
    exit 1
fi

# Run security checks
if ! npm run security:check; then
    echo "❌ Security checks failed. Review security issues before committing."
    exit 1
fi

# Run security tests if security-related files changed
if git diff --cached --name-only | grep -E 'firestore\.rules|storage\.rules|firebase-security\.test\.ts'; then
    echo "🔒 Firebase security rules changes detected"
    echo "Running security validation..."
    if ! npm run test:rules; then
        echo "❌ Security rule tests failed. Fix failing tests before committing."
        exit 1
    fi
fi

# Check for sensitive data
if ! npx gitleaks protect --source . --config .gitleaks.toml; then
    echo "❌ Sensitive data detected. Remove sensitive data before committing."
    exit 1
fi

# Run staged files through lint-staged
echo "Running lint-staged..."
npx lint-staged

# Success message
echo "✨ Pre-commit checks passed successfully!"
