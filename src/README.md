# TradeYa

A platform for trading skills and services, connecting people who want to exchange their expertise.

## Live Demo

Check out the live demo at [https://tradeya-45ede.web.app](https://tradeya-45ede.web.app)

## Project Setup

This project is built with:

- React 18
- TypeScript
- Vite
- Firebase (Authentication, Firestore, Storage)
- Tailwind CSS

### Firebase Configuration

This project uses Firebase for authentication, database, and storage. The Firebase configuration is stored in `src/firebase-config.ts` and uses environment variables to keep sensitive information secure.

The following Firebase services are used:

- **Authentication**: For user sign-up, login, and profile management
- **Firestore**: For storing trades, projects, challenges, user profiles, connections, and messages
- **Storage**: For storing user-uploaded images and files
- **Hosting**: For deploying the application
- **Security Rules**: For controlling access to Firestore data

## Current Status

### Completed

- ✅ Basic project structure set up
- ✅ Package.json configuration
- ✅ TypeScript configuration
- ✅ Vite configuration
- ✅ Tailwind CSS integration
- ✅ Firebase configuration
- ✅ Authentication context
- ✅ Basic routing
- ✅ Layout components (Navbar, Footer)
- ✅ Basic page components (Home, Profile, Trades)
- ✅ Sign-up page with validation
- ✅ Profile page with editing functionality
- ✅ Trades listing page with filtering
- ✅ Trade creation form
- ✅ Trade detail page with contact form
- ✅ User dashboard with activity overview
- ✅ Edit and delete trades functionality
- ✅ Password reset functionality
- ✅ Real data storage with Firebase
- ✅ Messaging functionality with real-time updates
- ✅ Notifications system with real-time updates
- ✅ User ratings and reviews system
- ✅ Enhanced user interface with animations and transitions
- ✅ Image upload functionality with Cloudinary
- ✅ CI/CD pipeline with GitHub Actions and Firebase
- ✅ Basic testing setup with Vitest
- ✅ Enhanced user profiles with skill badges and reputation system
- ✅ Project collaboration system with applications
- ✅ Deploy to production
- ✅ Add admin panel
- ✅ Connections system
- ✅ Advanced user directory
- ✅ Challenges system
- ✅ Robust error handling for data fetching
- ✅ Firebase security rules implementation

### In Progress

- 🔄 Write more comprehensive tests
- 🔄 Implement email notifications
- 🔄 Optimize performance
  - Created performance optimization plan
  - Created implementation tracking document
  - Created results tracking document
  - Next steps: Establish baseline metrics and implement optimizations

### To Do

- 📝 Add payment integration (if needed)
- 📝 Add analytics
- 📝 Mobile app support

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository

```bash
git clone <repository-url>
cd tradeya
```

1. Install dependencies

```bash
npm install
```

1. Set up environment variables

Create a `.env` file in the root directory with the following variables:

```env
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
VITE_FIREBASE_APP_ID=your-app-id
```

> **Important**: Never commit your `.env` file to version control. It contains sensitive API keys and credentials. The `.env.example` file is provided as a template.

1. Start the development server

```bash
npm run dev
```

### Building for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## Project Structure

```text
tradeya/
├── public/              # Static assets
├── src/                 # Source code
│   ├── components/      # Reusable components
│   │   ├── layout/      # Layout components (Navbar, Footer)
│   │   ├── ui/          # UI components (Button, Input, etc.)
│   │   └── features/    # Feature-specific components
│   │       ├── chat/    # Chat and messaging components
│   │       ├── connections/ # User connections components
│   │       ├── notifications/ # Notification components
│   │       ├── projects/ # Project-related components
│   │       ├── reviews/ # Review components
│   │       └── uploads/ # File upload components
│   ├── pages/           # Page components
│   │   ├── DashboardPage.tsx  # User dashboard
│   │   ├── ProfilePage.tsx    # User profile
│   │   ├── SignUpPage.tsx     # Sign up form
│   │   ├── TradeDetailPage.tsx # Trade details
│   │   ├── TradesPage.tsx     # Trades listing
│   │   ├── ProjectsPage.tsx   # Projects listing
│   │   ├── ProjectDetailPage.tsx # Project details
│   │   ├── ConnectionsPage.tsx # User connections
│   │   ├── UserDirectoryPage.tsx # User directory
│   │   ├── ChallengesPage.tsx # Challenges listing
│   │   └── admin/            # Admin pages
│   ├── contexts/        # Context providers
│   ├── services/        # Service modules (API calls, etc.)
│   ├── utils/           # Utility functions
│   ├── hooks/           # Custom React hooks
│   ├── App.tsx          # Main App component
│   ├── AuthContext.tsx  # Authentication context
│   ├── firebase-config.ts # Firebase configuration
│   ├── main.tsx         # Entry point
│   └── index.css        # Global styles
├── .env                 # Environment variables (not in repo)
├── .env.example         # Example environment variables
├── firestore.rules      # Firebase security rules
├── firebase.json        # Firebase configuration
├── index.html           # HTML template
├── package.json         # Dependencies and scripts
├── postcss.config.js    # PostCSS configuration
├── tailwind.config.js   # Tailwind CSS configuration
├── tsconfig.json        # TypeScript configuration
├── tsconfig.node.json   # TypeScript Node configuration
└── vite.config.ts       # Vite configuration
```

### Key Files

- **firebase-config.ts**: Contains Firebase initialization and utility functions for authentication, database operations, and storage. Uses environment variables for configuration.
- **AuthContext.tsx**: Provides authentication state and methods throughout the application.
- **App.tsx**: Sets up routing and global providers for the application.
- **firestore.ts**: Contains all Firestore database operations and interfaces.
- **firestore.rules**: Contains Firebase security rules for controlling access to Firestore data.

## Recent Improvements

### Profile Picture Handling

- Enhanced profile picture handling to prioritize the `profilePicture` field in Firebase
- Added automatic migration of images from `photoURL` to `profilePicture` when profiles are updated
- Improved URL formatting for Cloudinary images with proper transformations
- Created specialized components for handling specific user profile pictures
- Added robust fallback mechanisms for missing profile pictures

### Connections System

- Implemented a comprehensive user connections system similar to LinkedIn
- Added connection requests with accept/reject functionality
- Created a connections management page
- Integrated connection status into user profiles

### Advanced User Directory

- Developed a searchable user directory with filtering capabilities
- Implemented skill-based and location-based filtering
- Added user cards with profile previews
- Integrated connection functionality directly into the directory

### Challenges System

- Created a challenges system for users to participate in coding challenges
- Implemented filtering by category, difficulty, and status
- Added challenge details page with participation tracking

### Robust Error Handling

- Implemented comprehensive error handling for data fetching
- Added null checks and fallbacks for missing data
- Improved user experience by preventing crashes due to unexpected data formats

### Firebase Security Rules

- Implemented security rules to control access to Firestore data
- Added rules for specific collections like users, projects, trades, and connections
- Enhanced security while maintaining functionality

## Next Steps

### Immediate Tasks

1. Write more comprehensive tests
2. Implement email notifications
3. Continue performance optimization:
   - Establish baseline performance metrics
   - Identify performance bottlenecks
   - Implement high-priority optimizations
   - Validate improvements
4. Add analytics
5. Enhance mobile responsiveness

### Medium-term Tasks

1. Enhance the messaging system between users
2. Improve notifications for new messages, trade requests, etc.
3. Expand user ratings and reviews system
4. Enhance the dashboard with analytics and statistics
5. Add more features to the challenges system

### Long-term Tasks

1. Implement advanced search and filtering with AI recommendations
2. Add payment integration (if needed)
3. Expand admin panel capabilities for better moderation
4. Set up comprehensive analytics and reporting
5. Optimize performance and accessibility
6. Develop dedicated mobile app
7. Implement internationalization and localization

## CI/CD Pipeline

The project uses GitHub Actions for continuous integration and continuous deployment:

- **Continuous Integration**: Automatically runs on every push and pull request to the `main` branch
  - Installs dependencies
  - Runs linting checks
  - Runs tests
  - Builds the application

- **Continuous Deployment**: Automatically deploys to Firebase Hosting when changes are pushed to the `main` branch
  - Only runs after all tests and builds pass
  - Deploys to the production environment

### Local Deployment

To deploy the application locally for testing:

```bash
# Login to Firebase (only needed once)
npm run firebase:login

# Deploy to preview channel
npm run deploy:preview

# Deploy to production
npm run deploy
```

## Development Guidelines

### Environment Variables

The application uses environment variables for configuration, especially for Firebase. These are loaded through Vite's built-in environment variable support.

In the code, environment variables are accessed using:

```typescript
import.meta.env.VITE_FIREBASE_API_KEY
```

Make sure to create a `.env` file based on the `.env.example` template before starting development.

### Code Style

- Use TypeScript for type safety
- Follow React best practices with functional components and hooks
- Use Tailwind CSS for styling
- Implement responsive design for all components
- Write meaningful comments for complex logic

## Contributing

[Add contribution guidelines here]

## License

[Add license information here]
