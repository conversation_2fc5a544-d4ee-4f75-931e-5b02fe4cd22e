/**
 * Bundle Analysis Script
 * 
 * This script analyzes the bundle sizes of the TradeYa application.
 * It uses the webpack-bundle-analyzer package to generate a visual report.
 * 
 * Usage:
 * 1. Run a production build: npm run build
 * 2. Run this script: node src/scripts/analyzeBundles.js
 */

const fs = require('fs');
const path = require('path');
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
const { formatBytes } = require('../utils/bundleAnalyzer');

// Path to the stats.json file generated by webpack
const statsFilePath = path.resolve(__dirname, '../../dist/stats.json');

// Function to analyze the bundle
async function analyzeBundles() {
  try {
    // Check if stats.json exists
    if (!fs.existsSync(statsFilePath)) {
      console.error('Stats file not found. Please run a production build with stats enabled first.');
      console.error('Example: npm run build -- --stats');
      return;
    }

    // Read the stats file
    const stats = JSON.parse(fs.readFileSync(statsFilePath, 'utf8'));

    // Generate a report
    console.log('Generating bundle analysis report...');
    
    // Use webpack-bundle-analyzer to generate a visual report
    const analyzer = new BundleAnalyzerPlugin({
      analyzerMode: 'static',
      reportFilename: path.resolve(__dirname, '../../dist/report.html'),
      openAnalyzer: true,
      generateStatsFile: false,
      statsFilename: 'stats.json',
      statsOptions: null,
      logLevel: 'info'
    });

    // Generate a simple text report
    console.log('\nBundle Size Summary:');
    console.log('===================\n');

    // Extract asset information
    const assets = stats.assets || [];
    let totalSize = 0;

    // Sort assets by size (largest first)
    assets.sort((a, b) => b.size - a.size);

    // Print asset information
    console.log('Assets:');
    assets.forEach(asset => {
      console.log(`- ${asset.name}: ${formatBytes(asset.size)}`);
      totalSize += asset.size;
    });

    console.log(`\nTotal bundle size: ${formatBytes(totalSize)}`);

    // Generate a markdown report
    const reportPath = path.resolve(__dirname, '../../src/components/ui/BUNDLE_ANALYSIS_REPORT.md');
    let report = '# Bundle Analysis Report\n\n';
    report += `Generated on: ${new Date().toLocaleString()}\n\n`;
    report += `Total bundle size: ${formatBytes(totalSize)}\n\n`;
    
    report += '## Assets\n\n';
    report += '| Asset | Size |\n';
    report += '|-------|------|\n';
    assets.forEach(asset => {
      report += `| ${asset.name} | ${formatBytes(asset.size)} |\n`;
    });

    // Write the report to a file
    fs.writeFileSync(reportPath, report);
    console.log(`\nReport saved to ${reportPath}`);
    
    console.log('\nDone!');
  } catch (error) {
    console.error('Error analyzing bundles:', error);
  }
}

// Run the analysis
analyzeBundles();
