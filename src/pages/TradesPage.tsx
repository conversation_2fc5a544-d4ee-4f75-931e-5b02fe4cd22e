import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '../AuthContext';
import { getAllTrades, Trade, getUserProfile, User } from '../services/firestore';
import PerformanceMonitor from '../components/ui/PerformanceMonitor';
import TradeCard from '../components/features/trades/TradeCard';
import AnimatedList from '../components/ui/AnimatedList';
import { useToast } from '../contexts/ToastContext';

// Additional interface for local state
// interface LocalTrade extends Trade {
//   // Any additional properties needed for UI state
// }

export const TradesPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [trades, setTrades] = useState<Trade[]>([]);
  const [tradeCreators, setTradeCreators] = useState<{[key: string]: User}>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');

  // Trades data will be fetched from Firestore

  // Fetch trades from Firestore
  useEffect(() => {
    const fetchTrades = async () => {
      setLoading(true);
      setError(null);

      try {
        const { data, error } = await getAllTrades(categoryFilter, 20);

        if (error) throw new Error(error.message);
        if (data) {
          const tradesData = data as Trade[];
          setTrades(tradesData);

          // Fetch user profiles for trade creators
          const uniqueUserIds = [...new Set(tradesData.map(trade => trade.creatorId).filter(Boolean) as string[])];
          fetchTradeCreators(uniqueUserIds);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch trades');
      } finally {
        setLoading(false);
      }
    };

    fetchTrades();
  }, [categoryFilter]);

  // Fetch trade creators' profiles
  const fetchTradeCreators = useCallback(async (userIds: string[]) => {
    try {
      const creators: {[key: string]: User} = {};

      // Fetch each user profile
      for (const userId of userIds) {
        const { data, error } = await getUserProfile(userId);

        if (!error && data) {
          creators[userId] = data as User;
        }
      }

      setTradeCreators(creators);
    } catch (err) {
      console.error('Error fetching trade creators:', err);
    }
  }, [setTradeCreators]);

  // Format date
  const formatDate = useCallback((date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return 'Today';
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else {
      return `${diffDays} days ago`;
    }
  }, []);

  // Filter trades based on search term and category
  const filteredTrades = useMemo(() => trades.filter(trade => {
    // Search in title and description
    let matchesSearch = searchTerm === '' ||
      (trade.title && trade.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (trade.description && trade.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Search in offered skills
    if (!matchesSearch && trade.offeredSkills && trade.offeredSkills.length > 0) {
      matchesSearch = trade.offeredSkills.some(skill =>
        skill.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Search in requested skills
    if (!matchesSearch && trade.requestedSkills && trade.requestedSkills.length > 0) {
      matchesSearch = trade.requestedSkills.some(skill =>
        skill.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Fallback to legacy fields
    if (!matchesSearch && trade.offering) {
      matchesSearch = trade.offering.toLowerCase().includes(searchTerm.toLowerCase());
    }

    if (!matchesSearch && trade.seeking) {
      matchesSearch = trade.seeking.toLowerCase().includes(searchTerm.toLowerCase());
    }

    const matchesCategory = categoryFilter === '' || (trade.category && trade.category === categoryFilter);

    return matchesSearch && matchesCategory;
  }), [trades, searchTerm, categoryFilter]);

  // Render trade creator information
  const renderTradeCreator = (trade: Trade) => {
    const creator = tradeCreators[trade.creatorId];
    const creatorName = creator ? creator.displayName : trade.userName || 'Unknown User';
    const creatorLocation = creator ? creator.location : 'Location Not Specified';
    const creatorPhoto = creator?.bestProfilePicture || `https://ui-avatars.com/api/?name=${encodeURIComponent(creatorName)}&background=random`;
    const creatorProfileId = creator?.id;

    return {
      uid: creatorProfileId || '',
      id: creatorProfileId || '',
      displayName: creatorName,
      photoURL: creatorPhoto,
      location: creatorLocation,
    } as User;
  };

  return (
    <>
      {/* Performance monitoring (invisible) */}
      <PerformanceMonitor pageName="TradesPage" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Trades</h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 mt-2">
              Browse available trades or create your own.
            </p>
          </div>

        {currentUser && !showCreateForm && (
          <button
            onClick={() => setShowCreateForm(true)}
            className="ml-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:bg-orange-700 dark:hover:bg-orange-800 dark:focus:ring-offset-gray-800"
          >
            Create Trade
          </button>
        )}
      </div>

      {/* Create Trade Form */}
      {showCreateForm && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Create a New Trade</h2>
            <button
              onClick={() => setShowCreateForm(false)}
              className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Form content */}
          {/* ... (keep the existing form content) */}
        </div>
      )}

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Search
            </label>
            <input
              id="search"
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder="Search by title, description, skills..."
            />
          </div>

          <div>
            <label htmlFor="categoryFilter" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Category
            </label>
            <select
              id="categoryFilter"
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            >
              <option value="">All Categories</option>
              {/* {categories.map((cat) => (
                <option key={cat} value={cat}>{cat}</option>
              ))} */}
            </select>
          </div>
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}

      {/* Trades List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          <div className="col-span-full flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700 border-t-orange-500"></div>
          </div>
        ) : filteredTrades.length === 0 ? (
          <div className="col-span-full bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 text-center">
            <p className="text-gray-500 dark:text-gray-400">No trades found matching your criteria.</p>
          </div>
        ) : (
          <AnimatedList
            className="col-span-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            animation="slideUp"
            staggerDelay={0.05}
            initialDelay={0.1}
            duration={0.4}
          >
            {filteredTrades.map(trade => (
              <TradeCard
                key={trade.id}
                trade={trade}
                tradeCreator={renderTradeCreator(trade)}
                formatDate={formatDate}
              />
            ))}
          </AnimatedList>
        )}
      </div>
    </div>
    </>
  );
};

export default TradesPage;
