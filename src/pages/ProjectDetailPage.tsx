import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../AuthContext';
import {
  getProject,
  deleteProject,
  getProjectApplications,
  updateProjectApplication,
  Project,
  ProjectApplication
} from '../services/firestore';
import ProjectForm from '../components/features/projects/ProjectForm';
import ProjectApplicationForm from '../components/features/projects/ProjectApplicationForm';
import ProjectApplicationCard from '../components/features/projects/ProjectApplicationCard';
import { useToast } from '../contexts/ToastContext';
import { Modal } from '../components/ui/Modal';
import { ArrowLeft, Edit, Trash2, MapPin, Clock, DollarSign, Users } from 'lucide-react';
import ProfileHoverCard from '../components/ui/ProfileHoverCard';

export const ProjectDetailPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const { currentUser } = useAuth();
  const { addToast } = useToast();
  const navigate = useNavigate();

  const [project, setProject] = useState<Project | undefined>(undefined);
  const [applications, setApplications] = useState<ProjectApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isOwner, setIsOwner] = useState(false);

  const [isEditing, setIsEditing] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [hasApplied, setHasApplied] = useState(false);
  const [activeTab, setActiveTab] = useState<'details' | 'applications'>('details');

  // Check if current user is a collaborator
  const isCollaborator = currentUser && project && project.collaborators?.includes(currentUser.uid);

  useEffect(() => {
    const fetchProjectData = async () => {
      if (!projectId) {
        setError('Project ID is missing');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      const { data: projectData, error: projectError } = await getProject(projectId);
      if (projectError) {
        setError(`Failed to fetch project: ${projectError.message}`);
      } else if (projectData) {
        setProject(projectData);
        setIsOwner(currentUser?.uid === projectData.ownerId);

        // Fetch applications only if project is loaded
        const { data: applicationsData, error: applicationsError } = await getProjectApplications(projectId);
        if (applicationsError) {
          setError(`Failed to fetch applications: ${applicationsError.message}`);
        } else if (applicationsData) {
          setApplications(applicationsData);
          const userApplication = applicationsData.find(app => app.applicantId === currentUser?.uid);
          setHasApplied(!!userApplication);
        } else {
          // Handle case where projectData is null but no explicit error
          setError('Project not found');
        }
      }

      setIsLoading(false);
    };

    fetchProjectData();
  }, [projectId, currentUser]);

  const handleUpdateProject = async () => {
    setIsEditing(false);
    addToast('success', 'Project updated successfully');
  };

  const handleDeleteProject = async () => {
    if (!projectId) return;

    try {
      const { error: deleteError } = await deleteProject(projectId);

      if (deleteError) {
        throw new Error(deleteError.message || 'Failed to delete project');
      }

      addToast('success', 'Project deleted successfully');
      navigate('/projects');
    } catch (err: any) {
      addToast('error', err.message || 'Failed to delete project');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleApplicationSubmit = () => {
    setShowApplicationForm(false);
    setHasApplied(true);
  };

  const handleAcceptApplication = async (applicationId: string) => {
    const { error } = await updateProjectApplication(applicationId, { status: 'accepted' });
    if (error) {
      setError(`Failed to accept application: ${error.message}`);
      return;
    }

    addToast('success', 'Application accepted');
    setApplications(prevApps =>
      prevApps.map(app =>
        app.id === applicationId ? { ...app, status: 'accepted' } : app
      )
    );
    if (project && project.status !== 'in-progress') {
      setProject(prevProject => prevProject ? { ...prevProject, status: 'in-progress' } : undefined);
    }
  };

  const handleRejectApplication = async (applicationId: string) => {
    const { error } = await updateProjectApplication(applicationId, { status: 'rejected' });
    if (error) {
      setError(`Failed to reject application: ${error.message}`);
      return;
    }

    addToast('success', 'Application rejected');
    setApplications(prevApps =>
      prevApps.map(app =>
        app.id === applicationId ? { ...app, status: 'rejected' } : app
      )
    );
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Error</h2>
          <p className="text-gray-600 mb-6">
            {error || 'Project not found'}
          </p>
          <Link
            to="/projects"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            Back to Projects
          </Link>
        </div>
      </div>
    );
  }

  // Format date
  const formattedDate = project.createdAt && project.createdAt.seconds
    ? new Date(project.createdAt.seconds * 1000).toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      })
    : 'Recently posted';

  if (isEditing) {
    return (
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="mb-6">
          <button
            onClick={() => setIsEditing(false)}
            className="inline-flex items-center text-sm font-medium text-orange-600 hover:text-orange-500"
          >
            <ArrowLeft className="mr-1 h-4 w-4" />
            Back to Project
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Edit Project</h1>

          <ProjectForm
            project={project}
            onSuccess={handleUpdateProject}
            onCancel={() => setIsEditing(false)}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="mb-6">
        <Link
          to="/projects"
          className="inline-flex items-center text-sm font-medium text-orange-600 hover:text-orange-500"
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
          Back to Projects
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Project Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col md:flex-row md:items-start md:justify-between">
            <div>
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-gray-900">{project.title}</h1>
                <span className={`ml-3 inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium ${
                  project.status === 'open' ? 'bg-green-100 text-green-800 border-green-200' :
                  project.status === 'in-progress' ? 'bg-blue-100 text-blue-800 border-blue-200' :
                  project.status === 'completed' ? 'bg-gray-100 text-gray-800 border-gray-200' :
                  'bg-red-100 text-red-800 border-red-200'
                }`}>
                  {project.status === 'open' ? 'Open' :
                   project.status === 'in-progress' ? 'In Progress' :
                   project.status === 'completed' ? 'Completed' :
                   'Cancelled'}
                </span>
              </div>

              <div className="mt-2 flex items-center">
                <ProfileHoverCard
                  userId={project.ownerId}
                  displayName={project.ownerName}
                  photoURL={project.ownerPhotoURL}
                >
                  <div className="flex items-center">
                    <img
                      src={project.ownerPhotoURL || `https://ui-avatars.com/api/?name=${encodeURIComponent(project.ownerName)}&background=random`}
                      alt={project.ownerName}
                      className="h-6 w-6 rounded-full mr-2"
                    />
                    <span className="text-sm text-gray-600 hover:text-orange-600">
                      {project.ownerName}
                    </span>
                  </div>
                </ProfileHoverCard>
                <span className="mx-2 text-gray-300">•</span>
                <span className="text-sm text-gray-500">Posted {formattedDate}</span>
              </div>
            </div>

            {isOwner && (
              <div className="mt-4 md:mt-0 flex space-x-3">
                <button
                  onClick={() => setIsEditing(true)}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                >
                  <Edit className="mr-1.5 h-4 w-4" />
                  Edit
                </button>

                <button
                  onClick={() => setIsDeleting(true)}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                >
                  <Trash2 className="mr-1.5 h-4 w-4" />
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('details')}
              className={`w-1/2 py-4 px-1 text-center border-b-2 font-medium text-sm ${
                activeTab === 'details'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Project Details
            </button>

            {(isOwner || isCollaborator) && (
              <button
                onClick={() => setActiveTab('applications')}
                className={`w-1/2 py-4 px-1 text-center border-b-2 font-medium text-sm ${
                  activeTab === 'applications'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Applications {applications.length > 0 && `(${applications.length})`}
              </button>
            )}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'details' ? (
            <div className="space-y-6">
              {/* Project Images */}
              {project.images && Array.isArray(project.images) && project.images.length > 0 && (
                <div className="mb-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <img
                        src={project.images[0]}
                        alt={project.title}
                        className="w-full h-64 object-cover rounded-lg"
                      />
                    </div>

                    {project.images.slice(1).map((image, index) => (
                      <img
                        key={index}
                        src={image}
                        alt={`${project.title} ${index + 2}`}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Project Details */}
              <div>
                <h2 className="text-lg font-medium text-gray-900 mb-4">Description</h2>
                <p className="text-gray-600 whitespace-pre-line">
                  {project.description}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Category</h3>
                  <p className="text-gray-900">{project.category}</p>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Location</h3>
                  <div className="flex items-center">
                    <MapPin className="mr-1.5 h-4 w-4 text-gray-400" />
                    <p className="text-gray-900">
                      {project.isRemote ? 'Remote' : (project.location || 'Not specified')}
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Timeline</h3>
                  <div className="flex items-center">
                    <Clock className="mr-1.5 h-4 w-4 text-gray-400" />
                    <p className="text-gray-900">{project.timeline}</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Compensation</h3>
                  <div className="flex items-center">
                    <DollarSign className="mr-1.5 h-4 w-4 text-gray-400" />
                    <p className="text-gray-900">{project.compensation}</p>
                  </div>
                </div>
              </div>

              {project.skillsNeeded && project.skillsNeeded.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Skills Needed</h3>
                  <div className="flex flex-wrap gap-2">
                    {project.skillsNeeded.map((skill, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center rounded-full bg-orange-100 px-2.5 py-0.5 text-xs font-medium text-orange-800"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {project.collaborators && project.collaborators.length > 1 && (
                <div>
                  <h3 className="text-sm font-medium text-gray-500 mb-2">Collaborators</h3>
                  <div className="flex items-center">
                    <Users className="mr-1.5 h-4 w-4 text-gray-400" />
                    <p className="text-gray-900">{project.collaborators.length} collaborators</p>
                  </div>
                </div>
              )}

              {/* Apply Button */}
              {currentUser && !isOwner && project.status === 'open' && (
                <div className="mt-8 flex justify-center">
                  {hasApplied ? (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                      <p className="text-gray-600">
                        You have already applied to this project. The project owner will review your application.
                      </p>
                    </div>
                  ) : (
                    <button
                      onClick={() => setShowApplicationForm(true)}
                      className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                    >
                      Apply for this Project
                    </button>
                  )}
                </div>
              )}

              {!currentUser && (
                <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-4 text-center">
                  <p className="text-gray-600 mb-3">
                    You need to be signed in to apply for this project.
                  </p>
                  <Link
                    to="/login"
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                  >
                    Sign In
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Applications</h2>

              {applications.length > 0 ? (
                <div className="space-y-4">
                  {applications.map((application) => (
                    <ProjectApplicationCard
                      key={application.id}
                      application={application}
                      isOwner={isOwner || false}
                      onAccept={handleAcceptApplication}
                      onReject={handleRejectApplication}
                    />
                  ))}
                </div>
              ) : (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
                  <p className="text-gray-600">
                    No applications yet.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Application Form Modal */}
      <Modal
        isOpen={showApplicationForm}
        onClose={() => setShowApplicationForm(false)}
        title="Apply for Project"
      >
        <ProjectApplicationForm
          project={project}
          onSuccess={handleApplicationSubmit}
          onCancel={() => setShowApplicationForm(false)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleting}
        onClose={() => setIsDeleting(false)}
        title="Delete Project"
      >
        <div className="p-4">
          <p className="text-gray-600 mb-6">
            Are you sure you want to delete this project? This action cannot be undone.
          </p>

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setIsDeleting(false)}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Cancel
            </button>

            <button
              onClick={handleDeleteProject}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Delete
            </button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ProjectDetailPage;
