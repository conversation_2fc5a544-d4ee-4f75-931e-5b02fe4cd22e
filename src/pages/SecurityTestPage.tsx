import React, { useEffect, useState } from 'react';
import { useAuth } from '../AuthContext';
import { db } from '../firebase-config';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';

export const SecurityTestPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!currentUser) return;

    setLoading(true);
    setError(null);

    const runTests = async () => {
      const testResults: any[] = [];
      
      try {
        // Test 1: Access conversations collection
        try {
          const conversationsRef = collection(db, 'conversations');
          const conversationsSnap = await getDocs(conversationsRef);
          testResults.push({
            name: 'Access conversations collection',
            result: 'Success',
            details: `Found ${conversationsSnap.size} conversations`
          });
        } catch (err: any) {
          testResults.push({
            name: 'Access conversations collection',
            result: 'Failed',
            details: err.message
          });
        }

        // Test 2: Access specific conversation
        const conversationId = 'bcB1UuJ2VHwTXsTFG71g';
        try {
          const conversationRef = doc(db, 'conversations', conversationId);
          const conversationSnap = await getDoc(conversationRef);
          testResults.push({
            name: `Access specific conversation (${conversationId})`,
            result: conversationSnap.exists() ? 'Success' : 'Not Found',
            details: conversationSnap.exists() 
              ? `Conversation exists with data: ${JSON.stringify(conversationSnap.data())}`
              : 'Conversation document does not exist'
          });
        } catch (err: any) {
          testResults.push({
            name: `Access specific conversation (${conversationId})`,
            result: 'Failed',
            details: err.message
          });
        }

        // Test 3: Access messages subcollection
        try {
          const messagesRef = collection(db, 'conversations', conversationId, 'messages');
          const messagesSnap = await getDocs(messagesRef);
          testResults.push({
            name: `Access messages subcollection (${conversationId}/messages)`,
            result: 'Success',
            details: `Found ${messagesSnap.size} messages`
          });
        } catch (err: any) {
          testResults.push({
            name: `Access messages subcollection (${conversationId}/messages)`,
            result: 'Failed',
            details: err.message
          });
        }

        // Test 4: Access flat messages collection
        try {
          const flatMessagesRef = collection(db, 'messages');
          const flatMessagesSnap = await getDocs(flatMessagesRef);
          testResults.push({
            name: 'Access flat messages collection',
            result: 'Success',
            details: `Found ${flatMessagesSnap.size} messages`
          });
        } catch (err: any) {
          testResults.push({
            name: 'Access flat messages collection',
            result: 'Failed',
            details: err.message
          });
        }

        setResults(testResults);
      } catch (err: any) {
        setError(err.message || 'An error occurred during testing');
      } finally {
        setLoading(false);
      }
    };

    runTests();
  }, [currentUser, db]);

  if (!currentUser) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">Security Test</h1>
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
          <p className="text-yellow-700">Please log in to run security tests.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <h1 className="text-3xl font-bold text-gray-900 mb-6">Firebase Security Test</h1>
      <p className="mb-4">Testing access to various collections and documents</p>
      
      {loading ? (
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-4 border-gray-200 border-t-orange-500"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h2 className="text-lg font-medium text-gray-900">Test Results</h2>
            <p className="mt-1 text-sm text-gray-500">Ran {results.length} security tests</p>
          </div>
          <div className="border-t border-gray-200">
            <ul className="divide-y divide-gray-200">
              {results.map((test, index) => (
                <li key={index} className="px-4 py-4">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900">
                      {test.name}
                    </p>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      test.result === 'Success' 
                        ? 'bg-green-100 text-green-800' 
                        : test.result === 'Not Found'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                    }`}>
                      {test.result}
                    </span>
                  </div>
                  <p className="mt-2 text-sm text-gray-500 whitespace-pre-wrap">
                    {test.details}
                  </p>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default SecurityTestPage;
