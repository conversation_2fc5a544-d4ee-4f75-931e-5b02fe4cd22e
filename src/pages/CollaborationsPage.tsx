import React, { useState, useEffect } from 'react';
import { getCollaborations, Collaboration, getUserProfile, User } from '../services/firestore';
import { useToast } from '../contexts/ToastContext';
import { PlusCircle } from '../utils/icons';
import { themeClasses } from '../utils/themeUtils';
import ProfileImageWithUser from '../components/ui/ProfileImageWithUser';
import PerformanceMonitor from '../components/ui/PerformanceMonitor';
import { Link, useNavigate } from 'react-router-dom';
import { cn } from '../utils/cn';
import { motion } from 'framer-motion';

export const CollaborationsPage: React.FC = () => {
  const { addToast } = useToast();
  const navigate = useNavigate();

  const [collaborations, setCollaborations] = useState<Collaboration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [collaborationCreators, setCollaborationCreators] = useState<{[key: string]: User}>({});

  useEffect(() => {
    fetchCollaborations();
  }, []);

  const fetchCollaborationCreators = async (creatorIds: string[]) => {
    try {
      const creators: {[key: string]: User} = {};

      for (const creatorId of creatorIds) {
        if (creatorId) {
          const { data, error } = await getUserProfile(creatorId);
          if (!error && data) {
            creators[creatorId] = data as User;
          }
        }
      }

      setCollaborationCreators(creators);
    } catch (err) {
      console.error('Error fetching collaboration creators:', err);
    }
  };

  const fetchCollaborations = async () => {
    setLoading(true);
    setError(null);

    try {
      const collaborationsResult = await getCollaborations();
      if (collaborationsResult.error) throw new Error(collaborationsResult.error.message);
      if (collaborationsResult.data) {
        console.log('Fetched collaborations:', collaborationsResult.data);
        console.log('Number of collaborations:', collaborationsResult.data.length);
        setCollaborations(collaborationsResult.data);

        const creatorIds = collaborationsResult.data
          .map((collab: Collaboration) => collab.creatorId)
          .filter((id: string | undefined, index: number, self: (string | undefined)[]) => id && self.indexOf(id) === index);

        if (creatorIds.length > 0) {
          fetchCollaborationCreators(creatorIds as string[]);
        }
      } else {
        setCollaborations([]);
        console.log('No collaborations found');
      }
    } catch (err: any) {
      console.error('Error fetching collaborations:', err);
      setError(err.message || 'Failed to fetch collaborations');
      addToast('error', err.message || 'Failed to fetch collaborations');
    } finally {
      setLoading(false);
    }
  };



  return (
    <>
      {/* Performance monitoring (invisible) */}
      <PerformanceMonitor pageName="CollaborationsPage" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className={`text-3xl font-bold ${themeClasses.text}`}>Collaborations</h1>
            <p className={`mt-1 text-sm ${themeClasses.textMuted}`}>
              Find or create collaborative projects with other creative professionals
            </p>
          </div>

        <div className="mt-4 md:mt-0 flex space-x-3">
          <button
            onClick={() => navigate('/collaborations/new')}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 dark:bg-orange-700 dark:hover:bg-orange-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Collaboration
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 dark:border-orange-400"></div>
        </div>
      ) : collaborations.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {collaborations.map((collab) => (
            <motion.div
              key={collab.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="backdrop-blur-md bg-white/70 dark:bg-gray-800/60 border border-white/20 dark:border-gray-700/30 rounded-xl shadow-lg p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1 hover:bg-white/80 dark:hover:bg-gray-800/70 dark:hover:shadow-[0_0_15px_rgba(251,146,60,0.2)]"
            >
              <div className="flex justify-between items-start mb-3">
                <h3 className={`text-lg font-semibold ${themeClasses.heading3} line-clamp-2`}>{collab.title}</h3>
                <span className="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/30 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:text-blue-300 transition-colors duration-200 ml-2 flex-shrink-0">
                  {collab.status || 'Active'}
                </span>
              </div>

              {collab.creatorId && collab.creatorName && (
                <div className="mb-3 flex items-center">
                  {collab.creatorId === 'TozfQg0dAHe4ToLyiSnkDqe3ECj2' ? (
                    <img
                      src="https://res.cloudinary.com/doqqhj2nt/image/upload/c_fill,g_face,h_400,w_400,q_auto:best,f_auto/v1737789591/profile-pictures/TozfQg0dAHe4ToLyiSnkDqe3ECj2_47251d4b-f5a6-42b3-a7de-dcdeb2f66543.jpg"
                      alt="John Roberts"
                      className="h-7 w-7 rounded-full mr-3 ring-2 ring-white/20 dark:ring-gray-700/30"
                    />
                  ) : (
                    <ProfileImageWithUser
                      userId={collab.creatorId}
                      profileUrl={collaborationCreators[collab.creatorId]?.profilePicture || collaborationCreators[collab.creatorId]?.photoURL}
                      size="sm"
                      className="h-7 w-7 rounded-full mr-3 ring-2 ring-white/20 dark:ring-gray-700/30"
                    />
                  )}
                  <span className={`text-sm font-medium ${themeClasses.text}`}>
                    {collaborationCreators[collab.creatorId]?.displayName || collab.creatorName || 'Unknown User'}
                  </span>
                </div>
              )}

              <p className={`text-sm ${themeClasses.textMuted} line-clamp-3 mb-4 leading-relaxed`}>{collab.description}</p>

              <div className="mb-4">
                <h4 className={`text-sm font-medium ${themeClasses.text} mb-3`}>Roles Available:</h4>
                <div className="flex flex-wrap gap-2">
                  {collab.roles && collab.roles.length > 0 ? collab.roles.map((role, index) => (
                    <span
                      key={index}
                      className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-medium backdrop-blur-sm border transition-all duration-200 ${
                        role.filled
                          ? 'bg-green-100/80 dark:bg-green-900/40 text-green-800 dark:text-green-300 border-green-200/50 dark:border-green-800/50'
                          : 'bg-amber-100/80 dark:bg-amber-900/40 text-amber-800 dark:text-amber-300 border-amber-200/50 dark:border-amber-800/50'
                      }`}
                    >
                      {role.title} {role.filled ? '✓' : '○'}
                    </span>
                  )) : (
                    <span className={`text-xs ${themeClasses.textMuted} italic`}>No roles defined</span>
                  )}
                </div>
              </div>

              <div className="flex justify-between items-center mb-4 pt-3 border-t border-white/10 dark:border-gray-700/30">
                <span className={`text-xs ${themeClasses.textMuted} flex items-center`}>
                  <svg className="w-3 h-3 mr-1 opacity-60" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                  </svg>
                  {collab.createdAt && collab.createdAt.seconds ?
                    new Date(collab.createdAt.seconds * 1000).toLocaleDateString() :
                    'Recently added'}
                </span>
                <div className="flex items-center space-x-2">
                  {collab.roles && (
                    <span className={`text-xs ${themeClasses.textMuted} flex items-center`}>
                      <svg className="w-3 h-3 mr-1 opacity-60" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {collab.roles.filter(r => r.filled).length}/{collab.roles.length} filled
                    </span>
                  )}
                </div>
              </div>

              <Link
                to={`/collaborations/${collab.id}`}
                className="group w-full inline-flex justify-center items-center px-4 py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 dark:from-orange-600 dark:to-orange-700 dark:hover:from-orange-700 dark:hover:to-orange-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-[1.02]"
              >
                <span>View Collaboration</span>
                <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </motion.div>
          ))}
        </div>
      ) : (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="backdrop-blur-md bg-white/70 dark:bg-gray-800/60 border border-white/20 dark:border-gray-700/30 rounded-xl shadow-lg p-12 text-center"
        >
          <div className="mx-auto w-16 h-16 mb-6 rounded-full bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/30 flex items-center justify-center">
            <svg className="w-8 h-8 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </div>
          <h3 className={`text-xl font-semibold ${themeClasses.heading3} mb-3`}>No collaborations yet</h3>
          <p className={`${themeClasses.textMuted} mb-8 max-w-md mx-auto leading-relaxed`}>
            Be the first to create a collaborative project! Connect with other creative professionals and bring your ideas to life together.
          </p>
          <button
            onClick={() => navigate('/collaborations/new')}
            className="inline-flex items-center px-6 py-3 rounded-lg text-sm font-medium text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 dark:from-orange-600 dark:to-orange-700 dark:hover:from-orange-700 dark:hover:to-orange-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
          >
            <PlusCircle className="mr-2 h-5 w-5" />
            Create Your First Collaboration
          </button>
        </motion.div>
      )}
    </div>
    </>
  );
};

export default CollaborationsPage;
