import React, { useState } from 'react';
import { Card, CardHeader, CardBody, CardFooter } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { Avatar } from '../components/ui/Avatar';
import { ThemeToggle } from '../components/ui/ThemeToggle';
import { Modal } from '../components/ui/Modal';
import { SimpleModal } from '../components/ui/SimpleModal';
import { Toast } from '../components/ui/Toast';
import { Tooltip } from '../components/ui/Tooltip';
import { EmptyState } from '../components/ui/EmptyState';
import { SkeletonText, SkeletonCircle, SkeletonButton } from '../components/ui/skeletons/Skeleton';
import { CardSkeleton } from '../components/ui/skeletons/CardSkeleton';
import { Transition } from '../components/ui/transitions/Transition';
import SkillSelector from '../components/ui/SkillSelector';
import { themeClasses } from '../utils/themeUtils';
import { cn } from '../utils/cn';

const ComponentTestPage: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSimpleModalOpen, setIsSimpleModalOpen] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastType, setToastType] = useState<'success' | 'error' | 'warning' | 'info'>('success');
  // Initialize to false so we can see the entering transition when first clicking the button
  const [showTransition, setShowTransition] = useState(false);
  const [skills, setSkills] = useState<Array<{ name: string; level: 'beginner' | 'intermediate' | 'advanced' | 'expert' }>>([]);

  const handleShowToast = (type: 'success' | 'error' | 'warning' | 'info') => {
    setToastType(type);
    setShowToast(true);
  };

  return (
    <div className="min-h-screen p-4 md:p-8 bg-neutral-50 dark:bg-neutral-900">
      <div className="max-w-7xl mx-auto space-y-12">
        <div className="flex justify-between items-center">
          <h1 className={cn(themeClasses.heading1, "mb-8")}>Component Test Page</h1>
          <ThemeToggle />
        </div>

        {/* Theme Toggle */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Theme Toggle</h2>
          <div className="flex items-center space-x-4">
            <ThemeToggle />
          </div>
        </section>

        {/* Buttons */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Buttons</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <h3 className={themeClasses.heading4}>Variants</h3>
              <div className="flex flex-wrap gap-2">
                <Button variant="primary">Primary</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="tertiary">Tertiary</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="danger">Danger</Button>
                <Button variant="success">Success</Button>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className={themeClasses.heading4}>Sizes</h3>
              <div className="flex flex-wrap items-center gap-2">
                <Button size="xs">Extra Small</Button>
                <Button size="sm">Small</Button>
                <Button size="md">Medium</Button>
                <Button size="lg">Large</Button>
                <Button size="xl">Extra Large</Button>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className={themeClasses.heading4}>States</h3>
              <div className="flex flex-wrap gap-2">
                <Button isLoading>Loading</Button>
                <Button disabled>Disabled</Button>
                <Button rounded>Rounded</Button>
                <Button fullWidth>Full Width</Button>
              </div>
            </div>
          </div>
        </section>

        {/* Cards */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Cards</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardBody>
                <p className={themeClasses.body}>Basic Card</p>
              </CardBody>
            </Card>

            <Card variant="elevated">
              <CardHeader>
                <h3 className={themeClasses.heading4}>Card with Header</h3>
              </CardHeader>
              <CardBody>
                <p className={themeClasses.body}>This card has a header and body.</p>
              </CardBody>
            </Card>

            <Card variant="outlined">
              <CardHeader>
                <h3 className={themeClasses.heading4}>Outlined Card</h3>
              </CardHeader>
              <CardBody>
                <p className={themeClasses.body}>This card has an outlined variant.</p>
              </CardBody>
              <CardFooter>
                <div className="flex justify-end">
                  <Button variant="primary" size="sm">Action</Button>
                </div>
              </CardFooter>
            </Card>

            <Card variant="filled">
              <CardHeader>
                <h3 className={themeClasses.heading4}>Filled Card</h3>
              </CardHeader>
              <CardBody>
                <p className={themeClasses.body}>This card has a filled variant.</p>
              </CardBody>
              <CardFooter>
                <div className="flex justify-end">
                  <Button variant="primary" size="sm">Action</Button>
                </div>
              </CardFooter>
            </Card>

            <Card hover interactive>
              <CardBody>
                <p className={themeClasses.body}>Hover and Interactive Card</p>
                <p className={themeClasses.bodySmall}>This card has hover and interactive effects.</p>
              </CardBody>
            </Card>
          </div>
        </section>

        {/* Inputs */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Inputs</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Basic Input"
              placeholder="Enter text here"
            />

            <Input
              label="With Helper Text"
              placeholder="Enter text here"
              helperText="This is some helper text"
            />

            <Input
              label="With Error"
              placeholder="Enter text here"
              error="This field is required"
            />

            <Input
              label="With Left Icon"
              placeholder="Search..."
              leftIcon={
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              }
            />

            <Input
              label="With Right Icon"
              placeholder="Enter text here"
              rightIcon={
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              }
            />

            <Input
              label="Disabled Input"
              placeholder="This input is disabled"
              disabled
            />

            <Input
              label="Full Width Input"
              placeholder="This input takes full width"
              fullWidth
            />
          </div>
        </section>

        {/* Avatars */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Avatars</h2>
          <div className="flex flex-wrap items-center gap-4">
            <Avatar alt="User" size="xs" />
            <Avatar alt="User" size="sm" />
            <Avatar alt="User" size="md" />
            <Avatar alt="User" size="lg" />
            <Avatar alt="User" size="xl" />
            <Avatar src="https://i.pravatar.cc/300" alt="User with image" size="lg" />
          </div>
        </section>

        {/* Modal */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Modals</h2>
          <div className="flex flex-wrap gap-4">
            <div>
              <h3 className={themeClasses.heading4}>Regular Modal</h3>
              <div className="flex flex-wrap gap-2 mt-2">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={() => {
                    console.log('Opening modal, current state:', !isModalOpen);
                    setIsModalOpen(true);
                  }}
                >
                  Open Modal
                </Button>
              </div>
              <div className="p-4 bg-neutral-100 dark:bg-neutral-800 rounded-lg mt-2">
                <p className={themeClasses.bodySmall}>
                  Modal state: {isModalOpen ? 'OPEN' : 'CLOSED'}
                </p>
              </div>
              <Modal
                isOpen={isModalOpen}
                onClose={() => {
                  console.log('Closing modal');
                  setIsModalOpen(false);
                }}
                title="Modal Title"
                size="md"
                footer={
                  <div className="flex justify-end space-x-2">
                    <Button variant="tertiary" onClick={() => setIsModalOpen(false)}>Cancel</Button>
                    <Button variant="primary" onClick={() => setIsModalOpen(false)}>Confirm</Button>
                  </div>
                }
              >
                <div className="space-y-4">
                  <p className={themeClasses.body}>
                    This is the content of the modal. You can put any React components here.
                  </p>
                  <div className="p-4 bg-neutral-100 dark:bg-neutral-800 rounded-lg">
                    <p className={themeClasses.bodySmall}>
                      The modal has the following features:
                    </p>
                    <ul className="list-disc pl-5 mt-2 space-y-1">
                      <li className={themeClasses.bodySmall}>Closes when you click outside</li>
                      <li className={themeClasses.bodySmall}>Closes when you press ESC key</li>
                      <li className={themeClasses.bodySmall}>Prevents scrolling of the background</li>
                    </ul>
                  </div>
                </div>
              </Modal>
            </div>

            <div>
              <h3 className={themeClasses.heading4}>Simple Modal</h3>
              <div className="flex flex-wrap gap-2 mt-2">
                <Button
                  variant="secondary"
                  size="lg"
                  onClick={() => {
                    console.log('Opening simple modal, current state:', !isSimpleModalOpen);
                    setIsSimpleModalOpen(true);
                  }}
                >
                  Open Simple Modal
                </Button>
              </div>
              <div className="p-4 bg-neutral-100 dark:bg-neutral-800 rounded-lg mt-2">
                <p className={themeClasses.bodySmall}>
                  Simple Modal state: {isSimpleModalOpen ? 'OPEN' : 'CLOSED'}
                </p>
              </div>
              <SimpleModal
                isOpen={isSimpleModalOpen}
                onClose={() => {
                  console.log('Closing simple modal');
                  setIsSimpleModalOpen(false);
                }}
                title="Simple Modal Title"
              >
                <p>This is a very simple modal implementation without any fancy features.</p>
                <div style={{ marginTop: '20px' }}>
                  <Button
                    variant="primary"
                    onClick={() => setIsSimpleModalOpen(false)}
                  >
                    Close
                  </Button>
                </div>
              </SimpleModal>
            </div>
          </div>
        </section>

        {/* Toast */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Toast</h2>
          <div className="flex flex-wrap gap-2">
            <Button onClick={() => handleShowToast('success')}>Success Toast</Button>
            <Button onClick={() => handleShowToast('error')}>Error Toast</Button>
            <Button onClick={() => handleShowToast('warning')}>Warning Toast</Button>
            <Button onClick={() => handleShowToast('info')}>Info Toast</Button>
          </div>
          {showToast && (
            <div className="fixed bottom-4 right-4 z-50">
              <Toast
                type={toastType}
                message={`This is a ${toastType} toast message`}
                onClose={() => setShowToast(false)}
              />
            </div>
          )}
        </section>

        {/* Tooltip */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Tooltip</h2>
          <div className="flex flex-wrap gap-4">
            <Tooltip content="This is a tooltip" position="top">
              <Button>Hover me (Top)</Button>
            </Tooltip>
            <Tooltip content="This is a tooltip" position="right">
              <Button>Hover me (Right)</Button>
            </Tooltip>
            <Tooltip content="This is a tooltip" position="bottom">
              <Button>Hover me (Bottom)</Button>
            </Tooltip>
            <Tooltip content="This is a tooltip" position="left">
              <Button>Hover me (Left)</Button>
            </Tooltip>
          </div>
        </section>

        {/* Empty State */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Empty State</h2>
          <Card>
            <CardBody>
              <EmptyState
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
                  </svg>
                }
                title="No messages found"
                description="You don't have any messages yet. Start a conversation to see messages here."
                actionLabel="Start Conversation"
                onAction={() => alert('Action clicked')}
              />
            </CardBody>
          </Card>
        </section>

        {/* Skeletons */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Skeletons</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className={themeClasses.heading4}>Basic Skeletons</h3>
              <div className="space-y-2">
                <SkeletonText />
                <SkeletonText className="w-3/4" />
                <SkeletonText className="w-1/2" />
              </div>
              <div className="flex items-center space-x-4">
                <SkeletonCircle />
                <div className="space-y-2 flex-1">
                  <SkeletonText className="h-5" />
                  <SkeletonText className="h-4 w-3/4" />
                </div>
              </div>
              <div className="flex space-x-2">
                <SkeletonButton />
                <SkeletonButton />
              </div>
            </div>
            <div>
              <h3 className={themeClasses.heading4}>Card Skeleton</h3>
              <CardSkeleton hasImage hasFooter />
            </div>
          </div>
        </section>

        {/* Transitions */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Transitions</h2>
          <div className="space-y-4">
            <div className="flex flex-wrap gap-2">
              <Button onClick={() => setShowTransition(!showTransition)}>
                {showTransition ? 'Hide' : 'Show'} Transition
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <h3 className={themeClasses.heading5}>Fade</h3>
                <Transition show={showTransition} type="fade" duration={600}>
                  <Card>
                    <CardBody>
                      <p className={themeClasses.body}>Fade Transition</p>
                    </CardBody>
                  </Card>
                </Transition>
              </div>
              <div className="space-y-2">
                <h3 className={themeClasses.heading5}>Slide</h3>
                <Transition show={showTransition} type="slide" duration={600}>
                  <Card>
                    <CardBody>
                      <p className={themeClasses.body}>Slide Transition</p>
                    </CardBody>
                  </Card>
                </Transition>
              </div>
              <div className="space-y-2">
                <h3 className={themeClasses.heading5}>Zoom</h3>
                <Transition show={showTransition} type="zoom" duration={600}>
                  <Card>
                    <CardBody>
                      <p className={themeClasses.body}>Zoom Transition</p>
                    </CardBody>
                  </Card>
                </Transition>
              </div>
              <div className="space-y-2">
                <h3 className={themeClasses.heading5}>Bounce</h3>
                <Transition show={showTransition} type="bounce" duration={600}>
                  <Card>
                    <CardBody>
                      <p className={themeClasses.body}>Bounce Transition</p>
                    </CardBody>
                  </Card>
                </Transition>
              </div>
            </div>
          </div>
        </section>

        {/* Skill Selector */}
        <section className="space-y-4">
          <h2 className={themeClasses.heading2}>Skill Selector</h2>
          <SkillSelector
            selectedSkills={skills}
            onChange={setSkills}
            maxSkills={5}
          />
        </section>
      </div>
    </div>
  );
};

export default ComponentTestPage;
