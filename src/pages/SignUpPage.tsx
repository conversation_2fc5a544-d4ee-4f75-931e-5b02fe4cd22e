import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../AuthContext';
import EnhancedInput from '../components/ui/EnhancedInput';
import { motion } from 'framer-motion';
import { Card } from '../components/ui/Card';
import { MailIcon, LockIcon, CheckIcon } from 'lucide-react';

export const SignUpPage: React.FC = () => {
  const { signUpWithEmail, signInWithGoogle, error, loading, clearError, currentUser } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [formError, setFormError] = useState<string | null>(null);
  const [registrationSuccess, setRegistrationSuccess] = useState(false);
  const navigate = useNavigate();

  // Validation states
  const [emailValid, setEmailValid] = useState<boolean | null>(null);
  const [passwordValid, setPasswordValid] = useState<boolean | null>(null);
  const [passwordsMatch, setPasswordsMatch] = useState<boolean | null>(null);

  // Navigate to profile page after successful registration
  useEffect(() => {
    if (registrationSuccess && currentUser && !loading && !error) {
      // Short delay to allow toast to be seen
      const timer = setTimeout(() => {
        navigate('/profile');
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [registrationSuccess, currentUser, loading, error, navigate]);

  // Email validation
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);

    if (value) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      setEmailValid(emailRegex.test(value));
    } else {
      setEmailValid(null);
    }
  };

  // Password validation
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);

    if (value) {
      setPasswordValid(value.length >= 8);
    } else {
      setPasswordValid(null);
    }

    // Check if passwords match whenever password changes
    if (confirmPassword) {
      setPasswordsMatch(value === confirmPassword);
    }
  };

  // Confirm password validation
  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setConfirmPassword(value);

    if (value) {
      setPasswordsMatch(value === password);
    } else {
      setPasswordsMatch(null);
    }
  };

  const validateForm = () => {
    // Clear previous errors
    setFormError(null);
    clearError();

    // Check if email is valid
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setFormError('Please enter a valid email address');
      return false;
    }

    // Check if password is strong enough
    if (password.length < 8) {
      setFormError('Password must be at least 8 characters long');
      return false;
    }

    // Check if passwords match
    if (password !== confirmPassword) {
      setFormError('Passwords do not match');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    await signUpWithEmail(email, password);

    // If there's no error after sign up, set success state
    // Navigation will happen via the useEffect
    if (!error) {
      setRegistrationSuccess(true);
    }
  };

  return (
    <div className="max-w-md mx-auto px-4 sm:px-6 py-12">
      <Card variant="glass" className="p-8 shadow-lg">
        <motion.h1
          className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          Create an Account
        </motion.h1>

        {(error || formError) && (
          <motion.div
            className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            transition={{ duration: 0.3 }}
          >
            {formError || error}
          </motion.div>
        )}

        <motion.form
          onSubmit={handleSubmit}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="space-y-6"
        >
          <EnhancedInput
            id="email"
            type="email"
            label="Email"
            value={email}
            onChange={handleEmailChange}
            fullWidth
            required
            glassmorphism
            leftIcon={<MailIcon className="h-5 w-5" />}
            error={emailValid === false ? "Please enter a valid email address" : undefined}
            success={emailValid === true}
            placeholder="Enter your email"
            animateLabel
          />

          <EnhancedInput
            id="password"
            type="password"
            label="Password"
            value={password}
            onChange={handlePasswordChange}
            fullWidth
            required
            glassmorphism
            leftIcon={<LockIcon className="h-5 w-5" />}
            error={passwordValid === false ? "Password must be at least 8 characters" : undefined}
            success={passwordValid === true}
            placeholder="Enter your password"
            animateLabel
            helperText="Password must be at least 8 characters long"
          />

          <EnhancedInput
            id="confirmPassword"
            type="password"
            label="Confirm Password"
            value={confirmPassword}
            onChange={handleConfirmPasswordChange}
            fullWidth
            required
            glassmorphism
            leftIcon={<CheckIcon className="h-5 w-5" />}
            error={passwordsMatch === false ? "Passwords do not match" : undefined}
            success={passwordsMatch === true}
            placeholder="Confirm your password"
            animateLabel
          />

          <motion.button
            type="submit"
            className="w-full bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
            disabled={loading}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {loading ? 'Creating Account...' : 'Sign Up'}
          </motion.button>
        </motion.form>

        <div className="mt-6 relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300 dark:border-gray-700"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
              Or continue with
            </span>
          </div>
        </div>

        <motion.button
          type="button"
          onClick={async () => {
            await signInWithGoogle();
            if (!error && currentUser) {
              setRegistrationSuccess(true);
            }
          }}
          className="mt-4 w-full flex items-center justify-center gap-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-700 px-4 py-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
          disabled={loading}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="24px" height="24px">
            <path fill="#FFC107" d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z" />
            <path fill="#FF3D00" d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z" />
            <path fill="#4CAF50" d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z" />
            <path fill="#1976D2" d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z" />
          </svg>
          <span>Sign up with Google</span>
        </motion.button>

        <motion.div
          className="mt-6 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Already have an account?{' '}
            <Link to="/login" className="text-orange-500 hover:text-orange-700 font-medium transition-colors duration-200">
              Log In
            </Link>
          </p>
        </motion.div>
      </Card>
    </div>
  );
};

export default SignUpPage;
