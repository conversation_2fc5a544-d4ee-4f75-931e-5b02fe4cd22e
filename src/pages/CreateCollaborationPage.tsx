import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import CollaborationForm from '../components/features/collaborations/CollaborationForm';

export const CreateCollaborationPage: React.FC = () => {
  const navigate = useNavigate();

  const handleSuccess = (collaborationId: string) => {
    navigate(`/collaborations/${collaborationId}`);
  };

  const handleCancel = () => {
    navigate('/projects');
  };

  return (
    <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="mb-6">
        <Link
          to="/projects"
          className="inline-flex items-center text-sm font-medium text-orange-600 hover:text-orange-500 dark:text-orange-500 dark:hover:text-orange-400"
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
          Back to Projects
        </Link>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-6">Create a New Collaboration</h1>

        <CollaborationForm
          isCreating={true}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
};

export default CreateCollaborationPage;
