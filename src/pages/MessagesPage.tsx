/**
 * Messages Page
 *
 * Page for displaying and interacting with chat messages.
 */

import React from 'react';
import { ChatContainer } from '../components/features/chat/ChatContainer';
import { useAuth } from '../AuthContext';
import PerformanceMonitor from '../components/ui/PerformanceMonitor';
import { themeClasses } from '../utils/themeUtils';

export const MessagesPage: React.FC = () => {
  const { currentUser, loading } = useAuth();

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700 border-t-orange-500 dark:border-t-orange-400"></div>
      </div>
    );
  }

  // Not logged in state
  if (!currentUser) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Authentication Required</h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            You need to be logged in to access your messages.
          </p>
          <a
            href="/login"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
          >
            Log In
          </a>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Performance monitoring (invisible) */}
      <PerformanceMonitor pageName="MessagesPage" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col space-y-4">
          <h1 className={`text-3xl font-bold ${themeClasses.text}`}>Messages</h1>

          {/* Chat container */}
          <div className={`${themeClasses.card} rounded-lg shadow-sm ${themeClasses.border} h-[calc(100vh-12rem)] ${themeClasses.transition}`}>
            <ChatContainer />
          </div>
        </div>
      </div>
    </>
  );
};
