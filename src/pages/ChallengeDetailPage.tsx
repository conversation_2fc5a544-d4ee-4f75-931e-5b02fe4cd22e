import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import { useAuth } from '../AuthContext';
import { getChallenge, Challenge } from '../services/firestore';
import { useToast } from '../contexts/ToastContext';
import { ArrowLeft, Award, Clock, Calendar, User } from 'lucide-react';
import { themeClasses } from '../utils/themeUtils';

export const ChallengeDetailPage: React.FC = () => {
  const { challengeId } = useParams<{ challengeId: string }>();
  const { currentUser } = useAuth();
  const { addToast } = useToast();
  // const navigate = useNavigate();

  const [challenge, setChallenge] = useState<Challenge | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isParticipating, setIsParticipating] = useState(false);

  useEffect(() => {
    if (challengeId) {
      fetchChallenge(challengeId);
    }
  }, [challengeId]);

  const fetchChallenge = async (id: string) => {
    setLoading(true);
    setError(null);

    try {
      const fetchResult = await getChallenge(id);
      if (fetchResult.error) {
        throw new Error(fetchResult.error.message || 'Unknown error');
      }
      if (fetchResult.data) {
        setChallenge(fetchResult.data);
        // Check if user is participating (this would be implemented with actual data)
        setIsParticipating(false);
      } else {
        throw new Error('Challenge not found');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch challenge');
      addToast('error', err.message || 'Failed to fetch challenge');
    } finally {
      setLoading(false);
    }
  };

  const handleParticipate = () => {
    if (!currentUser) {
      addToast('error', 'You must be logged in to participate in challenges');
      return;
    }

    // This would be implemented with actual participation logic
    setIsParticipating(true);
    addToast('success', 'You are now participating in this challenge!');
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  const getDifficultyClass = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'Intermediate':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      case 'Advanced':
        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300';
      case 'Expert':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  const getStatusClass = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'completed':
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
      case 'upcoming':
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 dark:border-orange-400"></div>
        </div>
      </div>
    );
  }

  if (error || !challenge) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6 transition-colors duration-200">
          {error || 'Challenge not found'}
        </div>
        <Link
          to="/challenges"
          className="inline-flex items-center text-orange-600 hover:text-orange-700 dark:text-orange-500 dark:hover:text-orange-400 transition-colors duration-200"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Challenges
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="mb-6">
        <Link
          to="/challenges"
          className="inline-flex items-center text-orange-600 hover:text-orange-700 dark:text-orange-500 dark:hover:text-orange-400 transition-colors duration-200"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Challenges
        </Link>
      </div>

      <div className={`${themeClasses.card} rounded-lg shadow-sm ${themeClasses.border} overflow-hidden ${themeClasses.transition}`}>
        {/* Header */}
        <div className={`px-6 py-5 border-b ${themeClasses.border} flex justify-between items-center ${themeClasses.transition}`}>
          <div>
            <h1 className={`text-2xl font-bold ${themeClasses.text}`}>{challenge.title}</h1>
            <div className="flex items-center mt-2 space-x-4">
              {challenge.difficulty && (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getDifficultyClass(challenge.difficulty)}`}>
                  {challenge.difficulty}
                </span>
              )}
              {challenge.status && (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusClass(challenge.status)}`}>
                  {challenge.status.charAt(0).toUpperCase() + challenge.status.slice(1)}
                </span>
              )}
            </div>
          </div>

          {challenge.status === 'active' && (
            <button
              onClick={handleParticipate}
              disabled={isParticipating}
              className={`px-4 py-2 rounded-md ${
                isParticipating
                  ? 'bg-green-500 text-white cursor-not-allowed'
                  : 'bg-orange-500 hover:bg-orange-600 text-white'
              } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200`}
            >
              {isParticipating ? 'Participating' : 'Participate'}
            </button>
          )}
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Challenge details */}
          <div className="mb-8">
            <h2 className={`text-lg font-semibold ${themeClasses.text} mb-3`}>Challenge Details</h2>
            <p className={`${themeClasses.textMuted} mb-6`}>{challenge.description}</p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              {challenge.category && (
                <div className={`flex items-center text-sm ${themeClasses.textMuted}`}>
                  <Award className="mr-2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                  <span>Category: {challenge.category}</span>
                </div>
              )}

              {challenge.deadline && challenge.deadline.toDate && (
                <div className={`flex items-center text-sm ${themeClasses.textMuted}`}>
                  <Clock className="mr-2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                  <span>Deadline: {formatDate(challenge.deadline.toDate())}</span>
                </div>
              )}

              {challenge.createdAt && challenge.createdAt.toDate && (
                <div className={`flex items-center text-sm ${themeClasses.textMuted}`}>
                  <Calendar className="mr-2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                  <span>Posted: {formatDate(challenge.createdAt.toDate())}</span>
                </div>
              )}

              {challenge.participants && (
                <div className={`flex items-center text-sm ${themeClasses.textMuted}`}>
                  <User className="mr-2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                  <span>Participants: {challenge.participants}</span>
                </div>
              )}
            </div>
          </div>

          {/* Rules and Requirements */}
          {(challenge as any).rules && (
            <div className="mb-8">
              <h2 className={`text-lg font-semibold ${themeClasses.text} mb-3`}>Rules and Requirements</h2>
              <div className={`bg-gray-50 dark:bg-gray-700 p-4 rounded-lg ${themeClasses.transition}`}>
                <ul className="list-disc pl-5 space-y-2">
                  {Array.isArray((challenge as any).rules) ? (
                    (challenge as any).rules.map((rule: string, index: number) => (
                      <li key={index} className={themeClasses.textMuted}>{rule}</li>
                    ))
                  ) : (
                    <li className={themeClasses.textMuted}>{(challenge as any).rules}</li>
                  )}
                </ul>
              </div>
            </div>
          )}

          {/* Prizes */}
          {(challenge as any).prizes && (
            <div className="mb-8">
              <h2 className={`text-lg font-semibold ${themeClasses.text} mb-3`}>Prizes</h2>
              <div className={`bg-gray-50 dark:bg-gray-700 p-4 rounded-lg ${themeClasses.transition}`}>
                <ul className="list-disc pl-5 space-y-2">
                  {Array.isArray((challenge as any).prizes) ? (
                    (challenge as any).prizes.map((prize: string, index: number) => (
                      <li key={index} className={themeClasses.textMuted}>{prize}</li>
                    ))
                  ) : (
                    <li className={themeClasses.textMuted}>{(challenge as any).prizes}</li>
                  )}
                </ul>
              </div>
            </div>
          )}

          {/* Submission Guidelines */}
          {(challenge as any).submissionGuidelines && (
            <div className="mb-8">
              <h2 className={`text-lg font-semibold ${themeClasses.text} mb-3`}>Submission Guidelines</h2>
              <div className={`bg-gray-50 dark:bg-gray-700 p-4 rounded-lg ${themeClasses.transition}`}>
                <p className={themeClasses.textMuted}>{(challenge as any).submissionGuidelines}</p>
              </div>
            </div>
          )}

          {/* Call to Action */}
          {challenge.status === 'active' && (
            <div className="mt-8 flex justify-center">
              <button
                onClick={handleParticipate}
                disabled={isParticipating}
                className={`px-6 py-3 rounded-md text-lg font-medium ${
                  isParticipating
                    ? 'bg-green-500 text-white cursor-not-allowed'
                    : 'bg-orange-500 hover:bg-orange-600 text-white'
                } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200`}
              >
                {isParticipating ? 'You are participating' : 'Join this Challenge'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChallengeDetailPage;
