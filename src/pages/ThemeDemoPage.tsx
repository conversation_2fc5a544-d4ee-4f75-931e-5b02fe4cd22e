import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { ThemeToggle } from '../components/ui/ThemeToggle';
import { themeClasses, withDarkMode } from '../utils/themeUtils';

const ThemeDemoPage: React.FC = () => {
  const { theme } = useTheme();

  return (
    <div className={`min-h-screen ${themeClasses.page} ${themeClasses.transition}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex justify-between items-center mb-8">
          <h1 className={`text-3xl font-bold ${themeClasses.text}`}>Theme Demo</h1>
          <ThemeToggle />
        </div>

        <div className={`mb-8 p-6 rounded-lg ${themeClasses.card} ${themeClasses.shadow}`}>
          <h2 className={`text-xl font-semibold mb-4 ${themeClasses.text}`}>Current Theme: {theme}</h2>
          <p className={themeClasses.textMuted}>
            This page demonstrates the theme system and how to use the theme utilities.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          <div className={`p-6 rounded-lg ${themeClasses.card} ${themeClasses.shadow}`}>
            <h2 className={`text-xl font-semibold mb-4 ${themeClasses.text}`}>Text Styles</h2>
            <div className="space-y-4">
              <p className={themeClasses.text}>Primary Text</p>
              <p className={themeClasses.textMuted}>Muted Text</p>
              <p className={withDarkMode('text-orange-500', 'text-orange-400')}>Accent Text</p>
              <p className={withDarkMode('text-green-600', 'text-green-400')}>Success Text</p>
              <p className={withDarkMode('text-red-600', 'text-red-400')}>Error Text</p>
            </div>
          </div>

          <div className={`p-6 rounded-lg ${themeClasses.card} ${themeClasses.shadow}`}>
            <h2 className={`text-xl font-semibold mb-4 ${themeClasses.text}`}>Button Styles</h2>
            <div className="space-y-4">
              <button className={`px-4 py-2 rounded-md ${themeClasses.primaryButton} ${themeClasses.transition}`}>
                Primary Button
              </button>
              <button className={`px-4 py-2 rounded-md ${themeClasses.secondaryButton} ${themeClasses.transition}`}>
                Secondary Button
              </button>
              <button className={`px-4 py-2 rounded-md ${withDarkMode('bg-green-500 hover:bg-green-600 text-white', 'bg-green-600 hover:bg-green-700')} ${themeClasses.transition}`}>
                Success Button
              </button>
              <button className={`px-4 py-2 rounded-md ${withDarkMode('bg-red-500 hover:bg-red-600 text-white', 'bg-red-600 hover:bg-red-700')} ${themeClasses.transition}`}>
                Danger Button
              </button>
            </div>
          </div>
        </div>

        <div className={`p-6 rounded-lg ${themeClasses.card} ${themeClasses.shadow}`}>
          <h2 className={`text-xl font-semibold mb-4 ${themeClasses.text}`}>Form Elements</h2>
          <div className="space-y-4">
            <div>
              <label htmlFor="name" className={`block text-sm font-medium mb-1 ${themeClasses.text}`}>
                Name
              </label>
              <input
                type="text"
                id="name"
                className={`w-full rounded-md px-3 py-2 ${themeClasses.input} ${themeClasses.text} ${themeClasses.focus}`}
                placeholder="Enter your name"
              />
            </div>
            <div>
              <label htmlFor="email" className={`block text-sm font-medium mb-1 ${themeClasses.text}`}>
                Email
              </label>
              <input
                type="email"
                id="email"
                className={`w-full rounded-md px-3 py-2 ${themeClasses.input} ${themeClasses.text} ${themeClasses.focus}`}
                placeholder="Enter your email"
              />
            </div>
            <div>
              <label htmlFor="message" className={`block text-sm font-medium mb-1 ${themeClasses.text}`}>
                Message
              </label>
              <textarea
                id="message"
                rows={4}
                className={`w-full rounded-md px-3 py-2 ${themeClasses.input} ${themeClasses.text} ${themeClasses.focus}`}
                placeholder="Enter your message"
              ></textarea>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="terms"
                className={`rounded ${themeClasses.focus}`}
              />
              <label htmlFor="terms" className={`ml-2 text-sm ${themeClasses.text}`}>
                I agree to the terms and conditions
              </label>
            </div>
            <button className={`px-4 py-2 rounded-md ${themeClasses.primaryButton} ${themeClasses.transition}`}>
              Submit
            </button>
          </div>
        </div>

        <div className={`mt-8 p-6 rounded-lg ${themeClasses.card} ${themeClasses.shadow}`}>
          <h2 className={`text-xl font-semibold mb-4 ${themeClasses.text}`}>How to Use Theme Utilities</h2>
          <div className={`p-4 rounded-md ${withDarkMode('bg-gray-100', 'bg-gray-700')} font-mono text-sm overflow-auto`}>
            <pre className={themeClasses.text}>
{`// Import theme utilities
import { themeClasses, withDarkMode } from '../utils/themeUtils';

// Use predefined theme classes
<div className={themeClasses.card}>
  <p className={themeClasses.text}>This text adapts to the theme</p>
</div>

// Use withDarkMode utility for custom styles
<button className={withDarkMode(
  'bg-blue-500 text-white', // Light mode styles
  'bg-blue-700 text-gray-100' // Dark mode styles
)}>
  Custom Button
</button>`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeDemoPage;
