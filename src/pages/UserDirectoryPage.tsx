import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../AuthContext';
import { getAllUsers, User } from '../services/firestore';
import { useToast } from '../contexts/ToastContext';
import { Search, Filter, AlertCircle } from '../utils/icons';
import { themeClasses } from '../utils/themeUtils';
import { ErrorBoundary } from '../components/ui/ErrorBoundary';
import UserCardSkeleton from '../components/ui/UserCardSkeleton';
import UserCard from '../components/features/users/UserCard';
import { Button } from '../components/ui/Button';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { Modal } from '../components/ui/Modal';
import ProfilePage from '../pages/ProfilePage';


export const UserDirectoryPage: React.FC = () => {
  const { currentUser } = useAuth();
  const { addToast } = useToast();

  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage] = useState(10);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Filters
  const [selectedSkill, setSelectedSkill] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Unique skills and locations for filters
  const [availableSkills, setAvailableSkills] = useState<string[]>([]);
  const [availableLocations, setAvailableLocations] = useState<string[]>([]);

  // Parse skills into array of skill objects
  const parseSkills = useCallback((skills?: string | string[] | any): { name: string; level?: string }[] => {
    if (!skills) return [];

    try {
      // If skills is already an array of objects
      if (Array.isArray(skills) && skills.length > 0 && typeof skills[0] === 'object') {
        return skills.map(skill => ({
          name: skill.name || skill.toString(),
          level: skill.level
        }));
      }

      // If skills is an array of strings
      if (Array.isArray(skills)) {
        return skills.map(skill => {
          if (typeof skill === 'string') {
            const parts = skill.split(':');
            return {
              name: parts[0].trim(),
              level: parts.length > 1 ? parts[1].trim() : undefined
            };
          }
          return { name: String(skill), level: undefined };
        });
      }

      // If skills is a string
      if (typeof skills === 'string') {
        return skills.split(',')
          .map(skill => skill.trim())
          .filter(skill => skill !== '')
          .map(skill => {
            const parts = skill.split(':');
            return {
              name: parts[0].trim(),
              level: parts.length > 1 ? parts[1].trim() : undefined
            };
          });
      }

      // If we get here, skills is some other type
      console.warn('Unexpected skills type:', typeof skills, skills);
      return [];
    } catch (error) {
      console.error('Error parsing skills:', error);
      return [];
    }
  }, []);

  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      const { data: usersList, error: usersError } = await getAllUsers();

      if (usersError) {
        setError(usersError.message);
        setLoading(false);
        return;
      }

      if (usersList) {
        // Filter out anonymous users and current user
        const filteredList = usersList.filter(user => {
          // Create a display name using the same logic as userUtils
          const effectiveDisplayName = user.displayName || user.email || `User ${user.id.substring(0, 5)}`;
          const hasValidDisplayName = effectiveDisplayName &&
            effectiveDisplayName.toLowerCase() !== 'anonymous' &&
            effectiveDisplayName !== 'Unknown User';
          const isNotCurrentUser = !currentUser || user.id !== currentUser.uid;

          return hasValidDisplayName && isNotCurrentUser;
        });

        // Deduplicate users by ID to prevent duplicate keys
        const uniqueUsers = filteredList.filter((user, index, self) =>
          index === self.findIndex(u => u.id === user.id)
        );

        setUsers(uniqueUsers);
        setFilteredUsers(uniqueUsers);

        // Extract unique skills and locations
        const skills = new Set<string>();
        const locations = new Set<string>();

        uniqueUsers.forEach(user => {
          if (user.skills) {
            try {
              const parsedSkills = parseSkills(user.skills);
              parsedSkills.forEach((skill: { name: string; level?: string }) => skills.add(skill.name));
            } catch (error) {
              console.error('Error parsing skills for filters:', error);
            }
          }
          if (user.location) locations.add(user.location);
        });

        setAvailableSkills(Array.from(skills).sort());
        setAvailableLocations(Array.from(locations).sort());
      }

      setLoading(false);
    };

    fetchUsers();
  }, [currentUser, addToast]);

  const applyFilters = useCallback(() => {
    let result = [...users];

    // Ensure no anonymous users (backup check) - use same logic as main filter
    result = result.filter(user => {
      const effectiveDisplayName = user.displayName || user.email || `User ${user.id.substring(0, 5)}`;
      return effectiveDisplayName &&
        effectiveDisplayName.toLowerCase() !== 'anonymous' &&
        effectiveDisplayName !== 'Unknown User';
    });

    // Apply search term filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(user => {
        const effectiveDisplayName = user.displayName || user.email || `User ${user.id.substring(0, 5)}`;
        return (effectiveDisplayName && effectiveDisplayName.toLowerCase().includes(term)) ||
          (user.bio && user.bio.toLowerCase().includes(term)) ||
          (user.skills && JSON.stringify(user.skills).toLowerCase().includes(term)) ||
          (user.interests && user.interests.toLowerCase().includes(term));
      });
    }

    // Apply skill filter
    if (selectedSkill) {
      result = result.filter(user =>
        user.skills && parseSkills(user.skills).some((skill: { name: string; level?: string }) => skill.name.toLowerCase().includes(selectedSkill.toLowerCase()))
      );
    }

    // Apply location filter
    if (selectedLocation) {
      result = result.filter(user =>
        user.location && user.location.toLowerCase().includes(selectedLocation.toLowerCase())
      );
    }

    setFilteredUsers(result);
  }, [users, searchTerm, selectedSkill, selectedLocation, parseSkills]);

  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  const resetFilters = () => {
    setSearchTerm('');
    setSelectedSkill('');
    setSelectedLocation('');
  };

  // Pagination logic
  const indexOfLastUser = currentPage * usersPerPage;
  const indexOfFirstUser = indexOfLastUser - usersPerPage;
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);

  const paginate = (pageNumber: number) => setCurrentPage(pageNumber);

  // Calculate total pages
  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);

  const handleUserClick = (user: User) => {
    setSelectedUser(user);
    setIsModalOpen(true);
  };



  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className={`text-3xl font-bold ${themeClasses.text}`}>User Directory</h1>
          <p className={`mt-1 text-sm ${themeClasses.textMuted}`}>
            Find and connect with other users
          </p>
        </div>

        <div className="mt-4 md:mt-0">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
          >
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </button>
        </div>
      </div>

      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400 dark:text-gray-500" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500 sm:text-sm transition-colors duration-200"
            placeholder="Search by name, skills, or interests..."
          />
        </div>
      </div>

      {showFilters && (
        <div className={`${themeClasses.card} p-4 rounded-lg shadow-sm ${themeClasses.border} mb-6 ${themeClasses.transition}`}>
          <div className="flex flex-col md:flex-row md:items-center md:space-x-4 space-y-4 md:space-y-0">
            <div className="flex-1">
              <label htmlFor="skill" className={`block text-sm font-medium ${themeClasses.text} mb-1`}>
                Skill
              </label>
              <select
                id="skill"
                value={selectedSkill}
                onChange={(e) => setSelectedSkill(e.target.value)}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
              >
                <option value="">All Skills</option>
                {availableSkills.map((skill) => (
                  <option key={skill} value={skill}>
                    {skill}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex-1">
              <label htmlFor="location" className={`block text-sm font-medium ${themeClasses.text} mb-1`}>
                Location
              </label>
              <select
                id="location"
                value={selectedLocation}
                onChange={(e) => setSelectedLocation(e.target.value)}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
              >
                <option value="">All Locations</option>
                {availableLocations.map((location) => (
                  <option key={location} value={location}>
                    {location}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-end">
              <button
                onClick={resetFilters}
                className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:text-orange-600 dark:hover:text-orange-500 focus:outline-none transition-colors duration-200"
              >
                Reset Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6 transition-colors duration-200">
          {error}
        </div>
      )}

      {/* Users List */}
      <ErrorBoundary
        fallback={
          <div className={`${themeClasses.card} p-6 rounded-lg shadow-sm ${themeClasses.border} ${themeClasses.transition}`}>
            <div className="flex items-center text-red-500 dark:text-red-400">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>Failed to load user directory</span>
            </div>
          </div>
        }
      >
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <UserCardSkeleton key={index} />
            ))}
          </div>
        ) : filteredUsers.length === 0 ? (
          <div className={`${themeClasses.card} p-6 rounded-lg shadow-sm ${themeClasses.border} text-center ${themeClasses.transition}`}>
            <p className={themeClasses.textMuted}>No users found matching your criteria.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {currentUsers.map(user => (
              <div key={user.id} onClick={() => handleUserClick(user)} className="cursor-pointer">
                <UserCard
                  user={user}
                  currentUserId={currentUser?.uid}
                  parseSkills={parseSkills}
                />
              </div>
            ))}
          </div>
        )}
      </ErrorBoundary>
      {!loading && filteredUsers.length === 0 && (
        <div className={`${themeClasses.card} p-12 rounded-lg shadow-sm ${themeClasses.border} text-center ${themeClasses.transition}`}>
          <h3 className={`text-lg font-medium ${themeClasses.text} mb-2`}>No users found</h3>
          <p className={`${themeClasses.textMuted} mb-6`}>
            Try adjusting your filters or search terms.
          </p>

          <button
            onClick={resetFilters}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
          >
            Reset Filters
          </button>
        </div>
      )}

      {/* Pagination */}
      <div className="flex justify-center items-center space-x-2 mt-6">
        <Button
          onClick={() => paginate(currentPage - 1)}
          disabled={currentPage === 1}
          variant="outline"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <span>
          Page {currentPage} of {totalPages}
        </span>
        <Button
          onClick={() => paginate(currentPage + 1)}
          disabled={currentPage === totalPages}
          variant="outline"
        >
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>

      {/* User Profile Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={selectedUser ? selectedUser.displayName : 'User Profile'}
      >
        {selectedUser && <ProfilePage userId={selectedUser.id} />}
      </Modal>
    </div>
  );
};

export default UserDirectoryPage;
