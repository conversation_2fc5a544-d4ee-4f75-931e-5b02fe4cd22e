import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../AuthContext';
import ProjectForm from '../components/features/projects/ProjectForm';
import { ArrowLeft } from 'lucide-react';

export const CreateProjectPage: React.FC = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  
  const handleSuccess = (projectId: string) => {
    navigate(`/projects/${projectId}`);
  };
  
  const handleCancel = () => {
    navigate('/projects');
  };
  
  if (!currentUser) {
    return (
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Sign in Required</h2>
          <p className="text-gray-600 mb-6">
            You need to be signed in to create a project.
          </p>
          <Link
            to="/login"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="mb-6">
        <Link
          to="/projects"
          className="inline-flex items-center text-sm font-medium text-orange-600 hover:text-orange-500"
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
          Back to Projects
        </Link>
      </div>
      
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Create a New Project</h1>
        
        <ProjectForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
};

export default CreateProjectPage;
