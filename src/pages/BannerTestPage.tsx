import React from 'react';
import DefaultBanner from '../components/ui/DefaultBanner';
import BannerSelector from '../components/ui/BannerSelector';
import { themeClasses } from '../utils/themeUtils';
import { useTheme } from '../contexts/ThemeContext';

const BannerTestPage: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div className={`min-h-screen p-8 ${themeClasses.page}`}>
      <div className="max-w-6xl mx-auto space-y-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className={`text-3xl font-bold ${themeClasses.text}`}>Default Banner Showcase</h1>
          <button
            onClick={toggleTheme}
            className={`px-4 py-2 rounded-md ${themeClasses.primaryButton}`}
          >
            Toggle {theme === 'dark' ? 'Light' : 'Dark'} Mode
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Classic Gradients */}
          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Gradient 1</h2>
            <DefaultBanner design="gradient1" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Gradient 2</h2>
            <DefaultBanner design="gradient2" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Gradient 3</h2>
            <DefaultBanner design="gradient3" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>3D Gradient</h2>
            <DefaultBanner design="gradient3d" height="md" />
          </div>

          {/* Patterns */}
          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Geometric 1</h2>
            <DefaultBanner design="geometric1" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Geometric 2</h2>
            <DefaultBanner design="geometric2" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Waves</h2>
            <DefaultBanner design="waves" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Dots</h2>
            <DefaultBanner design="dots" height="md" />
          </div>

          {/* Modern Styles */}
          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Glassmorphism 1</h2>
            <DefaultBanner design="glassmorphism1" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Glassmorphism 2</h2>
            <DefaultBanner design="glassmorphism2" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Neobrutalism 1</h2>
            <DefaultBanner design="neobrutalism1" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Neobrutalism 2</h2>
            <DefaultBanner design="neobrutalism2" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Abstract 3D</h2>
            <DefaultBanner design="abstract3d" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Liquid</h2>
            <DefaultBanner design="liquid" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Memphis</h2>
            <DefaultBanner design="memphis" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Cyberpunk</h2>
            <DefaultBanner design="cyberpunk" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Minimal</h2>
            <DefaultBanner design="minimal" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>Random</h2>
            <DefaultBanner design="random" height="md" />
          </div>

          <div className="space-y-2">
            <h2 className={`text-xl font-semibold ${themeClasses.text}`}>With Content</h2>
            <DefaultBanner design="gradient1" height="md" className="flex items-center justify-center">
              <div className="relative z-10 text-white text-center p-4 bg-black/30 backdrop-blur-sm rounded-lg">
                <h3 className="text-xl font-bold">Banner with Content</h3>
                <p>You can add any content inside the banner</p>
              </div>
            </DefaultBanner>
          </div>
        </div>

        <div className="mt-12 p-6 border border-neutral-200 dark:border-neutral-800 rounded-lg">
          <h2 className={`text-xl font-semibold mb-4 ${themeClasses.text}`}>Banner Selector Component</h2>
          <BannerSelector
            onSelect={(design) => console.log('Selected design:', design)}
          />
        </div>
      </div>
    </div>
  );
};

export default BannerTestPage;
