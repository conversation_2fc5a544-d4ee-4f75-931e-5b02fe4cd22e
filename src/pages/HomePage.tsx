import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { ThemeToggle } from '../components/ui/ThemeToggle';
import PerformanceMonitor from '../components/ui/PerformanceMonitor';
import AnimatedHeading from '../components/ui/AnimatedHeading';
import GradientMeshBackground from '../components/ui/GradientMeshBackground';
import { BentoGrid, BentoItem } from '../components/ui/BentoGrid';
import { Card, CardBody } from '../components/ui/Card';

/**
 * HomePage component
 *
 * Landing page for the TradeYa application
 */
const HomePage: React.FC = () => {
  return (
    <>
      {/* Performance monitoring (invisible) */}
      <PerformanceMonitor pageName="HomePage" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section with GradientMeshBackground */}
        <div className="relative rounded-2xl overflow-hidden mb-16">
          <GradientMeshBackground variant="primary" intensity="medium" className="p-12 md:p-16">
            <div className="flex justify-between items-center mb-2">
              <AnimatedHeading as="h1" animation="kinetic" className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-gray-100">
                Welcome to TradeYa
              </AnimatedHeading>
              <ThemeToggle />
            </div>
            <p className="text-xl text-gray-700 dark:text-gray-300 max-w-2xl mb-8 animate-fadeIn">
              Connect with others, exchange skills, and collaborate on exciting projects.
            </p>
          </GradientMeshBackground>
        </div>

        {/* Featured Content Section */}
        <AnimatedHeading as="h2" animation="slide" className="text-2xl md:text-3xl font-semibold text-gray-900 dark:text-gray-100 mb-8">
          Discover What's Possible
        </AnimatedHeading>

        {/* BentoGrid Layout */}
        <BentoGrid columns={6} gap="md" className="mb-12">
          {/* Skill Trades - Featured Item */}
          <BentoItem colSpan={3} rowSpan={2} className="overflow-hidden">
            <Card variant="glass" hover className="h-full">
              <CardBody>
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">Skill Trades</h2>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Exchange your skills with others in the community. Offer what you're good at and get help
                  with what you need. Our skill trading platform makes it easy to find the perfect match.
                </p>
                <Link to="/trades" className="text-orange-500 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 font-medium transition-colors duration-200">
                  Browse Trades →
                </Link>
              </CardBody>
            </Card>
          </BentoItem>

          {/* Projects */}
          <BentoItem colSpan={3} rowSpan={1} className="overflow-hidden">
            <Card variant="glass" hover className="h-full">
              <CardBody>
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Projects</h2>
                <p className="text-gray-600 dark:text-gray-300 mb-2">
                  Join collaborative projects or start your own. Find team members with the skills you need.
                </p>
                <Link to="/projects" className="text-orange-500 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 font-medium transition-colors duration-200">
                  Explore Projects →
                </Link>
              </CardBody>
            </Card>
          </BentoItem>

          {/* Challenges */}
          <BentoItem colSpan={3} rowSpan={1} className="overflow-hidden">
            <Card variant="glass" hover className="h-full">
              <CardBody>
                <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Challenges</h2>
                <p className="text-gray-600 dark:text-gray-300 mb-2">
                  Participate in weekly and monthly challenges to showcase your skills and win rewards.
                </p>
                <Link to="/challenges" className="text-orange-500 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 font-medium transition-colors duration-200">
                  View Challenges →
                </Link>
              </CardBody>
            </Card>
          </BentoItem>

          {/* User Directory */}
          <BentoItem colSpan={2} rowSpan={1} className="overflow-hidden">
            <Card variant="glass" hover className="h-full">
              <CardBody>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">User Directory</h2>
                <p className="text-gray-600 dark:text-gray-300 mb-2">
                  Discover talented individuals in our community.
                </p>
                <Link to="/users" className="text-orange-500 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 font-medium transition-colors duration-200">
                  Browse Users →
                </Link>
              </CardBody>
            </Card>
          </BentoItem>

          {/* Messages */}
          <BentoItem colSpan={2} rowSpan={1} className="overflow-hidden">
            <Card variant="glass" hover className="h-full">
              <CardBody>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Messages</h2>
                <p className="text-gray-600 dark:text-gray-300 mb-2">
                  Connect and communicate with other members.
                </p>
                <Link to="/messages" className="text-orange-500 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 font-medium transition-colors duration-200">
                  Go to Messages →
                </Link>
              </CardBody>
            </Card>
          </BentoItem>

          {/* Profile */}
          <BentoItem colSpan={2} rowSpan={1} className="overflow-hidden">
            <Card variant="glass" hover className="h-full">
              <CardBody>
                <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Your Profile</h2>
                <p className="text-gray-600 dark:text-gray-300 mb-2">
                  Manage your profile and showcase your skills.
                </p>
                <Link to="/profile" className="text-orange-500 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 font-medium transition-colors duration-200">
                  View Profile →
                </Link>
              </CardBody>
            </Card>
          </BentoItem>
        </BentoGrid>
      </div>
    </>
  );
};

export default HomePage;
