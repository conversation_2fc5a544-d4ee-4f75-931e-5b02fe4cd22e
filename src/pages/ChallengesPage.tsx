import React, { useState, useEffect } from 'react';
// import { useAuth } from '../AuthContext';
import { getChallenges, Challenge } from '../services/firestore';
import { useToast } from '../contexts/ToastContext';
import { Award, Filter, Search, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';
import { themeClasses } from '../utils/themeUtils';
import { cn } from '../utils/cn';

export const ChallengesPage: React.FC = () => {
  // const { currentUser } = useAuth();
  const { addToast } = useToast();

  const [challenges, setChallenges] = useState<Challenge[]>([]);
  const [filteredChallenges, setFilteredChallenges] = useState<Challenge[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Sample categories and difficulties
  const categories = [
    'Web Development',
    'Mobile Development',
    'UI/UX Design',
    'Data Science',
    'Machine Learning',
    'DevOps',
    'Blockchain',
    'Game Development',
    'Other'
  ];

  const difficulties = [
    'Beginner',
    'Intermediate',
    'Advanced',
    'Expert'
  ];

  const statuses = [
    'active',
    'completed',
    'upcoming'
  ];

  useEffect(() => {
    fetchChallenges();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [searchTerm, selectedCategory, selectedDifficulty, selectedStatus, challenges]);

  const fetchChallenges = async () => {
    setLoading(true);
    setError(null);

    try {
      // Filters are applied locally now after fetching all challenges
      // const filters: any = {};

      // if (selectedCategory) {
      //   filters.category = selectedCategory;
      // }

      // if (selectedDifficulty) {
      //   filters.difficulty = selectedDifficulty;
      // }

      // if (selectedStatus) {
      //   filters.status = selectedStatus;
      // }

      const challengesResult = await getChallenges(/* filters */);
      if (challengesResult.error) {
        if (typeof challengesResult.error === 'object' && 'message' in challengesResult.error) {
          throw new Error(challengesResult.error.message);
        } else {
          throw new Error(String(challengesResult.error));
        }
      }
      if (challengesResult.data) {
        setChallenges(challengesResult.data);
        // Apply filters after fetching all challenges
        applyFilters();
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch challenges');
      addToast('error', err.message || 'Failed to fetch challenges');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let result = [...challenges];

    // Apply search term filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter((challenge: Challenge) =>
        (challenge.title && challenge.title.toLowerCase().includes(term)) ||
        (challenge.description && challenge.description.toLowerCase().includes(term))
      );
    }

    // Apply category filter
    if (selectedCategory) {
      result = result.filter((challenge: Challenge) => challenge.category === selectedCategory);
    }

    // Apply difficulty filter
    if (selectedDifficulty) {
      result = result.filter((challenge: Challenge) => challenge.difficulty === selectedDifficulty);
    }

    // Apply status filter
    if (selectedStatus) {
      result = result.filter((challenge: Challenge) => challenge.status === selectedStatus);
    }

    setFilteredChallenges(result);
  };

  const resetFilters = () => {
    setSearchTerm('');
    setSelectedCategory('');
    setSelectedDifficulty('');
    setSelectedStatus('');
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className={`text-3xl font-bold ${themeClasses.text}`}>Challenges</h1>
          <p className={`mt-1 text-sm ${themeClasses.textMuted}`}>
            Participate in coding challenges to improve your skills
          </p>
        </div>

        <div className="mt-4 md:mt-0">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
          >
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </button>
        </div>
      </div>

      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search className="h-5 w-5 text-gray-400 dark:text-gray-500" />
          </div>
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500 sm:text-sm transition-colors duration-200"
            placeholder="Search challenges..."
          />
        </div>
      </div>

      {showFilters && (
        <div className={`${themeClasses.card} p-4 rounded-lg shadow-sm ${themeClasses.border} mb-6 ${themeClasses.transition}`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="category" className={`block text-sm font-medium ${themeClasses.text} mb-1`}>
                Category
              </label>
              <select
                id="category"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="difficulty" className={`block text-sm font-medium ${themeClasses.text} mb-1`}>
                Difficulty
              </label>
              <select
                id="difficulty"
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
              >
                <option value="">All Difficulties</option>
                {difficulties.map((difficulty) => (
                  <option key={difficulty} value={difficulty}>
                    {difficulty}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="status" className={`block text-sm font-medium ${themeClasses.text} mb-1`}>
                Status
              </label>
              <select
                id="status"
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
              >
                <option value="">All Statuses</option>
                {statuses.map((status) => (
                  <option key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="mt-4 flex justify-end">
            <button
              onClick={resetFilters}
              className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:text-orange-600 dark:hover:text-orange-500 focus:outline-none transition-colors duration-200"
            >
              Reset Filters
            </button>
            <button
              onClick={fetchChallenges}
              className={`ml-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${themeClasses.primaryButton} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 ${themeClasses.transition}`}
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6 transition-colors duration-200">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 dark:border-orange-400"></div>
        </div>
      ) : filteredChallenges.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredChallenges.map((challenge) => (
            <div
              key={challenge.id}
              className={cn(`${themeClasses.card} rounded-lg shadow-sm ${themeClasses.border} overflow-hidden ${themeClasses.transition}`, themeClasses.hoverCard)}
            >
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className={`text-lg font-medium ${themeClasses.text}`}>
                    {challenge.title}
                  </h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    challenge.difficulty === 'Beginner' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' :
                    challenge.difficulty === 'Intermediate' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300' :
                    challenge.difficulty === 'Advanced' ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300' :
                    'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                  }`}>
                    {challenge.difficulty}
                  </span>
                </div>

                {challenge.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                    {challenge.description}
                  </p>
                )}

                {challenge.category && (
                  <div className="flex items-center text-sm text-gray-500 mb-4">
                    <Award className="mr-1.5 h-4 w-4 text-gray-400 dark:text-gray-500" />
                    <span>{challenge.category}</span>
                  </div>
                )}

                {challenge.deadline && challenge.deadline.toDate && (
                  <div className="flex items-center text-sm text-gray-500 mb-4">
                    <Clock className="mr-1.5 h-4 w-4 text-gray-400 dark:text-gray-500" />
                    <span>Deadline: {formatDate(challenge.deadline.toDate())}</span>
                  </div>
                )}

                <div className="mt-4 flex justify-between items-center">
                  {challenge.status ? (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      challenge.status === 'active' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' :
                      challenge.status === 'completed' ? 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300' :
                      'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
                    }`}>
                      {challenge.status.charAt(0).toUpperCase() + challenge.status.slice(1)}
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 transition-colors duration-200">
                      Unknown
                    </span>
                  )}

                  <Link
                    to={`/challenges/${challenge.id}`}
                    className="text-orange-600 hover:text-orange-700 dark:text-orange-500 dark:hover:text-orange-400 font-medium text-sm transition-colors duration-200"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className={cn(`${themeClasses.card} p-12 rounded-lg shadow-sm ${themeClasses.border} text-center ${themeClasses.transition}`, themeClasses.hoverCard)}>
          <h3 className={`text-lg font-medium ${themeClasses.text} mb-2`}>No challenges found</h3>
          <p className={`${themeClasses.textMuted} mb-6`}>
            Try adjusting your filters or check back later for new challenges.
          </p>

          <button
            onClick={resetFilters}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
          >
            Reset Filters
          </button>
        </div>
      )}
    </div>
  );
};

export default ChallengesPage;
