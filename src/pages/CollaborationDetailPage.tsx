import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useAuth } from '../AuthContext';
import { 
  Collaboration, 
  CollaborationPermission,
  hasPermission,
  CollaborationRole // Corrected import path and removed unused CollaborationRoleData
} from '../types/collaboration';
import { FirestoreError } from 'firebase/firestore';
import { getRoleInheritance } from '../services/roleInheritance';

interface CollaborationDetailProps {
  onError?: (error: FirestoreError) => void;
}

const CollaborationDetailPage: React.FC<CollaborationDetailProps> = ({ onError }) => {
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();
  const [collaboration, setCollaboration] = useState<Collaboration | null>(null);
  const [userRole, setUserRole] = useState<CollaborationRole | null>(null); // This should remain CollaborationRole for general site roles
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<FirestoreError | null>(null);

  useEffect(() => {
    const loadCollaboration = async () => {
      if (!id || !user) return;

      try {
        const collab = await getRoleInheritance().getCollaboration(id);
        setCollaboration(collab);

        const member = collab.members.find(member => member.userId === user.uid);
        if (member) {
          setUserRole(member.role);
        } else if (collab.ownerId === user.uid) {
          setUserRole(CollaborationRole.OWNER);
        }
      } catch (err) {
        const firestoreError = err as FirestoreError;
        setError(firestoreError);
        onError?.(firestoreError);
      } finally {
        setLoading(false);
      }
    };

    loadCollaboration();
  }, [id, user, onError]);

  if (loading) {
    return <div>Loading collaboration details...</div>;
  }

  if (error) {
    return (
      <div className="error-container">
        <h2>Error Loading Collaboration</h2>
        <p>{error.message}</p>
      </div>
    );
  }

  if (!collaboration || !userRole) {
    return <div>Collaboration not found or access denied</div>;
  }

  const canEditSettings = hasPermission(userRole, CollaborationPermission.EDIT_SETTINGS);
  const canManageMembers = hasPermission(userRole, CollaborationPermission.MANAGE_MEMBERS);
  const canDeleteCollaboration = hasPermission(userRole, CollaborationPermission.DELETE_COLLABORATION);
  const canInviteMembers = hasPermission(userRole, CollaborationPermission.INVITE_MEMBERS);

  return (
    <div className="collaboration-detail">
      <header className="collaboration-header">
        <h1>{collaboration.name}</h1>
        <div className="role-badge">{userRole}</div>
      </header>

      <section className="collaboration-info">
        <p>{collaboration.description}</p>
        <div className="metadata">
          <span>Status: {collaboration.metadata.status}</span>
          <span>Members: {collaboration.metadata.membersCount}</span>
          <span>Last Activity: {collaboration.metadata.lastActivity.toLocaleDateString()}</span>
        </div>
      </section>

      {canEditSettings && (
        <section className="settings-section">
          <h2>Settings</h2>
          <div className="settings-controls">
            {/* Settings controls */}
          </div>
        </section>
      )}

      {canManageMembers && (
        <section className="members-section">
          <h2>Members</h2>
          <div className="members-list">
            {collaboration.members.map(member => (
              <div key={member.userId} className="member-item">
                {/* Member details */}
              </div>
            ))}
          </div>
        </section>
      )}

      <div className="actions-section">
        {canInviteMembers && (
          <button className="invite-button">
            Invite Members
          </button>
        )}
        
        {canDeleteCollaboration && (
          <button className="delete-button" onClick={() => {/* Delete logic */}}>
            Delete Collaboration
          </button>
        )}
      </div>
    </div>
  );
};

export default CollaborationDetailPage;
