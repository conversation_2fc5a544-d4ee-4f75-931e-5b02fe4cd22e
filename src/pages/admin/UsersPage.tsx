import React, { useState } from 'react';
import { useAuth } from '../../AuthContext';
import { useSecureAuth } from '../../auth/SecureAuthProvider';

interface UserRow {
  id: string;
  email: string;
  role: string;
  lastLogin: string;
  status: 'active' | 'blocked' | 'suspended';
}

const mockUsers: UserRow[] = [
  {
    id: '1',
    email: '<EMAIL>',
    role: 'admin',
    lastLogin: '2025-05-21',
    status: 'active'
  },
  {
    id: '2',
    email: '<EMAIL>',
    role: 'user',
    lastLogin: '2025-05-20',
    status: 'active'
  },
  {
    id: '3',
    email: '<EMAIL>',
    role: 'user',
    lastLogin: '2025-05-19',
    status: 'blocked'
  }
];

const UsersPage: React.FC = () => {
  const { validateSession } = useSecureAuth();
  const [users] = useState<UserRow[]>(mockUsers);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);

  const handleAction = async (userId: string, action: string) => {
    // Validate session before performing admin actions
    const isValid = await validateSession();
    if (!isValid) {
      return;
    }

    console.log(`Performing ${action} on user ${userId}`);
    // Implement user management actions here
  };

  const getStatusBadgeColor = (status: UserRow['status']) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'blocked':
        return 'bg-red-100 text-red-800';
      case 'suspended':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-6">User Management</h1>
      
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Login
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {users.map((user) => (
              <tr key={user.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{user.email}</div>
                  <div className="text-sm text-gray-500">{user.id}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{user.role}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{user.lastLogin}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeColor(user.status)}`}>
                    {user.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button
                    onClick={() => handleAction(user.id, 'block')}
                    className="text-red-600 hover:text-red-900 mr-4"
                  >
                    Block
                  </button>
                  <button
                    onClick={() => handleAction(user.id, 'reset')}
                    className="text-indigo-600 hover:text-indigo-900"
                  >
                    Reset Security
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default UsersPage;
