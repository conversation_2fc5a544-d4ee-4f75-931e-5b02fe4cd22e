import React from 'react';
import { useAuth } from '../../AuthContext';

const AdminSettingsPage: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="admin-settings">
      <h2>Admin Settings</h2>
      <div className="settings-content">
        <div className="settings-section">
          <h3>Security Settings</h3>
          <div className="setting-group">
            <label>
              Enable MFA Requirement
              <input type="checkbox" defaultChecked />
            </label>
            <label>
              Minimum Password Length
              <input type="number" defaultValue={12} min={8} max={32} />
            </label>
          </div>
        </div>

        <div className="settings-section">
          <h3>Access Controls</h3>
          <div className="setting-group">
            <label>
              Maximum Login Attempts
              <input type="number" defaultValue={5} min={1} max={10} />
            </label>
            <label>
              Session Timeout (minutes)
              <input type="number" defaultValue={30} min={5} max={120} />
            </label>
          </div>
        </div>

        <div className="settings-section">
          <h3>Monitoring</h3>
          <div className="setting-group">
            <label>
              Enable Activity Logging
              <input type="checkbox" defaultChecked />
            </label>
            <label>
              Send Security Alerts
              <input type="checkbox" defaultChecked />
            </label>
          </div>
        </div>

        <div className="action-buttons">
          <button className="save-button">Save Changes</button>
          <button className="reset-button">Reset to Defaults</button>
        </div>
      </div>

      <style jsx>{`
        .admin-settings {
          padding: 2rem;
        }

        .settings-content {
          max-width: 800px;
          margin: 2rem auto;
        }

        .settings-section {
          margin-bottom: 2rem;
          padding: 1.5rem;
          border: 1px solid #e5e7eb;
          border-radius: 0.5rem;
        }

        .setting-group {
          display: grid;
          gap: 1rem;
          margin-top: 1rem;
        }

        label {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 1rem;
        }

        input[type="number"] {
          width: 100px;
          padding: 0.5rem;
          border: 1px solid #d1d5db;
          border-radius: 0.375rem;
        }

        input[type="checkbox"] {
          width: 1.5rem;
          height: 1.5rem;
        }

        .action-buttons {
          display: flex;
          gap: 1rem;
          margin-top: 2rem;
          justify-content: flex-end;
        }

        button {
          padding: 0.75rem 1.5rem;
          border-radius: 0.375rem;
          font-weight: 500;
          cursor: pointer;
        }

        .save-button {
          background-color: #3b82f6;
          color: white;
          border: none;
        }

        .save-button:hover {
          background-color: #2563eb;
        }

        .reset-button {
          background-color: transparent;
          border: 1px solid #d1d5db;
        }

        .reset-button:hover {
          background-color: #f3f4f6;
        }
      `}</style>
    </div>
  );
};

export default AdminSettingsPage;
