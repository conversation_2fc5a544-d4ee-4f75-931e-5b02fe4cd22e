import React, { useState, useEffect } from 'react';
import { useAuth } from '../../AuthContext';
import {
  getAllUsers,
  getAllTrades,
  getAllProjects,
  updateUserRole,
  deleteUser,
  getSystemStats
} from '../../services/firestore';
import { useToast } from '../../contexts/ToastContext';
import {
  Users,
  ShoppingBag,
  Briefcase,
  MessageSquare,
  BarChart,
  // Shield,
  UserCog
} from 'lucide-react';

// Define tab types
type TabType = 'dashboard' | 'users' | 'trades' | 'projects';

const AdminDashboard: React.FC = () => {
  const { userProfile } = useAuth();
  const { addToast } = useToast();

  // State
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const [users, setUsers] = useState<any[]>([]);
  const [trades, setTrades] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch data based on active tab
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        switch (activeTab) {
          case 'dashboard': {
            const { data: systemStats, error: statsError } = await getSystemStats();
            if (statsError) throw new Error(statsError.message);
            setStats(systemStats);
            break;
          }

          case 'users': {
            const { data: usersList, error: usersError } = await getAllUsers();
            if (usersError) throw new Error(usersError.message);
            setUsers(usersList || []);
            break;
          }

          case 'trades': {
            const { data: tradesList, error: tradesError } = await getAllTrades();
            if (tradesError) throw new Error(tradesError.message);
            setTrades(tradesList || []);
            break;
          }

          case 'projects': {
            const { data: projectsList, error: projectsError } = await getAllProjects();
            if (projectsError) throw new Error(projectsError.message);
            setProjects(projectsList || []);
            break;
          }
        }
      } catch (err: any) {
        setError(err.message || 'An error occurred');
        addToast('error', err.message || 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [activeTab, addToast]);

  // Handle role change
  const handleRoleChange = async (userId: string, newRole: 'user' | 'admin' | 'moderator') => {
    try {
      const { error: updateError } = await updateUserRole(userId, newRole);

      if (updateError) {
        throw new Error(updateError.message);
      }

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? { ...user, role: newRole } : user
      ));

      addToast('success', `User role updated to ${newRole}`);
    } catch (err: any) {
      addToast('error', err.message || 'Failed to update user role');
    }
  };

  // Handle user deletion
  const handleDeleteUser = async (userId: string, userEmail: string) => {
    // Confirm deletion
    const isConfirmed = window.confirm(
      `Are you sure you want to delete user "${userEmail}"?\n\nThis action cannot be undone and will permanently remove all user data.`
    );

    if (!isConfirmed) return;

    try {
      const { error: deleteError } = await deleteUser(userId);

      if (deleteError) {
        throw new Error(deleteError.message);
      }

      // Update local state
      setUsers(users.filter(user => user.id !== userId));

      addToast('success', `User "${userEmail}" has been deleted`);
    } catch (err: any) {
      addToast('error', err.message || 'Failed to delete user');
    }
  };

  // Render dashboard stats
  const renderDashboard = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats && (
        <>
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <Users className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Users</p>
                <h3 className="text-2xl font-bold text-gray-900">{stats.totalUsers}</h3>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <ShoppingBag className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Trades</p>
                <h3 className="text-2xl font-bold text-gray-900">{stats.totalTrades}</h3>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                <Briefcase className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Projects</p>
                <h3 className="text-2xl font-bold text-gray-900">{stats.totalProjects}</h3>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
                <MessageSquare className="h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Messages</p>
                <h3 className="text-2xl font-bold text-gray-900">{stats.totalMessages}</h3>
              </div>
            </div>
          </div>
        </>
      )}

      <div className="md:col-span-2 lg:col-span-4 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
        <p className="text-gray-500">
          Welcome to the admin dashboard. Use the tabs above to manage users, trades, and projects.
        </p>
      </div>
    </div>
  );

  // Render users table
  const renderUsers = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Users</h3>
        <p className="text-sm text-gray-500">Manage user accounts and roles</p>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Email
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role & Actions
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Joined
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {users.map((user) => (
              <tr key={user.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="h-10 w-10 flex-shrink-0">
                      <img
                        className="h-10 w-10 rounded-full"
                        src={user.photoURL || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.displayName || user.email)}&background=random`}
                        alt={user.displayName || user.email}
                      />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">
                        {user.displayName || 'No Name'}
                      </div>
                      <div className="text-sm text-gray-500">
                        ID: {user.id.substring(0, 8)}...
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{user.email}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    user.role === 'admin'
                      ? 'bg-red-100 text-red-800'
                      : user.role === 'moderator'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                  }`}>
                    {user.role || 'user'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {user.createdAt ? new Date(user.createdAt.seconds * 1000).toLocaleDateString() : 'Unknown'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <select
                      value={user.role || 'user'}
                      onChange={(e) => handleRoleChange(user.id, e.target.value as 'user' | 'admin' | 'moderator')}
                      className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm rounded-md"
                      disabled={user.id === userProfile?.uid}
                    >
                      <option value="user">User</option>
                      <option value="moderator">Moderator</option>
                      <option value="admin">Admin</option>
                    </select>
                    {user.id !== userProfile?.uid && (
                      <button
                        onClick={() => handleDeleteUser(user.id, user.email)}
                        className="ml-2 px-3 py-1 bg-red-600 text-white text-xs rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                        title="Delete user"
                      >
                        Delete
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Render trades table
  const renderTrades = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Trades</h3>
        <p className="text-sm text-gray-500">Manage trade listings</p>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Title
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {trades.map((trade) => (
              <tr key={trade.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{trade.title}</div>
                  <div className="text-xs text-gray-500">ID: {trade.id.substring(0, 8)}...</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{trade.category}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{trade.userName}</div>
                  <div className="text-xs text-gray-500">{trade.userId.substring(0, 8)}...</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    trade.status === 'open' || trade.status === 'active'
                      ? 'bg-green-100 text-green-800'
                      : trade.status === 'in-progress'
                        ? 'bg-yellow-100 text-yellow-800'
                      : trade.status === 'pending_confirmation'
                        ? 'bg-purple-100 text-purple-800'
                      : trade.status === 'completed'
                        ? 'bg-blue-100 text-blue-800'
                      : trade.status === 'disputed'
                        ? 'bg-orange-100 text-orange-800'
                        : 'bg-red-100 text-red-800'
                  }`}>
                    {trade.status === 'active' ? 'open' : trade.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {trade.createdAt ? new Date(trade.createdAt.seconds * 1000).toLocaleDateString() : 'Unknown'}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Render projects table
  const renderProjects = () => (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">Projects</h3>
        <p className="text-sm text-gray-500">Manage project listings</p>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Title
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Owner
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {projects.map((project) => (
              <tr key={project.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">{project.title}</div>
                  <div className="text-xs text-gray-500">ID: {project.id.substring(0, 8)}...</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{project.category}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">{project.ownerName}</div>
                  <div className="text-xs text-gray-500">{project.ownerId.substring(0, 8)}...</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    project.status === 'open'
                      ? 'bg-green-100 text-green-800'
                      : project.status === 'in-progress'
                        ? 'bg-blue-100 text-blue-800'
                        : project.status === 'completed'
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-red-100 text-red-800'
                  }`}>
                    {project.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {project.createdAt ? new Date(project.createdAt.seconds * 1000).toLocaleDateString() : 'Unknown'}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Render content based on active tab
  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      );
    }

    switch (activeTab) {
      case 'dashboard':
        return renderDashboard();
      case 'users':
        return renderUsers();
      case 'trades':
        return renderTrades();
      case 'projects':
        return renderProjects();
      default:
        return null;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage users, trades, and projects
          </p>
        </div>
      </div>

      <div className="mb-8">
        <nav className="flex space-x-4" aria-label="Tabs">
          <button
            onClick={() => setActiveTab('dashboard')}
            className={`px-3 py-2 font-medium text-sm rounded-md flex items-center ${
              activeTab === 'dashboard'
                ? 'bg-orange-100 text-orange-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <BarChart className="mr-2 h-5 w-5" />
            Dashboard
          </button>

          <button
            onClick={() => setActiveTab('users')}
            className={`px-3 py-2 font-medium text-sm rounded-md flex items-center ${
              activeTab === 'users'
                ? 'bg-orange-100 text-orange-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <UserCog className="mr-2 h-5 w-5" />
            Users
          </button>

          <button
            onClick={() => setActiveTab('trades')}
            className={`px-3 py-2 font-medium text-sm rounded-md flex items-center ${
              activeTab === 'trades'
                ? 'bg-orange-100 text-orange-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <ShoppingBag className="mr-2 h-5 w-5" />
            Trades
          </button>

          <button
            onClick={() => setActiveTab('projects')}
            className={`px-3 py-2 font-medium text-sm rounded-md flex items-center ${
              activeTab === 'projects'
                ? 'bg-orange-100 text-orange-700'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Briefcase className="mr-2 h-5 w-5" />
            Projects
          </button>
        </nav>
      </div>

      {renderContent()}
    </div>
  );
};

export default AdminDashboard;
