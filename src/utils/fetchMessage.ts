import { db } from '../firebase-config';
import { doc, getDoc, collection, getDocs, query, where } from 'firebase/firestore';

/**
 * Utility function to fetch a specific message by its path
 */
export const fetchSpecificMessage = async (conversationId = 'bcB1UuJ2VHwTXsTFG71g', messageId = '9U88pB16BSVhy2taDEjH') => {
  try {
    // Try to fetch using the nested collection approach
    const messageDocRef = doc(db, 'conversations', conversationId, 'messages', messageId);
    const messageDoc = await getDoc(messageDocRef);

    if (messageDoc.exists()) {
      console.log('Found message using nested path:', messageDoc.data());
      return { message: messageDoc.data(), found: true, method: 'nested' };
    } else {
      console.log('Message not found in nested collection, trying flat collection...');

      // Try to fetch from the flat messages collection
      const flatMessageDocRef = doc(db, 'messages', messageId);
      const flatMessageDoc = await getDoc(flatMessageDocRef);

      if (flatMessageDoc.exists()) {
        console.log('Found message in flat collection:', flatMessageDoc.data());
        return { message: flatMessageDoc.data(), found: true, method: 'flat' };
      } else {
        console.log('Message not found in either collection structure');
        return { found: false };
      }
    }
  } catch (error) {
    console.error('Error fetching message:', error);
    return { error, found: false };
  }
};

/**
 * Utility function to fetch a specific conversation
 */
export const fetchSpecificConversation = async (conversationId = 'bcB1UuJ2VHwTXsTFG71g') => {
  try {
    const conversationDocRef = doc(db, 'conversations', conversationId);
    const conversationDoc = await getDoc(conversationDocRef);

    if (conversationDoc.exists()) {
      console.log('Found conversation:', conversationDoc.data());
      return { conversation: conversationDoc.data(), found: true };
    } else {
      console.log('Conversation not found');
      return { found: false };
    }
  } catch (error) {
    console.error('Error fetching conversation:', error);
    return { error, found: false };
  }
};

/**
 * Utility function to fetch all messages in a conversation
 */
export const fetchAllMessagesInConversation = async (conversationId = 'bcB1UuJ2VHwTXsTFG71g') => {
  try {
    // Try both approaches to find messages

    // 1. Try nested collection approach
    const nestedMessagesRef = collection(db, 'conversations', conversationId, 'messages');
    const nestedMessagesSnapshot = await getDocs(nestedMessagesRef);

    const nestedMessages: Array<any> = [];
    nestedMessagesSnapshot.forEach((doc) => {
      nestedMessages.push({
        id: doc.id,
        ...doc.data()
      });
    });

    // 2. Try flat collection with conversationId field
    const flatMessagesRef = collection(db, 'messages');
    const flatMessagesQuery = query(flatMessagesRef, where('conversationId', '==', conversationId));
    const flatMessagesSnapshot = await getDocs(flatMessagesQuery);

    const flatMessages: Array<any> = [];
    flatMessagesSnapshot.forEach((doc) => {
      flatMessages.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return {
      nestedMessages,
      flatMessages,
      found: nestedMessages.length > 0 || flatMessages.length > 0
    };
  } catch (error) {
    console.error('Error fetching all messages:', error);
    return { error, found: false };
  }
};
