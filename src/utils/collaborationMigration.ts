import { db } from '../firebase-config';
import {
  collection,
  doc,
  getDocs,
  writeBatch,
  Timestamp
} from 'firebase/firestore';
import { CollaborationRole, RoleState } from '../types/collaboration';

/**
 * Migrates existing collaborations to the new schema with roles subcollection
 */
export const migrateCollaborations = async (): Promise<{
  success: boolean;
  migratedCount: number;
  error?: string;
}> => {
  try {
    const collaborationsRef = collection(db, 'collaborations');
    const collaborationsSnapshot = await getDocs(collaborationsRef);

    let migratedCount = 0;

    // Process collaborations in batches to avoid hitting Firestore limits
    const batchSize = 20;
    const totalCollaborations = collaborationsSnapshot.docs.length;

    for (let i = 0; i < totalCollaborations; i += batchSize) {
      const batch = writeBatch(db);
      const currentBatch = collaborationsSnapshot.docs.slice(i, i + batchSize);

      for (const collaborationDoc of currentBatch) {
        const collaboration = collaborationDoc.data();
        const collaborationId = collaborationDoc.id;

        // Skip if already migrated (has roleCount field)
        if (collaboration.roleCount !== undefined) {
          continue;
        }

        // Extract existing roles array
        const existingRoles = collaboration.roles || [];

        // Create role documents
        let filledRoleCount = 0;
        let completedRoleCount = 0;

        for (let j = 0; j < existingRoles.length; j++) {
          const existingRole = existingRoles[j];

          // Determine role status
          let status = 'open';
          if (existingRole.filled) {
            status = 'filled';
            filledRoleCount++;
          }

          // Create new role document
          const roleRef = doc(collection(db, `collaborations/${collaborationId}/roles`));

          const newRole: CollaborationRole = {
            id: roleRef.id,
            collaborationId,
            title: existingRole.title || `Role ${j + 1}`,
            description: existingRole.description || '',
            requiredSkills: existingRole.skills ? existingRole.skills.map((skill: string) => ({
              name: skill,
              level: 'intermediate'
            })) : [],
            status: status as RoleState,
            applicationCount: 0,
            participantId: existingRole.participantId || undefined,
            metadata: {
              ...existingRole.metadata,
              participantName: existingRole.participantId || undefined
            },
            participantPhotoURL: existingRole.participantPhotoURL || undefined,
            createdAt: collaboration.createdAt || Timestamp.now(),
            updatedAt: collaboration.updatedAt || Timestamp.now(),
            childRoleIds: [],
            requirements: [],
            permissions: [],
            maxParticipants: 1,
            currentParticipants: existingRole.participantId ? 1 : 0,
            completionStatus: undefined,
            completionCriteria: {
              requiredDeliverables: [],
              reviewType: 'admin',
              minimumDuration: 1,
              maximumDuration: 30
            }
          };

          batch.set(roleRef, newRole);
        }

        // Update collaboration document
        const updatedCollaboration = {
          ...collaboration,
          roleCount: existingRoles.length,
          filledRoleCount,
          completedRoleCount,
          // Ensure status field uses new values
          status: collaboration.status === 'active' ? 'in-progress' : collaboration.status
        };

        // Remove the old roles array
        if ('roles' in updatedCollaboration) {
          delete (updatedCollaboration as any).roles;
        }

        batch.update(doc(db, 'collaborations', collaborationId), updatedCollaboration);

        migratedCount++;
      }

      await batch.commit();
      console.log(`Migrated batch ${i / batchSize + 1} of ${Math.ceil(totalCollaborations / batchSize)}`);
    }

    console.log(`Migration completed successfully. Migrated ${migratedCount} collaborations.`);
    return { success: true, migratedCount };
  } catch (error) {
    console.error('Error migrating collaborations:', error);
    return {
      success: false,
      migratedCount: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Rolls back the migration by removing roles subcollections and restoring the roles array
 * WARNING: This will delete all role applications and completion requests
 */
export const rollbackMigration = async (): Promise<{
  success: boolean,
  rolledBackCount: number,
  error?: string
}> => {
  try {
    const collaborationsRef = collection(db, 'collaborations');
    const collaborationsSnapshot = await getDocs(collaborationsRef);

    let rolledBackCount = 0;

    // Process collaborations in batches to avoid hitting Firestore limits
    const batchSize = 20;
    const totalCollaborations = collaborationsSnapshot.docs.length;

    for (let i = 0; i < totalCollaborations; i += batchSize) {
      const batch = writeBatch(db);
      const currentBatch = collaborationsSnapshot.docs.slice(i, i + batchSize);

      for (const collaborationDoc of currentBatch) {
        const collaboration = collaborationDoc.data();
        const collaborationId = collaborationDoc.id;

        // Skip if not migrated (no roleCount field)
        if (collaboration.roleCount === undefined) {
          continue;
        }

        // Get roles from subcollection
        const rolesRef = collection(db, `collaborations/${collaborationId}/roles`);
        const rolesSnapshot = await getDocs(rolesRef);

        // Convert roles back to array format
        const roles = rolesSnapshot.docs.map(roleDoc => {
          const role = roleDoc.data() as CollaborationRole;
          return {
            title: role.title,
            description: role.description,
            skills: role.requiredSkills.map(skill => skill.name),
            filled: role.status === 'filled' || role.status === 'completed',
            participantId: role.participantId,
            participantName: role.participantId,
            participantPhotoURL: role.participantPhotoURL
          };
        });

        // Update collaboration document
        const updatedCollaboration = {
          ...collaboration,
          roles
        };

        // Remove the new fields
        if ('roleCount' in updatedCollaboration) {
          delete (updatedCollaboration as any).roleCount;
        }
        if ('filledRoleCount' in updatedCollaboration) {
          delete (updatedCollaboration as any).filledRoleCount;
        }
        if ('completedRoleCount' in updatedCollaboration) {
          delete (updatedCollaboration as any).completedRoleCount;
        }

        batch.update(doc(db, 'collaborations', collaborationId), updatedCollaboration);

        // Note: We don't delete the roles subcollection here because Firestore doesn't
        // support deleting collections in batches. This would need to be done separately.

        rolledBackCount++;
      }

      await batch.commit();
      console.log(`Rolled back batch ${i / batchSize + 1} of ${Math.ceil(totalCollaborations / batchSize)}`);
    }

    console.log(`Rollback completed successfully. Rolled back ${rolledBackCount} collaborations.`);
    return { success: true, rolledBackCount };
  } catch (error) {
    console.error('Error rolling back migration:', error);
    return {
      success: false,
      rolledBackCount: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Deletes roles subcollections for all collaborations
 * WARNING: This will permanently delete all role data, applications, and completion requests
 */
export const deleteRolesSubcollections = async (): Promise<{
  success: boolean,
  processedCount: number,
  error?: string
}> => {
  try {
    const collaborationsRef = collection(db, 'collaborations');
    const collaborationsSnapshot = await getDocs(collaborationsRef);

    let processedCount = 0;

    for (const collaborationDoc of collaborationsSnapshot.docs) {
      const collaborationId = collaborationDoc.id;

      // Get roles from subcollection
      const rolesRef = collection(db, `collaborations/${collaborationId}/roles`);
      const rolesSnapshot = await getDocs(rolesRef);

      // Delete each role document
      const batch = writeBatch(db);
      let batchCount = 0;

      for (const roleDoc of rolesSnapshot.docs) {
        batch.delete(roleDoc.ref);
        batchCount++;

        // Commit when batch reaches limit
        if (batchCount >= 500) {
          await batch.commit();
          batchCount = 0;
        }
      }

      // Commit any remaining deletes
      if (batchCount > 0) {
        await batch.commit();
      }

      processedCount++;
    }

    console.log(`Deleted roles subcollections for ${processedCount} collaborations.`);
    return { success: true, processedCount };
  } catch (error) {
    console.error('Error deleting roles subcollections:', error);
    return {
      success: false,
      processedCount: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
