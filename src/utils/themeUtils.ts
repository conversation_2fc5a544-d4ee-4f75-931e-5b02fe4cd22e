/**
 * Utility functions for theme-related operations
 */

/**
 * Creates a class string that includes dark mode variants
 *
 * @param baseClasses - Base classes for light mode
 * @param darkClasses - Classes to apply in dark mode
 * @returns A string of classes with dark mode variants
 *
 * @example
 * // Returns "bg-white text-black dark:bg-gray-800 dark:text-white"
 * withDarkMode("bg-white text-black", "bg-gray-800 text-white")
 */
export const withDarkMode = (baseClasses: string, darkClasses: string): string => {
  const darkClassesWithPrefix = darkClasses
    .split(' ')
    .map(cls => `dark:${cls}`)
    .join(' ');

  return `${baseClasses} ${darkClassesWithPrefix}`;
};

/**
 * Common theme class combinations for reuse
 */
export const themeClasses = {
  // Backgrounds
  card: 'bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700',
  cardAlt: 'bg-neutral-50 dark:bg-neutral-700 border border-neutral-200 dark:border-neutral-600',
  page: 'bg-neutral-50 dark:bg-neutral-900',
  input: 'bg-white dark:bg-neutral-800 border-neutral-300 dark:border-neutral-600',

  // Text
  text: 'text-neutral-900 dark:text-neutral-100',
  textMuted: 'text-neutral-500 dark:text-neutral-400',
  textInverse: 'text-white dark:text-neutral-900',
  textAccent: 'text-primary-500 dark:text-primary-400',

  // Buttons
  primaryButton: 'bg-primary-500 hover:bg-primary-600 text-white',
  secondaryButton: 'bg-secondary-500 hover:bg-secondary-600 text-white',
  tertiaryButton: 'bg-neutral-200 dark:bg-neutral-700 hover:bg-neutral-300 dark:hover:bg-neutral-600 text-neutral-800 dark:text-neutral-200',
  dangerButton: 'bg-red-500 hover:bg-red-600 text-white',

  // Borders
  border: 'border-neutral-200 dark:border-neutral-700',
  borderAccent: 'border-primary-500 dark:border-primary-400',

  // Shadows
  shadowSm: 'shadow-sm dark:shadow-neutral-900/30',
  shadow: 'shadow dark:shadow-neutral-900/30',
  shadowMd: 'shadow-md dark:shadow-neutral-900/30',
  shadowLg: 'shadow-lg dark:shadow-neutral-900/30',

  // Focus
  focus: 'focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400 focus:ring-offset-2 dark:focus:ring-offset-neutral-800',

  // Transitions
  transition: 'transition-all duration-200',
  transitionFast: 'transition-all duration-150',
  transitionSlow: 'transition-all duration-300',

  // Hover effects
  hoverCard: 'hover:shadow-md hover:-translate-y-1 dark:hover:bg-neutral-700/70 dark:hover:shadow-[0_0_12px_rgba(251,146,60,0.15)]',
  hoverGlassCard: 'hover:shadow-md hover:-translate-y-1 hover:bg-white/80 dark:hover:bg-neutral-800/70 dark:hover:shadow-[0_0_15px_rgba(251,146,60,0.2)] hover:backdrop-blur-md',

  // Typography
  heading1: 'font-heading font-bold text-4xl md:text-5xl text-neutral-900 dark:text-white',
  heading2: 'font-heading font-semibold text-3xl md:text-4xl text-neutral-900 dark:text-white',
  heading3: 'font-heading font-semibold text-2xl md:text-3xl text-neutral-900 dark:text-white',
  heading4: 'font-heading font-medium text-xl md:text-2xl text-neutral-900 dark:text-white',
  heading5: 'font-heading font-medium text-lg md:text-xl text-neutral-900 dark:text-white',
  heading6: 'font-heading font-medium text-base md:text-lg text-neutral-900 dark:text-white',

  // For backward compatibility with new components
  heading: 'font-medium text-neutral-900 dark:text-white',
  label: 'text-neutral-700 dark:text-neutral-300',

  bodyLarge: 'font-sans text-lg text-neutral-700 dark:text-neutral-300 leading-relaxed',
  body: 'font-sans text-base text-neutral-700 dark:text-neutral-300 leading-relaxed',
  bodySmall: 'font-sans text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed',

  caption: 'font-sans text-xs text-neutral-500 dark:text-neutral-500',
  overline: 'font-sans text-xs uppercase tracking-wider text-neutral-500 dark:text-neutral-500 font-medium',

  link: 'text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 underline-offset-2 hover:underline',

  // Badges
  badgePrimary: 'bg-primary-100 text-primary-800 dark:bg-primary-900/30 dark:text-primary-300',
  badgeSecondary: 'bg-secondary-100 text-secondary-800 dark:bg-secondary-900/30 dark:text-secondary-300',

  // Legacy classes for backward compatibility
  legacyCard: 'bg-white dark:bg-gray-800',
  legacyPage: 'bg-gray-50 dark:bg-gray-900',
  legacyInput: 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600',
  legacyText: 'text-gray-900 dark:text-gray-100',
  legacyTextMuted: 'text-gray-500 dark:text-gray-400',
  legacyPrimaryButton: 'bg-orange-500 hover:bg-orange-600 text-white',
  legacySecondaryButton: 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200',
  legacyBorder: 'border-gray-200 dark:border-gray-700',
};

/**
 * Checks if the current theme is dark mode
 *
 * @returns Boolean indicating if dark mode is active
 */
export const isDarkMode = (): boolean => {
  if (typeof window === 'undefined') return false;
  return document.documentElement.classList.contains('dark');
};
