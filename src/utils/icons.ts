/**
 * Icon Utilities
 *
 * This file centralizes icon imports from lucide-react to optimize tree shaking.
 * Instead of importing icons directly from lucide-react in components,
 * import them from this file to ensure only used icons are included in the bundle.
 */

// Import only the icons we need from lucide-react
import {
  // Layout icons
  Home,
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,

  // User related icons
  User,
  Users,
  UserPlus,
  UserCheck,
  UserX,

  // Communication icons
  MessageSquare,
  Bell,
  Mail,

  // Action icons
  Plus,
  PlusCircle,
  Minus,
  MinusCircle,
  Edit,
  Trash,
  Search,
  Filter,
  Settings,
  LogOut,

  // Status icons
  Check,
  AlertCircle,
  Info,
  AlertTriangle,

  // Content icons
  Image,
  File,
  FileText,
  Upload,
  Download,

  // Commerce icons
  ShoppingBag,
  CreditCard,
  DollarSign,

  // Misc icons
  Briefcase,
  Award,
  Shield,
  Star,
  Heart,
  Calendar,
  Clock,
  MapPin,
  ExternalLink,
  Link,
  Loader2,
} from 'lucide-react';

// Export all icons
export {
  // Layout icons
  Home,
  Menu,
  X,
  ChevronDown,
  <PERSON><PERSON>ronUp,
  ChevronLeft,
  ChevronRight,

  // User related icons
  User,
  Users,
  UserPlus,
  UserCheck,
  UserX,

  // Communication icons
  MessageSquare,
  Bell,
  Mail,

  // Action icons
  Plus,
  PlusCircle,
  Minus,
  MinusCircle,
  Edit,
  Trash,
  Search,
  Filter,
  Settings,
  LogOut,

  // Status icons
  Check,
  AlertCircle,
  Info,
  AlertTriangle,

  // Content icons
  Image,
  File,
  FileText,
  Upload,
  Download,

  // Commerce icons
  ShoppingBag,
  CreditCard,
  DollarSign,

  // Misc icons
  Briefcase,
  Award,
  Shield,
  Star,
  Heart,
  Calendar,
  Clock,
  MapPin,
  ExternalLink,
  Link,
  Loader2,
};
