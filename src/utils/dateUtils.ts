/**
 * Date Utilities
 * 
 * Utilities for formatting and manipulating dates.
 */

/**
 * Format a date or timestamp to a readable string
 * @param date Date object, timestamp, or string
 * @returns Formatted date string
 */
export const formatDate = (date: Date | any): string => {
  if (!date) return '';
  
  // Handle Firebase Timestamp
  if (date && typeof date.toDate === 'function') {
    date = date.toDate();
  }
  
  // Convert string to Date if needed
  if (typeof date === 'string') {
    date = new Date(date);
  }
  
  // Check if date is valid
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return '';
  }
  
  // Format the date
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

/**
 * Get relative time string (e.g., "2 hours ago")
 * @param date Date object, timestamp, or string
 * @returns Relative time string
 */
export const getRelativeTimeString = (date: Date | any): string => {
  if (!date) return '';
  
  // Handle Firebase Timestamp
  if (date && typeof date.toDate === 'function') {
    date = date.toDate();
  }
  
  // Convert string to Date if needed
  if (typeof date === 'string') {
    date = new Date(date);
  }
  
  // Check if date is valid
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return '';
  }
  
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  // Less than a minute
  if (diffInSeconds < 60) {
    return 'just now';
  }
  
  // Less than an hour
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
  }
  
  // Less than a day
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
  }
  
  // Less than a week
  if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} ${days === 1 ? 'day' : 'days'} ago`;
  }
  
  // Less than a month
  if (diffInSeconds < 2592000) {
    const weeks = Math.floor(diffInSeconds / 604800);
    return `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
  }
  
  // Less than a year
  if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000);
    return `${months} ${months === 1 ? 'month' : 'months'} ago`;
  }
  
  // More than a year
  const years = Math.floor(diffInSeconds / 31536000);
  return `${years} ${years === 1 ? 'year' : 'years'} ago`;
};
