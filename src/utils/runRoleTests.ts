import { roleMonitoringTestRunner, TestResult } from './roleMonitoringTestRunner';

async function runRoleManagementTests() {
  console.log('Starting Role Management Flow Tests...\n');

  const testCollabId = `test-collab-${Date.now()}`;
  const results: TestResult[] = [];

  try {
    // Test 1: Create collaboration with multiple roles
    console.log('Test 1: Creating collaboration with multiple roles...');
    const createResult = await roleMonitoringTestRunner.runTestScenario({
      name: 'create-multi-roles',
      roles: [
        { title: 'Developer', skills: ['React', 'TypeScript'] },
        { title: 'Designer', skills: ['UI/UX', 'Figma'] }
      ]
    }, testCollabId);
    results.push(createResult);
    console.log(`✓ Creation test ${createResult.success ? 'passed' : 'failed'}\n`);

    // Test 2: Update existing roles
    console.log('Test 2: Updating existing roles...');
    const updateResult = await roleMonitoringTestRunner.runTestScenario({
      name: 'update-roles',
      operations: ['edit', 'delete', 'add'] as const
    }, testCollabId);
    results.push(updateResult);
    console.log(`✓ Update test ${updateResult.success ? 'passed' : 'failed'}\n`);

    // Test 3: Edge cases
    console.log('Test 3: Testing edge cases...');
    const edgeCaseResult = await roleMonitoringTestRunner.runTestScenario({
      name: 'edge-cases',
      scenarios: ['concurrent-edits', 'network-error', 'validation-error'] as const
    }, testCollabId);
    results.push(edgeCaseResult);
    console.log(`✓ Edge case test ${edgeCaseResult.success ? 'passed' : 'failed'}\n`);

    // Generate summary report
    console.log('Test Summary:');
    console.log('-------------');
    console.log(`Total Tests: ${results.length}`);
    console.log(`Passed: ${results.filter(r => r.success).length}`);
    console.log(`Failed: ${results.filter(r => !r.success).length}\n`);

    // Detailed results
    results.forEach((result, index) => {
      console.log(`Test ${index + 1}: ${result.scenarioName}`);
      console.log(`Status: ${result.success ? 'PASSED' : 'FAILED'}`);
      if (result.health.issues.length > 0) {
        console.log('Issues:');
        result.health.issues.forEach(issue => console.log(`- ${issue}`));
      }
      console.log(`Operations: ${result.operations.length}`);
      const failedOps = result.operations.filter(op => op.status === 'failed');
      if (failedOps.length > 0) {
        console.log('Failed Operations:');
        failedOps.forEach(op => console.log(`- ${op.type}: ${op.error}`));
      }
      console.log('---\n');
    });

  } catch (error) {
    console.error('Test execution failed:', error);
    throw error;
  }

  return {
    success: results.every(r => r.success),
    results,
    summary: {
      total: results.length,
      passed: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length
    }
  };
}

// Execute tests when run directly
if (require.main === module) {
  runRoleManagementTests()
    .then(results => {
      if (!results.success) {
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('Test execution failed:', error);
      process.exit(1);
    });
}

export { runRoleManagementTests };