import {
  createNotification as createFirestoreNotification,
  markNotificationAsRead as markFirestoreNotificationAsRead,
  markAllNotificationsAsRead as markFirestoreAllNotificationsAsRead,
  type Notification
} from '../firestore';
import { ServiceResult } from '../../types/ServiceError';

import { 
  collection, 
  query, 
  where, 
  orderBy, 
  onSnapshot
} from 'firebase/firestore';
import { db } from '../../firebase-config';

export type { Notification };

export const createNotification = createFirestoreNotification;
export const markNotificationAsRead = markFirestoreNotificationAsRead;
export const markAllNotificationsAsRead = markFirestoreAllNotificationsAsRead;

export const deleteNotification = async (notificationId: string): Promise<ServiceResult<void>> => {
  // For now, we'll just mark it as read since we don't want to actually delete notifications
  return markNotificationAsRead(notificationId);
};

export const getUserNotifications = (
  userId: string,
  callback: (notifications: Notification[]) => void
): (() => void) => {
  const notificationsRef = collection(db, 'notifications');
  const q = query(
    notificationsRef,
    where('userId', '==', userId),
    orderBy('createdAt', 'desc')
  );

  return onSnapshot(q, (snapshot) => {
    const notifications = snapshot.docs.map(doc => ({
      ...doc.data(),
      id: doc.id
    })) as Notification[];
    
    callback(notifications);
  });
};
