import { collection, addDoc } from 'firebase/firestore';
import { db } from '../firebase-config';
import { CreateNotificationParams } from '../types/services';

/**
 * Create a notification for a user
 */
export const createNotification = async (params: CreateNotificationParams): Promise<string | null> => {
  try {
    const notificationsRef = collection(db, 'notifications');
    const docRef = await addDoc(notificationsRef, {
      ...params,
      read: false
    });
    return docRef.id;
  } catch (error) {
    console.error('Error creating notification:', error);
    return null;
  }
};
