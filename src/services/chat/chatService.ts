/**
 * Chat Service
 * 
 * This service provides real-time chat functionality using Firebase Firestore.
 */

import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  query,
  where,
  orderBy,
  writeBatch,
  Timestamp,
  onSnapshot,
  arrayUnion,
  serverTimestamp,
  deleteDoc,
  FieldValue
} from 'firebase/firestore';
import { db } from '../../firebase-config';
import { ServiceResult } from '../../types/ServiceError';

// Types
export interface ChatMessage {
  id?: string;
  conversationId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  createdAt: Timestamp | FieldValue;
  readBy: string[];
  attachments?: {
    type: 'image' | 'file' | 'link';
    url: string;
    name?: string;
    size?: number;
    thumbnailUrl?: string;
  }[];
}

// ChatConversation: Used for real-time chat features (direct/group chat, avatars, chat-specific metadata).
export interface ChatConversation {
  id?: string;
  participants: {
    id: string;
    name: string;
    avatar?: string;
  }[];
  lastMessage?: {
    content: string;
    senderId: string;
    createdAt: Timestamp | FieldValue;
    read: boolean;
    id?: string;
  };
  lastMessageTimestamp?: Timestamp | FieldValue;
  createdAt: Timestamp | FieldValue;
  updatedAt: Timestamp | FieldValue;
  type: 'direct' | 'group';
  name?: string;
  unreadCount?: { [userId: string]: number };
  metadata?: {
    tradeId?: string;
    tradeName?: string;
    conversationType?: string;
  };
}

/**
 * Get all conversations for a user
 */
export const getUserConversations = (
  userId: string,
  callback: (conversations: ChatConversation[]) => void
) => {
  const conversationsRef = collection(db, 'conversations');
  const q = query(
    conversationsRef,
    where('participants', 'array-contains', { id: userId }),
    orderBy('updatedAt', 'desc')
  );
  
  return onSnapshot(q, (snapshot) => {
    const conversations: ChatConversation[] = [];
    
    snapshot.forEach((doc) => {
      const data = doc.data();
      conversations.push({
        id: doc.id,
        ...data
      } as ChatConversation);
    });
    
    callback(conversations);
  });
};

/**
 * Get or create a direct conversation between two users
 */
export const getOrCreateDirectConversation = async (
  user1: { id: string; name: string; avatar?: string },
  user2: { id: string; name: string; avatar?: string }
): Promise<ChatConversation> => {
  try {
    // Check if conversation already exists
    const conversationsRef = collection(db, 'conversations');
    const q1 = query(
      conversationsRef,
      where('type', '==', 'direct'),
      where('participants', 'array-contains', { id: user1.id })
    );
    
    const snapshot = await getDocs(q1);
    let existingConversation: ChatConversation | null = null;
    
    snapshot.forEach((doc) => {
      const data = doc.data() as ChatConversation;
      if (data.participants.some(p => p.id === user2.id)) {
        existingConversation = {
          id: doc.id,
          ...data
        };
      }
    });
    
    if (existingConversation) {
      return existingConversation;
    }
    
    // Create new conversation
    const newConversation: Omit<ChatConversation, 'id'> = {
      participants: [
        { id: user1.id, name: user1.name, avatar: user1.avatar },
        { id: user2.id, name: user2.name, avatar: user2.avatar }
      ],
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      type: 'direct'
    };
    
    const docRef = await addDoc(conversationsRef, newConversation);
    
    return {
      id: docRef.id,
      ...newConversation,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };
  } catch (error) {
    console.error('Error getting or creating conversation:', error);
    throw error;
  }
};

/**
 * Create a group conversation
 */
export const createGroupConversation = async (
  name: string,
  participants: { id: string; name: string; avatar?: string }[]
): Promise<ChatConversation> => {
  try {
    const conversationsRef = collection(db, 'conversations');
    
    const newConversation: Omit<ChatConversation, 'id'> = {
      name,
      participants,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
      type: 'group'
    };
    
    const docRef = await addDoc(conversationsRef, newConversation);
    
    return {
      id: docRef.id,
      ...newConversation,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    };
  } catch (error) {
    console.error('Error creating group conversation:', error);
    throw error;
  }
};

/**
 * Get messages for a conversation
 */
export const getConversationMessages = (
  conversationId: string,
  callback: (messages: ChatMessage[]) => void
) => {
  const messagesRef = collection(db, 'messages');
  const q = query(
    messagesRef,
    where('conversationId', '==', conversationId),
    orderBy('createdAt', 'asc')
  );
  
  return onSnapshot(q, (snapshot) => {
    const messages: ChatMessage[] = [];
    
    snapshot.forEach((doc) => {
      messages.push({
        id: doc.id,
        ...doc.data()
      } as ChatMessage);
    });
    
    callback(messages);
  });
};

/**
 * Send a message
 */
export const sendMessage = async (message: Omit<ChatMessage, 'id' | 'createdAt' | 'readBy'>): Promise<ChatMessage> => {
  try {
    const messagesRef = collection(db, 'messages');
    
    const newMessage = {
      ...message,
      createdAt: serverTimestamp(),
      readBy: [message.senderId]
    };
    
    const docRef = await addDoc(messagesRef, newMessage);
    
    // Update conversation's last message
    const conversationRef = doc(db, 'conversations', message.conversationId);
    await updateDoc(conversationRef, {
      lastMessage: {
        content: message.content,
        senderId: message.senderId,
        createdAt: serverTimestamp()
      },
      updatedAt: serverTimestamp()
    });
    
    return {
      id: docRef.id,
      ...newMessage,
      createdAt: Timestamp.now()
    } as ChatMessage;
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
};

/**
 * Mark messages as read
 */
export const markMessagesAsRead = async (conversationId: string, userId: string): Promise<void> => {
  try {
    const messagesRef = collection(db, 'messages');
    const q = query(
      messagesRef,
      where('conversationId', '==', conversationId),
      where('readBy', 'array-contains', userId)
    );
    
    const snapshot = await getDocs(q);
    
    const batch = writeBatch(db);
    
    snapshot.forEach((messageDoc) => {
      const messageRef = doc(db, 'messages', messageDoc.id);
      batch.update(messageRef, {
        readBy: arrayUnion(userId)
      });
    });
    
    await batch.commit();
  } catch (error) {
    console.error('Error marking messages as read:', error);
    throw error;
  }
};

/**
 * Add user to group conversation
 */
export const addUserToGroupConversation = async (
  conversationId: string,
  user: { id: string; name: string; avatar?: string }
): Promise<void> => {
  try {
    const conversationRef = doc(db, 'conversations', conversationId);
    const conversationSnap = await getDoc(conversationRef);
    
    if (!conversationSnap.exists()) {
      throw new Error('Conversation not found');
    }
    
    const conversation = conversationSnap.data() as ChatConversation;
    
    if (conversation.type !== 'group') {
      throw new Error('Cannot add user to direct conversation');
    }
    
    if (conversation.participants.some(p => p.id === user.id)) {
      throw new Error('User is already in the conversation');
    }
    
    await updateDoc(conversationRef, {
      participants: [...conversation.participants, user],
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error adding user to group conversation:', error);
    throw error;
  }
};

/**
 * Remove user from group conversation
 */
export const removeUserFromGroupConversation = async (
  conversationId: string,
  userId: string
): Promise<void> => {
  try {
    const conversationRef = doc(db, 'conversations', conversationId);
    const conversationSnap = await getDoc(conversationRef);
    
    if (!conversationSnap.exists()) {
      throw new Error('Conversation not found');
    }
    
    const conversation = conversationSnap.data() as ChatConversation;
    
    if (conversation.type !== 'group') {
      throw new Error('Cannot remove user from direct conversation');
    }
    
    if (!conversation.participants.some(p => p.id === userId)) {
      throw new Error('User is not in the conversation');
    }
    
    await updateDoc(conversationRef, {
      participants: conversation.participants.filter(p => p.id !== userId),
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error removing user from group conversation:', error);
    throw error;
  }
};

export const deleteConversation = async (conversationId: string): Promise<ServiceResult<void>> => {
  try {
    // Delete all messages in the conversation
    const messagesRef = collection(db, 'messages');
    const q = query(messagesRef, where('conversationId', '==', conversationId));
    const snapshot = await getDocs(q);

    const batch = writeBatch(db);

    snapshot.forEach((messageDoc) => {
      const messageRef = doc(db, 'messages', messageDoc.id);
      batch.delete(messageRef);
    });

    // Delete the conversation document
    const conversationRef = doc(db, 'conversations', conversationId);
    await deleteDoc(conversationRef);

    return {
      data: undefined,
      error: null
    };
  } catch (error) {
    console.error('Error deleting conversation:', error);
    return {
      data: undefined,
      error: error instanceof Error ? { code: 'delete-failed', message: error.message } : { code: 'delete-failed', message: 'An error occurred' }
    };
  }
};
