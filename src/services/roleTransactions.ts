import { transactionManager } from './transactionManager';
import { v4 as uuidv4 } from 'uuid';
import { db } from '../firebase-config';
import { doc, runTransaction, getDoc } from 'firebase/firestore';

interface RoleUpdate {
  roleId: string;
  updates: Record<string, any>;
  previousState?: Record<string, any>;
}

interface RoleOperation {
  execute: () => Promise<void>;
  rollback: () => Promise<void>;
}

export class RoleTransactionHandler {
  private readonly transactionId: string;

  constructor() {
    this.transactionId = uuidv4();
  }

  async executeRoleUpdate(roleId: string, updates: Record<string, any>): Promise<void> {
    const lockId = `role:${roleId}`;
    const ownerId = this.transactionId;

    try {
      // Acquire lock
      const locked = await transactionManager.acquireLock({
        lockId,
        ownerId,
        timeout: 30000,
        retryCount: 3
      });

      if (!locked) {
        throw new Error(`Failed to acquire lock for role ${roleId}`);
      }

      // Begin transaction
      await transactionManager.beginTransaction(this.transactionId);

      // Perform update with snapshot for rollback
      await this.performRoleUpdate({
        roleId,
        updates,
        previousState: await this.getRoleState(roleId)
      });

      // Commit transaction
      await transactionManager.commitTransaction(this.transactionId);
    } catch (error) {
      // Rollback on error
      await transactionManager.rollbackTransaction(this.transactionId);
      throw error;
    } finally {
      // Release lock
      await transactionManager.releaseLock(lockId, ownerId);
    }
  }

  async executeBatchRoleUpdates(updates: RoleUpdate[]): Promise<void> {
    const operations: RoleOperation[] = [];

    try {
      // Begin transaction
      await transactionManager.beginTransaction(this.transactionId);

      // Prepare all operations
      for (const update of updates) {
        const previousState = await this.getRoleState(update.roleId);
        
        operations.push({
          execute: async () => {
            await this.performRoleUpdate({
              ...update,
              previousState
            });
          },
          rollback: async () => {
            if (previousState) {
              await this.restoreRoleState(update.roleId, previousState);
            }
          }
        });
      }

      // Execute all operations
      for (const operation of operations) {
        await operation.execute();
      }

      // Commit transaction
      await transactionManager.commitTransaction(this.transactionId);
    } catch (error) {
      // Rollback all operations in reverse order
      for (let i = operations.length - 1; i >= 0; i--) {
        await operations[i].rollback();
      }
      await transactionManager.rollbackTransaction(this.transactionId);
      throw error;
    }
  }

  private async performRoleUpdate({ roleId, updates }: RoleUpdate): Promise<void> {
    const roleRef = doc(db, 'roles', roleId);

    await runTransaction(db, async (transaction) => {
      const roleDoc = await transaction.get(roleRef);
      
      if (!roleDoc.exists()) {
        throw new Error(`Role ${roleId} not found`);
      }

      // Validate updates before applying them
      await this.validateRoleUpdates(roleDoc.data(), updates);

      transaction.update(roleRef, {
        ...updates,
        updatedAt: Date.now(),
        transactionId: this.transactionId
      });
    });
  }

  private async validateRoleUpdates(currentState: any, updates: Record<string, any>): Promise<void> {
    // Add your role-specific validation logic here
    // For example, checking valid state transitions, required fields, etc.
    const validStateTransitions = ['active', 'suspended', 'completed'];
    
    if (updates.state && !validStateTransitions.includes(updates.state)) {
      throw new Error(`Invalid role state transition: ${updates.state}`);
    }
    
    // Check if the state transition is valid based on current state
    if (updates.state && currentState.state) {
      // Example validation using currentState
      if (currentState.state === 'completed' && updates.state !== 'completed') {
        throw new Error('Cannot change state from completed');
      }
    }
  }

  private async getRoleState(roleId: string): Promise<Record<string, any> | undefined> {
    const roleRef = doc(db, 'roles', roleId);
    const roleDoc = await getDoc(roleRef);
    
    if (!roleDoc.exists()) {
      return undefined;
    }

    return roleDoc.data() as Record<string, any>;
  }

  private async restoreRoleState(roleId: string, state: Record<string, any>): Promise<void> {
    const roleRef = doc(db, 'roles', roleId);
    
    await runTransaction(db, async (transaction) => {
      const roleDoc = await transaction.get(roleRef);
      
      if (!roleDoc.exists()) {
        throw new Error(`Role ${roleId} not found during state restoration`);
      }

      transaction.update(roleRef, {
        ...state,
        restoredAt: Date.now(),
        restoredBy: this.transactionId
      });
    });
  }
}

export const roleTransactionHandler = new RoleTransactionHandler();
