import React from 'react';
import ReactDOM from 'react-dom/client';
import { ErrorBoundary, FallbackProps as ErrorFallbackProps } from 'react-error-boundary';
import App from './App';
import './index.css';

// Define the error fallback component separately
function ErrorFallback({ error }: ErrorFallbackProps) {
  return (
    <div>
      <h1>{"An error occurred"}</h1>
      <p>Message: {error.message}</p>
      <button onClick={() => window.location.reload()}>Reload Page</button>
    </div>
  );
}

export function Test() {
  return <h1>Error Occurred</h1>;
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <App />
    </ErrorBoundary>
  </React.StrictMode>
);

// If you need this configuration later, uncomment the following:
// const esbuild = {
//   loader: 'tsx',
//   target: 'es2018',
//   jsx: 'automatic',
// };
