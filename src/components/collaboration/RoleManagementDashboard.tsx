import React, { useState, useEffect } from 'react';
import {
  CollaborationRoleData,
  RoleApplication
} from '../../types/collaboration';
import { themeClasses } from '../../utils/themeUtils';
import { AnimatePresence } from 'framer-motion';
import { getRoleApplications } from '../../services/roleApplications';
import { Button } from '../ui/Button';
import { ApplicationCard } from './ApplicationCard';

interface RoleManagementDashboardProps {
  collaboration: {
    id: string;
    title: string;
  };
  roles: CollaborationRoleData[];
  onAcceptApplication: (roleId: string, applicationId: string) => Promise<void>;
  onRejectApplication: (roleId: string, applicationId: string) => Promise<void>;
  onUpdateHierarchy?: (roleId: string, updates: {
    newParentId?: string;
    addChildIds?: string[];
    removeChildIds?: string[];
  }) => Promise<void>;
}

interface RoleManagementCardProps {
  role: CollaborationRoleData;
  collaborationId: string;
  onAcceptApplication: (roleId: string, applicationId: string) => Promise<void>;
  onRejectApplication: (roleId: string, applicationId: string) => Promise<void>;
  onUpdateHierarchy?: (roleId: string, updates: {
    newParentId?: string;
    addChildIds?: string[];
    removeChildIds?: string[];
  }) => Promise<void>;
}

const RoleManagementCard: React.FC<RoleManagementCardProps> = ({
  role,
  collaborationId,
  onAcceptApplication,
  onRejectApplication,
  onUpdateHierarchy,
}) => {
  const [applications, setApplications] = useState<RoleApplication[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hierarchyError, setHierarchyError] = useState<string | null>(null);
  const [isUpdatingHierarchy, setIsUpdatingHierarchy] = useState(false);
  const [hierarchyUpdateSuccess, setHierarchyUpdateSuccess] = useState(false);

  // Fetch applications for this role
  useEffect(() => {
    const fetchApplications = async () => {
      setIsLoading(true);
      try {
        const result = await getRoleApplications(collaborationId, role.id);
        if (result.success) {
          setApplications(result.data || []);
        } else {
          setError(result.error || 'Failed to load applications');
        }
      } catch (err) {
        setError('An error occurred while loading applications');
      } finally {
        setIsLoading(false);
      }
    };

    fetchApplications();
  }, [collaborationId, role.id]);

  return (
    <div className={`${themeClasses.card} p-6 mb-6`}>
      <div className="flex justify-between items-start">
        <h3 className={`text-lg font-semibold ${themeClasses.heading3}`}>{role.title}</h3>
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          role.status === 'open'
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
            : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
        }`}>
          {role.status === 'open' ? 'Open' : 'Filled'}
        </span>
      </div>

      <p className={`mt-2 text-sm ${themeClasses.text}`}>{role.description}</p>

      {/* Role Hierarchy Section */}
      {onUpdateHierarchy && (
        <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
          <div className="flex justify-between items-center">
            <h4 className={`text-md font-medium ${themeClasses.heading4}`}>Role Hierarchy</h4>
            <Button
              onClick={async () => {
                setIsUpdatingHierarchy(true);
                setHierarchyError(null);
                try {
                  await onUpdateHierarchy(role.id, { newParentId: undefined });
                  setHierarchyUpdateSuccess(true);
                  setTimeout(() => setHierarchyUpdateSuccess(false), 3000);
                } catch (err) {
                  setHierarchyError(err instanceof Error ? err.message : 'Failed to update role hierarchy');
                } finally {
                  setIsUpdatingHierarchy(false);
                }
              }}
              isLoading={isUpdatingHierarchy}
              disabled={isUpdatingHierarchy}
              variant="secondary"
              size="sm"
            >
              Edit Hierarchy
            </Button>
          </div>
          <div className="mt-4">
            {/* ... hierarchy content ... */}
          </div>
          {hierarchyError && (
            <div className="mt-2 text-sm text-red-600 dark:text-red-400">
              {hierarchyError}
            </div>
          )}
          {hierarchyUpdateSuccess && (
            <div className="mt-2 text-sm text-green-600 dark:text-green-400">
              Role hierarchy updated successfully
            </div>
          )}
          {isUpdatingHierarchy && (
            <div className="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
              <svg className="animate-spin -ml-1 mr-3 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Updating role hierarchy...
            </div>
          )}
        </div>
      )}

      {/* Applications Section */}
      {role.status === 'open' && (
        <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 className={`text-md font-medium ${themeClasses.heading4}`}>
            Applications ({applications.filter(app => app.status === 'pending').length})
          </h4>

          {isLoading ? (
            <div className="py-4 text-center">Loading...</div>
          ) : error ? (
            <div className="py-4 text-center text-red-500">{error}</div>
          ) : applications.length === 0 ? (
            <div className="py-4 text-center text-gray-500">No pending applications</div>
          ) : (
            <div className="mt-4 space-y-4">
              <AnimatePresence>
                {applications
                  .filter((app: RoleApplication) => app.status === 'pending')
                  .map((application: RoleApplication) => (
                    <ApplicationCard
                      key={application.id}
                      application={application}
                      onAccept={async () => onAcceptApplication(role.id, application.id)}
                      onReject={async () => onRejectApplication(role.id, application.id)}
                    />
                  ))}
              </AnimatePresence>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const RoleManagementDashboard: React.FC<RoleManagementDashboardProps> = ({
  collaboration,
  roles,
  onAcceptApplication,
  onRejectApplication,
  onUpdateHierarchy
}) => {
  const filteredRoles = roles;

  return (
    <div className={`${themeClasses.card} p-6`}>
      <div className="space-y-6">
        {filteredRoles.map((role: CollaborationRoleData) => (
          <RoleManagementCard
            key={role.id}
            role={role}
            collaborationId={collaboration.id}
            onAcceptApplication={onAcceptApplication}
            onRejectApplication={onRejectApplication}
            onUpdateHierarchy={onUpdateHierarchy}
          />
        ))}
      </div>
    </div>
  );
};

export { RoleManagementCard, RoleManagementDashboard };
export type { RoleManagementCardProps, RoleManagementDashboardProps };
