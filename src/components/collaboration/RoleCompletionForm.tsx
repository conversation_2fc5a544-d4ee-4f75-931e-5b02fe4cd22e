import React, { useState } from 'react';
import { CollaborationRoleData } from '../../types/collaboration';
import { EmbeddedEvidence } from '../../types/evidence';
import EvidenceSubmitter from '../evidence/EvidenceSubmitter';
import { EvidenceGallery } from '../features/evidence/EvidenceGallery';
import { themeClasses } from '../../utils/themeUtils';
import { motion } from 'framer-motion';

interface RoleCompletionFormProps {
  role: CollaborationRoleData;
  collaborationTitle: string;
  onSubmit: (completionData: {
    notes: string;
    evidence?: EmbeddedEvidence[];
  }) => Promise<void>;
  onCancel: () => void;
}

export const RoleCompletionForm: React.FC<RoleCompletionFormProps> = ({
  role,
  collaborationTitle,
  onSubmit,
  onCancel
}) => {
  const [notes, setNotes] = useState('');
  const [evidence, setEvidence] = useState<EmbeddedEvidence[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!notes.trim()) {
      setError('Please provide completion notes');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await onSubmit({
        notes,
        evidence: evidence.length > 0 ? evidence : undefined
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while submitting your completion request');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEvidenceChange = (updatedEvidence: EmbeddedEvidence[]) => {
    setEvidence(updatedEvidence);
  };

  const handleRemoveEvidence = (evidenceId: string) => {
    setEvidence(evidence.filter(item => item.id !== evidenceId));
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`${themeClasses.card} p-6`}
    >
      <div className="mb-6">
        <h2 className={`text-xl font-semibold ${themeClasses.heading2}`}>
          Request Completion for: {role.title}
        </h2>
        <p className={`mt-1 text-sm ${themeClasses.textMuted}`}>
          Part of collaboration: {collaborationTitle}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded dark:bg-red-900/20 dark:text-red-300 dark:border-red-800">
            {error}
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Completion Notes
          </label>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            rows={4}
            className={`mt-1 block w-full rounded-md ${themeClasses.input}`}
            placeholder="Describe what you've accomplished and how you've fulfilled the role requirements..."
            required
          />
          <p className={`mt-1 text-sm ${themeClasses.textMuted}`}>
            Be specific about what you've completed and how it meets the requirements of the role.
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Evidence of Completion
          </label>

          {evidence.length > 0 && (
            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Added Evidence
              </h4>
              <EvidenceGallery
                evidence={evidence}
                onRemove={handleRemoveEvidence}
                isEditable={true}
              />
            </div>
          )}

          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Add New Evidence
            </h4>
            <EvidenceSubmitter
              evidence={evidence}
              onChange={handleEvidenceChange}
              maxItems={5}
            />
          </div>
        </div>

        <div className="flex justify-end space-x-4 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={onCancel}
            className={`px-4 py-2 border ${themeClasses.secondaryButton}`}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={`px-4 py-2 ${themeClasses.primaryButton}`}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : 'Request Completion'}
          </button>
        </div>
      </form>
    </motion.div>
  );
};

export default RoleCompletionForm;
