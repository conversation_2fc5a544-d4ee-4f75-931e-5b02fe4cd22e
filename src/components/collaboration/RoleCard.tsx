import React from 'react';
import { CollaborationRoleData, RoleState } from '../../types/collaboration';
import { SkillBadge } from '../ui/SkillBadge';
import ProfileImageWithUser from '../ui/ProfileImageWithUser';
import { motion } from 'framer-motion';
import { themeClasses } from '../../utils/themeUtils';

interface RoleCardProps {
  role: CollaborationRoleData;
  collaborationId: string;
  isCreator: boolean;
  onApply?: () => void;
  onManage?: () => void;
  onEdit?: () => void;
  onRequestCompletion?: () => void;
  onAbandon?: () => void;
}

// Create a stable composite key for roles
const createRoleKey = (roleId: string, collaborationId: string, context: string) => {
  return `role-${roleId}-collab-${collaborationId}-${context}`;
};

// Map numeric skill levels to string types
const mapSkillLevel = (level: number): 'beginner' | 'intermediate' | 'expert' => {
  if (level <= 2) return 'beginner';
  if (level <= 4) return 'intermediate';
  return 'expert';
};

/**
 * Status badge component for role status
 */
const RoleStatusBadge: React.FC<{ status: CollaborationRoleData['status'] }> = ({ status }) => {
  const getStatusStyles = () => {
    switch (status) {
      case RoleState.OPEN:
        return 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800';
      case RoleState.FILLED:
        return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800';
      case RoleState.COMPLETED:
        return 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800';
      case RoleState.ABANDONED:
        return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700';
    }
  };

  const getStatusLabel = () => {
    switch (status) {
      case RoleState.OPEN:
        return 'Open';
      case RoleState.FILLED:
        return 'Filled';
      case RoleState.COMPLETED:
        return 'Completed';
      case RoleState.ABANDONED:
        return 'Abandoned';
      default:
        return 'Unknown';
    }
  };

  return (
    <span className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium ${getStatusStyles()}`}>
      {getStatusLabel()}
    </span>
  );
};

export const RoleCard: React.FC<RoleCardProps> = ({
  role,
  collaborationId,
  isCreator,
  onApply,
  onManage,
  onEdit,
  onRequestCompletion,
  onAbandon
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="backdrop-blur-md bg-white/70 dark:bg-gray-800/60 border border-white/20 dark:border-gray-700/30 rounded-lg shadow-lg p-6 transition-all duration-300 hover:shadow-xl"
    >
      <div className="flex justify-between items-start">
        <h3 className="text-lg font-semibold">{role.title}</h3>
        <RoleStatusBadge status={role.status} />
      </div>

      <p className="mt-2 text-gray-600 dark:text-gray-300">{role.description}</p>

      <div className="mt-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-200">Required Skills</h4>
        <div className="mt-1 flex flex-wrap gap-2">
          {role.requiredSkills?.map((skill) => (
            <SkillBadge
              key={createRoleKey(role.id, collaborationId, `required-${skill.name}`)}
              skill={skill.name}
              level={mapSkillLevel(skill.level)}
            />
          ))}
        </div>
      </div>

      {role.preferredSkills && role.preferredSkills.length > 0 && (
        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-200">Preferred Skills</h4>
          <div className="mt-1 flex flex-wrap gap-2">
            {role.preferredSkills.map((skill) => (
              <SkillBadge
                key={createRoleKey(role.id, collaborationId, `preferred-${skill.name}`)}
                skill={skill.name}
                level={mapSkillLevel(skill.level)}
              />
            ))}
          </div>
        </div>
      )}

      {role.participantId && (
        <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-200">Assigned To</h4>
          <div className="mt-1">
            <ProfileImageWithUser
              userId={role.participantId}
              profileUrl={role.participantPhotoURL}
              size="small"
            />
          </div>
        </div>
      )}

       {role.applicationCount !== undefined && role.applicationCount > 0 && isCreator && (
        <div className="mt-4 border-t border-gray-200 dark:border-gray-700 pt-4">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
            {role.applicationCount} application{role.applicationCount !== 1 ? 's' : ''}
          </span>
        </div>
      )}

      <div className="mt-6 flex justify-end">
        {isCreator ? (
          <>
            {role.status !== RoleState.COMPLETED && role.status !== RoleState.ABANDONED && (
              <button
                onClick={onEdit}
                className={`text-gray-600 dark:text-gray-300 hover:text-gray-800 dark:hover:text-white mr-4 ${themeClasses.transition}`}
              >
                Edit
              </button>
            )}
            {role.status === RoleState.FILLED && onAbandon && (
              <button
                onClick={onAbandon}
                className={`text-amber-600 dark:text-amber-400 hover:text-amber-800 dark:hover:text-amber-300 mr-4 ${themeClasses.transition}`}
                aria-label={`Abandon role ${role.title}`}
              >
                Abandon Role
              </button>
            )}
            <button
              onClick={onManage}
              className={`bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 ${themeClasses.transition}`}
              aria-label={`${role.status === RoleState.OPEN ? 'Manage applications' : 'Manage role'} for ${role.title}`}
            >
              {role.status === RoleState.OPEN ? 'Manage Applications' : 'Manage Role'}
            </button>
          </>
        ) : (
          <>
            {role.status === RoleState.OPEN && (            <button
              onClick={onApply}
              className={`bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 ${themeClasses.transition}`}
              aria-label={`Apply for ${role.title}`}
            >
              Apply
            </button>
            )}
            {role.status === RoleState.FILLED &&
             role.participantId &&
             onRequestCompletion &&
             !role.completionStatus && (
              <button
                onClick={onRequestCompletion}
                className={`bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 ${themeClasses.transition}`}
                aria-label={`Request completion for ${role.title}`}
              >
                Request Role Completion
              </button>
            )}
          </>
        )}
      </div>
    </motion.div>
  );
};

export default RoleCard;
