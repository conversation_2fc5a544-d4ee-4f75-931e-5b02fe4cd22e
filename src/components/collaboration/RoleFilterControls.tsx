import React, { useState, useEffect } from 'react';
import { Skill } from '../../types/collaboration';
import { themeClasses } from '../../utils/themeUtils';
import { Search, SlidersHorizontal, X, ChevronDown, ChevronUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

export interface RoleFilterOptions {
  search: string;
  status: ('open' | 'filled' | 'completed' | 'abandoned' | 'unneeded' | 'all')[];
  skills: string[];
  sortBy: 'createdAt' | 'updatedAt' | 'title' | 'status';
  sortDirection: 'asc' | 'desc';
  dateRange?: {
    start: Date | null;
    end: Date | null;
  };
}

interface RoleFilterControlsProps {
  availableSkills: Skill[];
  filters: RoleFilterOptions;
  onChange: (filters: RoleFilterOptions) => void;
  onReset: () => void;
}

export const RoleFilterControls: React.FC<RoleFilterControlsProps> = ({
  availableSkills,
  filters,
  onChange,
  onReset
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [localFilters, setLocalFilters] = useState<RoleFilterOptions>(filters);
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newFilters = {
      ...localFilters,
      search: e.target.value
    };
    setLocalFilters(newFilters);
    onChange(newFilters);
  };

  // Handle status filter change
  const handleStatusChange = (status: 'open' | 'filled' | 'completed' | 'abandoned' | 'unneeded' | 'all') => {
    let newStatuses: ('open' | 'filled' | 'completed' | 'abandoned' | 'unneeded' | 'all')[];

    if (status === 'all') {
      // If 'all' is selected, clear other selections
      newStatuses = ['all'];
    } else {
      // If a specific status is selected, remove 'all' if present
      const currentStatuses = localFilters.status.filter(s => s !== 'all');

      if (currentStatuses.includes(status)) {
        // If status is already selected, remove it
        newStatuses = currentStatuses.filter(s => s !== status);
        // If no statuses left, default to 'all'
        if (newStatuses.length === 0) {
          newStatuses = ['all'];
        }
      } else {
        // Add the status
        newStatuses = [...currentStatuses, status];
      }
    }

    const newFilters = {
      ...localFilters,
      status: newStatuses
    };

    setLocalFilters(newFilters);
    onChange(newFilters);
  };

  // Handle skill filter change
  const handleSkillChange = (skill: string) => {
    let newSkills: string[];

    if (localFilters.skills.includes(skill)) {
      // If skill is already selected, remove it
      newSkills = localFilters.skills.filter(s => s !== skill);
    } else {
      // Add the skill
      newSkills = [...localFilters.skills, skill];
    }

    const newFilters = {
      ...localFilters,
      skills: newSkills
    };

    setLocalFilters(newFilters);
    onChange(newFilters);
  };

  // Handle sort change
  const handleSortChange = (sortBy: 'createdAt' | 'updatedAt' | 'title' | 'status') => {
    const newFilters: RoleFilterOptions = {
      ...localFilters,
      sortBy,
      // If clicking the same sort field, toggle direction
      sortDirection: localFilters.sortBy === sortBy && localFilters.sortDirection === 'asc' ? 'desc' : 'asc'
    };

    setLocalFilters(newFilters);
    onChange(newFilters);
  };

  // Handle date range change
  const handleDateChange = (type: 'start' | 'end', date: string) => {
    const newDateRange = {
      start: localFilters.dateRange?.start || null,
      end: localFilters.dateRange?.end || null,
      [type]: date ? new Date(date) : null
    };

    const newFilters: RoleFilterOptions = {
      ...localFilters,
      dateRange: newDateRange
    };

    setLocalFilters(newFilters);
    onChange(newFilters);
  };

  // Reset filters
  const handleReset = () => {
    onReset();
  };

  // Get unique skill names from available skills
  const uniqueSkillNames = Array.from(new Set(availableSkills.map(skill => skill.name)));

  return (
    <div className={`${themeClasses.card} p-4 mb-6`}>
      {/* Search Bar */}
      <div className={`relative flex items-center mb-4 ${isSearchFocused ? 'ring-2 ring-blue-500' : ''}`}>
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Search className="h-4 w-4 text-gray-400" />
        </div>
        <input
          type="text"
          value={localFilters.search}
          onChange={handleSearchChange}
          onFocus={() => setIsSearchFocused(true)}
          onBlur={() => setIsSearchFocused(false)}
          placeholder="Search roles by title or description..."
          className={`block w-full pl-10 pr-10 py-2 border rounded-md ${themeClasses.input}`}
        />
        {localFilters.search && (
          <button
            onClick={() => {
              const newFilters = { ...localFilters, search: '' };
              setLocalFilters(newFilters);
              onChange(newFilters);
            }}
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
          >
            <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
          </button>
        )}
      </div>

      {/* Quick Filters */}
      <div className="flex flex-wrap gap-2 mb-4">
        <button
          onClick={() => handleStatusChange('all')}
          className={`px-3 py-1 text-sm rounded-full ${
            localFilters.status.includes('all')
              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          All
        </button>
        <button
          onClick={() => handleStatusChange('open')}
          className={`px-3 py-1 text-sm rounded-full ${
            localFilters.status.includes('open')
              ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          Open
        </button>
        <button
          onClick={() => handleStatusChange('filled')}
          className={`px-3 py-1 text-sm rounded-full ${
            localFilters.status.includes('filled')
              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          Filled
        </button>
        <button
          onClick={() => handleStatusChange('completed')}
          className={`px-3 py-1 text-sm rounded-full ${
            localFilters.status.includes('completed')
              ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          Completed
        </button>
        <button
          onClick={() => handleStatusChange('abandoned')}
          className={`px-3 py-1 text-sm rounded-full ${
            localFilters.status.includes('abandoned')
              ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          Abandoned
        </button>
        <button
          onClick={() => handleStatusChange('unneeded')}
          className={`px-3 py-1 text-sm rounded-full ${
            localFilters.status.includes('unneeded')
              ? 'bg-gray-300 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
          }`}
        >
          Unneeded
        </button>
      </div>

      {/* Sort Controls */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <span className="text-sm font-medium mr-2">Sort by:</span>
          <div className="flex gap-2">
            <button
              onClick={() => handleSortChange('title')}
              className={`px-3 py-1 text-sm rounded-md flex items-center ${
                localFilters.sortBy === 'title'
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Title
              {localFilters.sortBy === 'title' && (
                localFilters.sortDirection === 'asc' ? <ChevronUp className="h-3 w-3 ml-1" /> : <ChevronDown className="h-3 w-3 ml-1" />
              )}
            </button>
            <button
              onClick={() => handleSortChange('status')}
              className={`px-3 py-1 text-sm rounded-md flex items-center ${
                localFilters.sortBy === 'status'
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Status
              {localFilters.sortBy === 'status' && (
                localFilters.sortDirection === 'asc' ? <ChevronUp className="h-3 w-3 ml-1" /> : <ChevronDown className="h-3 w-3 ml-1" />
              )}
            </button>
            <button
              onClick={() => handleSortChange('createdAt')}
              className={`px-3 py-1 text-sm rounded-md flex items-center ${
                localFilters.sortBy === 'createdAt'
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Created
              {localFilters.sortBy === 'createdAt' && (
                localFilters.sortDirection === 'asc' ? <ChevronUp className="h-3 w-3 ml-1" /> : <ChevronDown className="h-3 w-3 ml-1" />
              )}
            </button>
          </div>
        </div>

        <div className="flex items-center">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center text-sm font-medium text-blue-600 dark:text-blue-400"
          >
            <SlidersHorizontal className="h-4 w-4 mr-1" />
            {showAdvanced ? 'Hide Filters' : 'Advanced Filters'}
          </button>

          {(localFilters.search ||
            !localFilters.status.includes('all') ||
            localFilters.skills.length > 0 ||
            localFilters.sortBy !== 'createdAt' ||
            localFilters.sortDirection !== 'desc') && (
            <button
              onClick={handleReset}
              className="ml-4 text-sm font-medium text-red-600 dark:text-red-400"
            >
              Reset Filters
            </button>
          )}
        </div>
      </div>

      {/* Advanced Filters */}
      <AnimatePresence>
        {showAdvanced && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
              {/* Skills Filter */}
              <div className="mb-4">
                <h4 className={`text-sm font-medium mb-2 ${themeClasses.heading5}`}>Filter by Skills</h4>
                <div className="flex flex-wrap gap-2">
                  {uniqueSkillNames.map(skill => (
                    <button
                      key={skill}
                      onClick={() => handleSkillChange(skill)}
                      className={`px-3 py-1 text-sm rounded-full ${
                        localFilters.skills.includes(skill)
                          ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                      }`}
                    >
                      {skill}
                    </button>
                  ))}
                </div>
              </div>

              {/* Date Range Filter */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className={`block text-sm font-medium mb-1 ${themeClasses.label}`}>
                    From Date
                  </label>
                  <input
                    type="date"
                    value={localFilters.dateRange?.start ? localFilters.dateRange.start.toISOString().split('T')[0] : ''}
                    onChange={(e) => handleDateChange('start', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md ${themeClasses.input}`}
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 ${themeClasses.label}`}>
                    To Date
                  </label>
                  <input
                    type="date"
                    value={localFilters.dateRange?.end ? localFilters.dateRange.end.toISOString().split('T')[0] : ''}
                    onChange={(e) => handleDateChange('end', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md ${themeClasses.input}`}
                  />
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default RoleFilterControls;
