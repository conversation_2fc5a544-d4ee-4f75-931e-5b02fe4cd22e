import React, { useState } from 'react';
import { CompletionRequest } from '../../types/collaboration';
import ProfileImageWithUser from '../ui/ProfileImageWithUser';
import { EvidenceGallery } from '../features/evidence/EvidenceGallery';
import { themeClasses } from '../../utils/themeUtils';
import { motion } from 'framer-motion';
import { CheckCircleIcon, XCircleIcon } from 'lucide-react';

interface CompletionRequestCardProps {
  request: CompletionRequest;
  onConfirm: () => Promise<void>;
  onReject: (reason: string) => Promise<void>;
}

export const CompletionRequestCard: React.FC<CompletionRequestCardProps> = ({
  request,
  onConfirm,
  onReject
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [showRejectForm, setShowRejectForm] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [error, setError] = useState<string | null>(null);

  const handleConfirm = async () => {
    setIsProcessing(true);
    setError(null);
    try {
      await onConfirm();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to confirm completion');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      setError('Please provide a reason for rejection');
      return;
    }

    setIsProcessing(true);
    setError(null);
    try {
      await onReject(rejectReason);
      setShowRejectForm(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reject completion');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={`${themeClasses.card} p-4 mb-4`}
    >
      <div className="flex items-start justify-between">
        <div className="flex items-center">
          <ProfileImageWithUser
            userId={request.requesterId}
            size="small"
          />
        </div>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          {new Date(request.createdAt.toDate()).toLocaleDateString()}
        </div>
      </div>

      <div className="mt-4">
        <h4 className={`text-sm font-medium ${themeClasses.heading5}`}>Completion Notes</h4>
        <p className={`mt-1 text-sm ${themeClasses.text}`}>{request.notes}</p>
      </div>

      {request.evidence && request.evidence.length > 0 && (
        <div className="mt-4">
          <h4 className={`text-sm font-medium ${themeClasses.heading5}`}>Completion Evidence</h4>
          <div className="mt-2">
            <EvidenceGallery
              evidence={request.evidence}
              title="Submitted Evidence"
            />
          </div>
        </div>
      )}

      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md dark:bg-red-900/20 dark:text-red-300">
          {error}
        </div>
      )}

      {showRejectForm ? (
        <div className="mt-4">
          <h4 className={`text-sm font-medium ${themeClasses.heading5}`}>Rejection Reason</h4>
          <textarea
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
            className={`mt-1 w-full px-3 py-2 border rounded-md ${themeClasses.input}`}
            rows={3}
            placeholder="Please provide a reason for rejecting this completion request..."
          />
          <div className="mt-2 flex justify-end space-x-3">
            <button
              onClick={() => setShowRejectForm(false)}
              disabled={isProcessing}
              className={`px-3 py-1 text-sm border ${themeClasses.secondaryButton} ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Cancel
            </button>
            <button
              onClick={handleReject}
              disabled={isProcessing}
              className={`px-3 py-1 text-sm bg-red-600 text-white hover:bg-red-700 rounded-md ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Confirm Rejection
            </button>
          </div>
        </div>
      ) : (
        <div className="mt-4 flex justify-end space-x-3">
          <button
            onClick={() => setShowRejectForm(true)}
            disabled={isProcessing}
            className={`px-3 py-1 text-sm flex items-center border ${themeClasses.secondaryButton} ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <XCircleIcon className="h-4 w-4 mr-1" />
            Reject
          </button>
          <button
            onClick={handleConfirm}
            disabled={isProcessing}
            className={`px-3 py-1 text-sm flex items-center ${themeClasses.primaryButton} ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <CheckCircleIcon className="h-4 w-4 mr-1" />
            Confirm Completion
          </button>
        </div>
      )}
    </motion.div>
  );
};

export default CompletionRequestCard;
