import React, { useState } from 'react';
import { CompletionRequest, CollaborationRoleData } from '../../types/collaboration';
import ProfileImageWithUser from '../ui/ProfileImageWithUser';
import { EvidenceGallery } from '../features/evidence/EvidenceGallery';
import { themeClasses } from '../../utils/themeUtils';
import { motion, AnimatePresence } from 'framer-motion';
import { CheckCircleIcon, XCircleIcon, ArrowLeftIcon, FileTextIcon } from 'lucide-react';

interface CompletionReviewModalProps {
  request: CompletionRequest;
  role: CollaborationRoleData;
  collaborationTitle: string;
  onConfirm: () => Promise<void>;
  onReject: (reason: string) => Promise<void>;
  onClose: () => void;
}

/**
 * A modal component for reviewing role completion requests in detail
 */
export const CompletionReviewModal: React.FC<CompletionReviewModalProps> = ({
  request,
  role,
  collaborationTitle,
  onConfirm,
  onReject,
  onClose
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [showRejectForm, setShowRejectForm] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [feedback, setFeedback] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'details' | 'evidence'>('details');

  const handleConfirm = async () => {
    setIsProcessing(true);
    setError(null);
    try {
      await onConfirm();
      // Close modal is handled by parent component
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to confirm completion');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      setError('Please provide a reason for rejection');
      return;
    }

    setIsProcessing(true);
    setError(null);
    try {
      await onReject(rejectReason);
      // Close modal is handled by parent component
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reject completion');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="max-h-[80vh] overflow-y-auto">
      {/* Header with role info */}
      <div className="mb-6">
        <h3 className={`text-xl font-semibold ${themeClasses.heading3}`}>
          {role.title}
        </h3>
        <p className={`mt-1 text-sm ${themeClasses.textMuted}`}>
          Part of collaboration: {collaborationTitle}
        </p>
      </div>

      {/* Requester info */}
      <div className="mb-6 flex items-center">
        <ProfileImageWithUser
          userId={request.requesterId}
          size="medium"
        />
        <div className="ml-4">
          <p className={`text-sm ${themeClasses.textMuted}`}>
            Requested completion on {new Date(request.createdAt.toDate()).toLocaleDateString()}
          </p>
        </div>
      </div>

      {/* Tab navigation */}
      <div className="flex border-b mb-6">
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === 'details'
              ? `border-b-2 ${themeClasses.borderAccent} ${themeClasses.textAccent}`
              : themeClasses.textMuted
          }`}
          onClick={() => setActiveTab('details')}
        >
          Details
        </button>
        <button
          className={`px-4 py-2 font-medium text-sm ${
            activeTab === 'evidence'
              ? `border-b-2 ${themeClasses.borderAccent} ${themeClasses.textAccent}`
              : themeClasses.textMuted
          }`}
          onClick={() => setActiveTab('evidence')}
        >
          Evidence
          {request.evidence && request.evidence.length > 0 && (
            <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              {request.evidence.length}
            </span>
          )}
        </button>
      </div>

      {/* Tab content */}
      <AnimatePresence mode="wait">
        {activeTab === 'details' ? (
          <motion.div
            key="details"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            <div className="mb-6">
              <h4 className={`text-sm font-medium ${themeClasses.heading5} mb-2`}>Completion Notes</h4>
              <div className={`p-4 rounded-md ${themeClasses.cardAlt}`}>
                <p className={themeClasses.text}>{request.notes}</p>
              </div>
            </div>

            <div className="mb-6">
              <h4 className={`text-sm font-medium ${themeClasses.heading5} mb-2`}>Required Skills</h4>
              <div className="flex flex-wrap gap-2">
                {role.requiredSkills?.map(skill => (
                  <span
                    key={skill.name}
                    className={`px-3 py-1 text-sm rounded-full ${themeClasses.badgePrimary}`}
                  >
                    {skill.name} ({skill.level})
                  </span>
                ))}
              </div>
            </div>

            {!showRejectForm && (
              <div className="mb-6">
                <h4 className={`text-sm font-medium ${themeClasses.heading5} mb-2`}>Feedback (Optional)</h4>
                <textarea
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md ${themeClasses.input}`}
                  rows={3}
                  placeholder="Provide feedback on the completed work..."
                />
              </div>
            )}
          </motion.div>
        ) : (
          <motion.div
            key="evidence"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {request.evidence && request.evidence.length > 0 ? (
              <div className="mb-6">
                <EvidenceGallery
                  evidence={request.evidence}
                  title="Completion Evidence"
                  expanded={true}
                />
              </div>
            ) : (
              <div className={`p-8 text-center ${themeClasses.cardAlt} rounded-md`}>
                <FileTextIcon className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-600 mb-4" />
                <p className={`${themeClasses.textMuted}`}>No evidence was submitted with this completion request.</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md dark:bg-red-900/20 dark:text-red-300">
          {error}
        </div>
      )}

      {showRejectForm ? (
        <div className="mt-6">
          <h4 className={`text-sm font-medium ${themeClasses.heading5} mb-2`}>Rejection Reason</h4>
          <textarea
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md ${themeClasses.input}`}
            rows={3}
            placeholder="Please provide a reason for rejecting this completion request..."
            required
          />
          <div className="mt-4 flex justify-end space-x-3">
            <button
              onClick={() => setShowRejectForm(false)}
              disabled={isProcessing}
              className={`px-4 py-2 text-sm border ${themeClasses.secondaryButton} ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Cancel
            </button>
            <button
              onClick={handleReject}
              disabled={isProcessing}
              className={`px-4 py-2 text-sm bg-red-600 text-white hover:bg-red-700 rounded-md ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              Confirm Rejection
            </button>
          </div>
        </div>
      ) : (
        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={onClose}
            disabled={isProcessing}
            className={`px-4 py-2 text-sm flex items-center ${themeClasses.secondaryButton} ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back
          </button>
          <button
            onClick={() => setShowRejectForm(true)}
            disabled={isProcessing}
            className={`px-4 py-2 text-sm flex items-center border ${themeClasses.dangerButton} ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <XCircleIcon className="h-4 w-4 mr-1" />
            Reject
          </button>
          <button
            onClick={handleConfirm}
            disabled={isProcessing}
            className={`px-4 py-2 text-sm flex items-center ${themeClasses.primaryButton} ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <CheckCircleIcon className="h-4 w-4 mr-1" />
            Confirm Completion
          </button>
        </div>
      )}
    </div>
  );
};

export default CompletionReviewModal;
