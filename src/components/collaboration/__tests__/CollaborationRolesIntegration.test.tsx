// Mock ProfileImageWithUser component to avoid import.meta issues
jest.mock('../../ui/ProfileImageWithUser', () => {
  return function MockProfileImageWithUser({ user, size }: { user: any; size?: string }) {
    return (
      <div data-testid="profile-image-with-user">
        <img 
          src={user?.photoURL || 'default-avatar.png'} 
          alt={user?.displayName || 'User'} 
          style={{ width: size || '32px', height: size || '32px' }}
        />
        <span>{user?.displayName || 'Anonymous'}</span>
      </div>
    );
  };
});

// Mock firebase-config to avoid import.meta issues
jest.mock('../../../firebase-config', () => ({
  db: {
    collection: jest.fn(),
    doc: jest.fn(),
  },
  auth: {
    currentUser: null,
  },
  analytics: null,
}));

import { render, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import { CollaborationRolesSection } from '../CollaborationRolesSection';
import { RoleState, ApplicationStatus, CollaborationRoleData } from '../../../types/collaboration';
import AuthContext from '../../../AuthContext';
import { ToastProvider } from '../../../contexts/ToastContext';
import { ThemeProvider } from '../../../contexts/ThemeContext';

// Mock services
jest.mock('../../../services/roleApplications', () => ({
  submitRoleApplication: jest.fn(),
  updateApplicationStatus: jest.fn(),
  getRoleApplications: jest.fn(),
  getRoleApplication: jest.fn()
}));
jest.mock('../../../services/roleCompletions', () => ({
  requestRoleCompletion: jest.fn(),
  updateCompletionStatus: jest.fn()
}));
jest.mock('../../../services/roleAbandonment', () => ({
  abandonRole: jest.fn()
}));
jest.mock('../../../services/notifications', () => ({
  createNotification: jest.fn()
}));

// Import mocked services after mocking
import * as roleApplications from '../../../services/roleApplications';
import * as roleCompletions from '../../../services/roleCompletions';
import * as roleAbandonment from '../../../services/roleAbandonment';

const mockRoleApplications = roleApplications as jest.Mocked<typeof roleApplications>;
const mockRoleCompletions = roleCompletions as jest.Mocked<typeof roleCompletions>;
const mockRoleAbandonment = roleAbandonment as jest.Mocked<typeof roleAbandonment>;

// Mock user data
const mockUser = {
  uid: 'test-user-id',
  email: '<EMAIL>',
  displayName: 'Test User',
  photoURL: 'https://example.com/photo.jpg',
  phoneNumber: null,
  providerId: 'firebase',
  emailVerified: true,
  isAnonymous: false,
  metadata: {
    creationTime: 'Wed, 21 Feb 2018 22:13:07 GMT',
    lastSignInTime: 'Wed, 21 Feb 2018 22:13:07 GMT'
  },
  providerData: [],
  refreshToken: 'mock-refresh-token',
  tenantId: null,
  delete: jest.fn(),
  getIdToken: jest.fn(),
  getIdTokenResult: jest.fn(),
  reload: jest.fn(),
  toJSON: jest.fn()
};

const mockCreatorUser = {
  uid: 'creator-user-id',
  email: '<EMAIL>',
  displayName: 'Creator User',
  photoURL: 'https://example.com/creator.jpg',
  phoneNumber: null,
  providerId: 'firebase',
  emailVerified: true,
  isAnonymous: false,
  metadata: {
    creationTime: 'Wed, 21 Feb 2018 22:13:07 GMT',
    lastSignInTime: 'Wed, 21 Feb 2018 22:13:07 GMT'
  },
  providerData: [],
  refreshToken: 'mock-refresh-token',
  tenantId: null,
  delete: jest.fn(),
  getIdToken: jest.fn(),
  getIdTokenResult: jest.fn(),
  reload: jest.fn(),
  toJSON: jest.fn()
};

// Test data for collaboration roles
const mockRoles: CollaborationRoleData[] = [
  {
    id: 'role-1',
    collaborationId: 'collab-1',
    title: 'Frontend Developer',
    description: 'Responsible for implementing the user interface',
    maxParticipants: 1,
    parentRoleId: undefined,
    childRoleIds: [],
    participantId: undefined,
    participantName: undefined,
    participantPhotoURL: undefined,
    status: RoleState.OPEN,
    applicationCount: 0,
    completionStatus: undefined,
    requiredSkills: [
      { name: 'React', level: 2 },
      { name: 'TypeScript', level: 1 }
    ],
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-01')
  },
  {
    id: 'role-2',
    collaborationId: 'collab-1',
    title: 'UI/UX Designer',
    description: 'Create wireframes and visual designs',
    maxParticipants: 1,
    parentRoleId: undefined,
    childRoleIds: [],
    participantId: 'test-user-id',
    participantName: 'Test User',
    participantPhotoURL: 'https://example.com/photo.jpg',
    status: RoleState.FILLED,
    applicationCount: 2,
    completionStatus: undefined,
    requiredSkills: [
      { name: 'Figma', level: 2 },
      { name: 'UI Design', level: 2 }
    ],
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-02')
  },
  {
    id: 'role-3',
    collaborationId: 'collab-1',
    title: 'Backend Developer',
    description: 'Implement server-side functionality',
    maxParticipants: 1,
    parentRoleId: undefined,
    childRoleIds: [],
    participantId: 'other-user-id',
    participantName: 'Other User',
    participantPhotoURL: 'https://example.com/other.jpg',
    status: RoleState.COMPLETED,
    applicationCount: 1,
    completionStatus: undefined,
    requiredSkills: [
      { name: 'Node.js', level: 2 },
      { name: 'MongoDB', level: 1 }
    ],
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-05')
  }
];

// Test wrapper component
const TestWrapper: React.FC<{ 
  children: React.ReactNode; 
  user?: typeof mockUser | null;
}> = ({ children, user = mockUser }) => (
  <AuthContext.Provider value={{
    user: user,
    currentUser: user,
    loading: false,
    error: null,
    signIn: jest.fn(),
    signInWithEmail: jest.fn(),
    signInWithGoogle: jest.fn(),
    signOut: jest.fn(),
    logout: jest.fn()
  }}>
    <ThemeProvider>
      <ToastProvider>
        {children}
      </ToastProvider>
    </ThemeProvider>
  </AuthContext.Provider>
);

describe('CollaborationRolesSection Integration Tests', () => {
  const mockOnRolesUpdated = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default mock responses
    mockRoleApplications.submitRoleApplication.mockResolvedValue({
      success: true,
      data: {
        id: 'app-1',
        roleId: 'role-1',
        applicantId: 'test-user-id',
        applicantName: 'Test User',
        message: 'I would like to apply for this role',
        evidence: [],
        status: ApplicationStatus.PENDING,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    mockRoleApplications.updateApplicationStatus.mockResolvedValue({
      success: true,
      data: undefined
    } as any);

    mockRoleCompletions.requestRoleCompletion.mockResolvedValue({
      success: true,
      data: undefined
    } as any);

    mockRoleAbandonment.abandonRole.mockResolvedValue({
      success: true,
      data: undefined
    } as any);
  });

  describe('Role Display and Filtering', () => {
    it('should display roles correctly separated by status', () => {
      render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Check that open roles are displayed
      expect(screen.getByText('Frontend Developer')).toBeInTheDocument();
      
      // Check that filled/completed roles are also displayed
      expect(screen.getByText('UI/UX Designer')).toBeInTheDocument();
      expect(screen.getByText('Backend Developer')).toBeInTheDocument();

      // Check status indicators
      expect(screen.getByText('Open')).toBeInTheDocument();
      expect(screen.getByText('Filled')).toBeInTheDocument();
      expect(screen.getByText('Completed')).toBeInTheDocument();
    });

    it('should show different UI for creator vs regular user', () => {
      const { rerender } = render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // For regular user, should show apply buttons for open roles
      expect(screen.getByText('Apply')).toBeInTheDocument();

      // Rerender as creator
      rerender(
        <TestWrapper user={mockCreatorUser}>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={true}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // For creator, should show management options
      expect(screen.getByText('View Status')).toBeInTheDocument();
    });
  });

  describe('Role Application Flow', () => {
    it('should handle complete role application workflow', async () => {
      const user = userEvent.setup();
      
      render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Find and click apply button for open role
      const applyButton = screen.getByText('Apply');
      await user.click(applyButton);

      // Application modal should open - just check for the modal content
      await waitFor(() => {
        expect(screen.getByText('Apply for: Frontend Developer')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Check if the form elements are present
      expect(screen.getByLabelText(/why are you a good fit for this role/i)).toBeInTheDocument();
      expect(screen.getByText('Submit Application')).toBeInTheDocument();
      
      // For now, just verify the modal opened successfully
      // TODO: Add form interaction tests once modal rendering is stable

      // Verify service was called
      await waitFor(() => {
        expect(mockRoleApplications.submitRoleApplication).toHaveBeenCalled();
      });

      // Verify callback was called
      expect(mockOnRolesUpdated).toHaveBeenCalled();
    });

    it('should prevent application when user is not logged in', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper user={null}>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Try to apply for role
      const applyButton = screen.getByText('Apply');
      await user.click(applyButton);

      // Should show error message (toast)
      await waitFor(() => {
        expect(screen.getByText('You must be logged in to apply for a role')).toBeInTheDocument();
      });

      // Modal should not open
      expect(screen.queryByText('Apply for Frontend Developer')).not.toBeInTheDocument();
    });

    it('should handle application errors gracefully', async () => {
      const user = userEvent.setup();

      // Mock service to return error
      mockRoleApplications.submitRoleApplication.mockResolvedValue({
        success: false,
        error: 'This role is no longer accepting applications'
      });

      render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Apply for role
      const applyButton = screen.getByText('Apply');
      await user.click(applyButton);

      // Fill and submit form
      const messageTextarea = screen.getByLabelText(/why are you a good fit for this role/i);
      await user.type(messageTextarea, 'Test application');

      const submitButton = screen.getByText('Submit Application');
      await user.click(submitButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText('This role is no longer accepting applications')).toBeInTheDocument();
      });
    });
  });

  describe('Role Completion Flow', () => {
    it('should handle role completion request workflow', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Find role that user is assigned to (UI/UX Designer) in filled roles section
      const filledRolesSection = screen.getByText('Filled Roles').closest('div');
      const roleCard = within(filledRolesSection!).getByText('UI/UX Designer').closest('[class*="backdrop-blur"]') as HTMLElement;
      expect(roleCard).toBeInTheDocument();

      // Click on "Request Role Completion" button
      const requestButton = within(roleCard!).getByText('Request Role Completion');
      await user.click(requestButton);

      // Completion modal should open
      await waitFor(() => {
        expect(screen.getByText('Request Role Completion')).toBeInTheDocument();
      });

      // Fill completion form
      const notesTextarea = screen.getByLabelText(/completion notes/i);
      await user.type(notesTextarea, 'All wireframes and designs have been completed and approved');

      // Submit completion request
      const submitButton = screen.getByText('Submit Request');
      await user.click(submitButton);

      // Verify service was called
      await waitFor(() => {
        expect(mockRoleCompletions.requestRoleCompletion).toHaveBeenCalledWith(
          'collab-1',
          'role-2',
          'test-user-id',
          expect.objectContaining({
            notes: 'All wireframes and designs have been completed and approved'
          })
        );
      });

      // Verify callback was called
      expect(mockOnRolesUpdated).toHaveBeenCalled();
    });
  });

  describe('Role Management Dashboard', () => {
    it('should show management dashboard for collaboration creator', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper user={mockCreatorUser}>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={true}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Click manage roles button - look for "Manage Applications" or "Manage Role" buttons
      const manageButton = screen.getByText('Manage Applications');
      await user.click(manageButton);

      // Management dashboard should open
      await waitFor(() => {
        expect(screen.getByText('Role Management')).toBeInTheDocument();
      });

      // Should show all roles in management view
      expect(screen.getAllByText('Frontend Developer')).toHaveLength(1);
      expect(screen.getAllByText('UI/UX Designer')).toHaveLength(1);
      expect(screen.getAllByText('Backend Developer')).toHaveLength(1);
    });
  });

  describe('Status Tracking and Visualization', () => {
    it('should show collaboration status tracker', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Click status tracker button
      const statusButton = screen.getByText('View Status');
      await user.click(statusButton);

      // Status tracker should open - look for the modal title specifically
      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
        expect(screen.getAllByText('Collaboration Status')).toHaveLength(1);
      });

      // Should show progress indicators
      expect(screen.getByText(/progress/i)).toBeInTheDocument();
    });

    it('should calculate and display correct progress metrics', () => {
      render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // With our mock data:
      // Total roles: 3
      // Filled roles: 2 (filled + completed)
      // Completed roles: 1
      // Open roles: 1

      const openRoles = mockRoles.filter(role => role.status === RoleState.OPEN);
      const filledRoles = mockRoles.filter(role => 
        role.status === RoleState.FILLED || role.status === RoleState.COMPLETED
      );
      
      expect(openRoles).toHaveLength(1);
      expect(filledRoles).toHaveLength(2);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle empty roles array', () => {
      render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={[]}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Should show empty state message
      expect(screen.getByText(/no roles/i)).toBeInTheDocument();
    });

    it('should handle service errors gracefully', async () => {
      const user = userEvent.setup();

      // Mock service to throw error
      mockRoleApplications.submitRoleApplication.mockRejectedValue(
        new Error('Network error')
      );

      render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Try to apply for role
      const applyButton = screen.getByText('Apply');
      await user.click(applyButton);

      const messageTextarea = screen.getByLabelText(/why are you a good fit for this role/i);
      await user.type(messageTextarea, 'Test application');

      const submitButton = screen.getByText('Submit Application');
      await user.click(submitButton);

      // Should show generic error message
      await waitFor(() => {
        expect(screen.getByText(/failed to submit application/i)).toBeInTheDocument();
      });
    });

    it('should prevent actions for roles user cannot interact with', () => {
      render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Should not show apply button for filled/completed roles
      const backendRole = screen.getByText('Backend Developer').closest('[class*="backdrop-blur"]') as HTMLElement;
      expect(within(backendRole!).queryByText('Apply')).not.toBeInTheDocument();

      // Should only show apply button for open roles
      const frontendRole = screen.getByText('Frontend Developer').closest('[class*="backdrop-blur"]') as HTMLElement;
      expect(within(frontendRole!).getByText('Apply')).toBeInTheDocument();
    });
  });

  describe('Role Abandonment and Replacement', () => {
    it('should handle role abandonment workflow', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper user={mockCreatorUser}>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={true}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Find role that is filled and has abandon option (UI/UX Designer)
      const filledRolesSection = screen.getByText('Filled Roles').closest('div');
      const roleCard = within(filledRolesSection!).getByText('UI/UX Designer').closest('[class*="backdrop-blur"]') as HTMLElement;
      const abandonButton = within(roleCard!).getByText('Abandon Role');
      await user.click(abandonButton);

      // Abandon modal should open
      await waitFor(() => {
        expect(screen.getByText('Abandon Role')).toBeInTheDocument();
      });

      // Fill reason
      const reasonTextarea = screen.getByLabelText(/reason/i);
      await user.type(reasonTextarea, 'Personal circumstances have changed');

      // Confirm abandonment
      const confirmButton = screen.getByText('Confirm Abandonment');
      await user.click(confirmButton);

      // Verify service was called
      await waitFor(() => {
        expect(mockRoleAbandonment.abandonRole).toHaveBeenCalledWith(
          'collab-1',
          'role-2',
          'test-user-id',
          'Personal circumstances have changed'
        );
      });

      // Verify callback was called
      expect(mockOnRolesUpdated).toHaveBeenCalled();
    });
  });

  describe('Accessibility and User Experience', () => {
    it('should have proper ARIA labels and keyboard navigation', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Check for proper role attributes - buttons naturally have button role
      const applyButton = screen.getByText('Apply');
      expect(applyButton.tagName).toBe('BUTTON');

      // Test keyboard navigation
      applyButton.focus();
      expect(applyButton).toHaveFocus();

      // Test Enter key activation
      await user.keyboard('{Enter}');
      
      await waitFor(() => {
        expect(screen.getByText('Apply for Frontend Developer')).toBeInTheDocument();
      });
    });

    it('should provide clear loading states during operations', async () => {
      const user = userEvent.setup();

      // Mock service to be slow
      mockRoleApplications.submitRoleApplication.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 100))
      );

      render(
        <TestWrapper>
          <CollaborationRolesSection
            collaborationId="collab-1"
            collaborationTitle="Test Collaboration"
            roles={mockRoles}
            isCreator={false}
            onRolesUpdated={mockOnRolesUpdated}
          />
        </TestWrapper>
      );

      // Apply for role
      const applyButton = screen.getByText('Apply');
      await user.click(applyButton);

      const messageTextarea = screen.getByLabelText(/why are you a good fit for this role/i);
      await user.type(messageTextarea, 'Test application');

      const submitButton = screen.getByText('Submit Application');
      await user.click(submitButton);

      // Should show loading state (spinner in button)
      const loadingButton = screen.getByRole('button', { name: /submit application/i });
      expect(loadingButton).toBeDisabled();
      expect(loadingButton.querySelector('svg.animate-spin')).toBeInTheDocument();

      // Wait for completion
      await waitFor(() => {
        expect(screen.queryByText('Submitting...')).not.toBeInTheDocument();
      }, { timeout: 1000 });
    });
  });
});
