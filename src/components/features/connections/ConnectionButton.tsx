import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../AuthContext';
import { getConnections, getConnectionRequests, updateConnectionStatus, removeConnection, Connection } from '../../../services/firestore';
import { useToast } from '../../../contexts/ToastContext';
import { Modal } from '../../ui/Modal';
import ConnectionRequestForm from './ConnectionRequestForm';
import { UserPlus, UserCheck, UserX, Loader2 } from '../../../utils/icons';
import { themeClasses } from '../../../utils/themeUtils';

interface ConnectionButtonProps {
  userId: string;
  userName: string;
  userPhotoURL?: string;
}

export const ConnectionButton: React.FC<ConnectionButtonProps> = ({
  userId,
  userName,
  userPhotoURL
}) => {
  const { currentUser } = useAuth();
  const { addToast } = useToast();

  const [connectionStatus, setConnectionStatus] = useState<string | null>(null);
  const [connectionId, setConnectionId] = useState<string | null>(null);
  const [direction, setDirection] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [showRequestForm, setShowRequestForm] = useState(false);

  useEffect(() => {
    if (currentUser && userId) {
      fetchConnectionStatus();
    }
  }, [currentUser, userId]);

  const fetchConnectionStatus = async () => {
    if (!currentUser) return;

    setLoading(true);

    try {
      // Check for an accepted connection
      const { data: connections, error: connectionsError } = await getConnections(currentUser.uid);
      if (connectionsError) throw connectionsError;

      const acceptedConnection = connections?.find(conn => 
        (conn.userId === currentUser.uid && conn.connectedUserId === userId) ||
        (conn.userId === userId && conn.connectedUserId === currentUser.uid)
      );

      if (acceptedConnection) {
        setConnectionStatus('accepted');
        setConnectionId(acceptedConnection.id || null);
        setDirection(acceptedConnection.userId === currentUser.uid ? 'sent' : 'received');
      } else {
        // Check for a pending request sent by the current user
        const { data: sentRequests, error: sentRequestsError } = await getConnectionRequests(currentUser.uid);
        if (sentRequestsError) throw sentRequestsError;

        const sentPending = sentRequests?.find(req => req.connectedUserId === userId);

        if (sentPending) {
          setConnectionStatus('pending');
          setConnectionId(sentPending.id || null);
          setDirection('sent');
        } else {
          // Check for a pending request received by the current user
          const { data: receivedRequests, error: receivedRequestsError } = await getConnectionRequests(currentUser.uid);
          if (receivedRequestsError) throw receivedRequestsError;

          const receivedPending = receivedRequests?.find(req => req.userId === userId);

          if (receivedPending) {
            setConnectionStatus('pending');
            setConnectionId(receivedPending.id || null);
            setDirection('received');
          } else {
            // No connection or pending request found
            setConnectionStatus('none');
            setConnectionId(null);
            setDirection(null);
          }
        }
      }

    } catch (err: any) {
      console.error('Error checking connection status:', err);
      // Pass error message instead of the entire object
      addToast('error', err.message || 'Failed to check connection status');
    } finally {
      setLoading(false);
    }
  };

  const handleConnect = () => {
    setShowRequestForm(true);
  };

  const handleAccept = async () => {
    if (!connectionId) return;

    setLoading(true);

    try {
      const { error } = await updateConnectionStatus(connectionId, 'accepted');

      if (error) {
        // Pass error message instead of the entire object
        throw new Error(error.message);
      }

      setConnectionStatus('accepted');
      addToast('success', `You are now connected with ${userName}`);
    } catch (err: any) {
      addToast('error', err.message || 'Failed to accept connection request');
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async () => {
    if (!connectionId) return;

    setLoading(true);

    try {
      const { error } = await removeConnection(connectionId);

      if (error) {
        // Pass error message instead of the entire object
        throw new Error(error.message);
      }

      setConnectionStatus('none');
      setConnectionId(null);
      setDirection(null);
      addToast('success', 'Connection request rejected');
    } catch (err: any) {
      addToast('error', err.message || 'Failed to reject connection request');
    } finally {
      setLoading(false);
    }
  };

  const handleRemove = async () => {
    if (!connectionId) return;

    setLoading(true);

    try {
      const { error } = await removeConnection(connectionId);

      if (error) {
        // Pass error message instead of the entire object
        throw new Error(error.message);
      }

      setConnectionStatus('none');
      setConnectionId(null);
      setDirection(null);
      addToast('success', 'Connection removed');
    } catch (err: any) {
      addToast('error', err.message || 'Failed to remove connection');
    } finally {
      setLoading(false);
    }
  };

  const handleRequestSuccess = () => {
    setShowRequestForm(false);
    setConnectionStatus('pending');
    setDirection('sent');
    fetchConnectionStatus(); // Refresh the status
  };

  // Don't show the button if it's the current user's profile
  if (currentUser?.uid === userId) {
    return null;
  }

  if (loading) {
    return (
      <button
        disabled
        className="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-400 dark:bg-gray-600 focus:outline-none transition-colors duration-200"
        title="Loading..."
      >
        <Loader2 className="h-4 w-4 animate-spin" />
      </button>
    );
  }

  if (connectionStatus === 'accepted') {
    return (
      <div className="flex items-center space-x-2">
        <button
          className="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 dark:bg-green-700 dark:hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 group"
          disabled
          title="Connected with this user"
        >
          <UserCheck className="h-4 w-4" />
        </button>

        <button
          onClick={handleRemove}
          className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 group"
          title="Remove connection with this user"
        >
          <UserX className="h-4 w-4" />
        </button>
      </div>
    );
  }

  if (connectionStatus === 'pending' && direction === 'sent') {
    return (
      <button
        onClick={handleRemove}
        className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
        title="Cancel pending connection request"
      >
        <UserX className="h-4 w-4" />
      </button>
    );
  }

  if (connectionStatus === 'pending' && direction === 'received') {
    return (
      <div className="flex items-center space-x-2">
        <button
          onClick={handleAccept}
          className={`inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${themeClasses.primaryButton} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 ${themeClasses.transition}`}
          title="Accept connection request"
        >
          <UserCheck className="h-4 w-4" />
        </button>

        <button
          onClick={handleReject}
          className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
          title="Decline connection request"
        >
          <UserX className="h-4 w-4" />
        </button>
      </div>
    );
  }

  return (
    <>
      <button
        onClick={handleConnect}
        className={`inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${themeClasses.primaryButton} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 ${themeClasses.transition}`}
        title="Send a connection request"
      >
        <UserPlus className="h-4 w-4" />
      </button>

      <Modal
        isOpen={showRequestForm}
        onClose={() => setShowRequestForm(false)}
        title="Send Connection Request"
      >
        <ConnectionRequestForm
          receiverId={userId}
          receiverName={userName}
          receiverPhotoURL={userPhotoURL}
          onSuccess={handleRequestSuccess}
          onCancel={() => setShowRequestForm(false)}
        />
      </Modal>
    </>
  );
};

export default ConnectionButton;
