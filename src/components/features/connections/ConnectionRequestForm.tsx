import React, { useState } from 'react';
import { useAuth } from '../../../AuthContext';
import { createConnectionRequest } from '../../../services/firestore';
import { useToast } from '../../../contexts/ToastContext';
import { themeClasses } from '../../../utils/themeUtils';

interface ConnectionRequestFormProps {
  receiverId: string;
  receiverName: string;
  receiverPhotoURL?: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export const ConnectionRequestForm: React.FC<ConnectionRequestFormProps> = ({
  receiverId,
  receiverName,
  receiverPhotoURL,
  onSuccess,
  onCancel
}) => {
  const { currentUser, userProfile } = useAuth();
  const { addToast } = useToast();

  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser || !userProfile) {
      setError('You must be logged in to send a connection request');
      return;
    }

    if (currentUser.uid === receiverId) {
      setError('You cannot connect with yourself');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const connectionData = {
        senderId: currentUser.uid,
        senderName: userProfile.displayName || 'Anonymous',
        senderPhotoURL: userProfile.photoURL,
        receiverId,
        receiverName,
        receiverPhotoURL,
        message
      };

      const result = await createConnectionRequest(currentUser.uid, receiverId);

      if (result.error || !result.data) {
        throw new Error(result.error?.message || 'Failed to send connection request');
      }

      addToast('success', 'Connection request sent successfully');
      onSuccess();
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      addToast('error', err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4 transition-colors duration-200">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Connect with {receiverName}</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Send a connection request to start collaborating with this user.
        </p>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg transition-colors duration-200">
          {error}
        </div>
      )}

      <div>
        <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Message (Optional)
        </label>
        <textarea
          id="message"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          rows={4}
          className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
          placeholder="Introduce yourself and explain why you'd like to connect..."
        />
      </div>

      <div className="flex justify-end space-x-3 pt-2">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
        >
          Cancel
        </button>

        <button
          type="submit"
          disabled={loading}
          className={`inline-flex justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${themeClasses.primaryButton} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 ${themeClasses.transition}`}
        >
          {loading ? 'Sending...' : 'Send Request'}
        </button>
      </div>
    </form>
  );
};

export default ConnectionRequestForm;
