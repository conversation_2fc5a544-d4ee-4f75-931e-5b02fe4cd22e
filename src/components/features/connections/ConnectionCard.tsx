// To fix "Cannot find module 'react'": ensure 'react' and '@types/react' are installed (e.g., npm install react @types/react) and tsconfig.json is correctly configured.
import React from 'react';
import { Link } from 'react-router-dom';
import { Connection } from '../../../services/firestore';
import { cn } from '../../../utils/cn';
import { Calendar, MessageCircle, UserCheck, UserX } from 'lucide-react';
import ProfileHoverCard from '../../ui/ProfileHoverCard';
import ProfileImage from '../../ui/ProfileImage';
import JohnRobertsProfileImage from '../../ui/JohnRobertsProfileImage';
import { themeClasses } from '../../../utils/themeUtils';

interface ConnectionCardProps {
  connection: Connection;
  currentUserId: string;
  onAccept?: (connectionId: string) => void;
  onReject?: (connectionId: string) => void;
  onRemove?: (connectionId: string) => void;
  className?: string;
}

export const ConnectionCard: React.FC<ConnectionCardProps> = ({
  connection,
  currentUserId,
  onAccept,
  onReject,
  onRemove,
  className
}) => {
  // Assert additional properties on the connection object
  const typedConnection = connection as Connection & {
    senderId: string;
    receiverId: string;
    senderName: string;
    receiverName: string;
    senderPhotoURL?: string;
    receiverPhotoURL?: string;
    message?: string; // Add this line
  };

  // Determine if the current user is the sender or receiver
  const isSender = typedConnection.senderId === currentUserId;

  // Get the other user's details
  const otherUserId = isSender ? typedConnection.receiverId : typedConnection.senderId;
  const otherUserName = isSender ? typedConnection.receiverName : typedConnection.senderName;
  const otherUserPhoto = isSender ? typedConnection.receiverPhotoURL : typedConnection.senderPhotoURL;

  // Format date
  const formattedDate = new Date(typedConnection.createdAt.seconds * 1000).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });

  return (
    <div className={cn(
      `overflow-hidden rounded-lg border ${themeClasses.border} ${themeClasses.card} shadow-sm ${themeClasses.transition} hover:shadow-md hover:-translate-y-1 dark:hover:bg-neutral-700/70 dark:hover:shadow-[0_0_12px_rgba(251,146,60,0.15)]`,
      className
    )}>
      <div className="p-5">
        <div className="flex justify-between items-start">
          <ProfileHoverCard
            userId={otherUserId}
            displayName={otherUserName}
            photoURL={otherUserPhoto}
          >
            <div className="flex items-center">
              {otherUserId === 'TozfQg0dAHe4ToLyiSnkDqe3ECj2' ? (
                <JohnRobertsProfileImage
                  size="sm"
                  className="mr-3"
                />
              ) : (
                <ProfileImage
                  photoURL={otherUserPhoto}
                  profilePicture={otherUserPhoto}
                  displayName={otherUserName}
                  size="sm"
                  className="mr-3"
                />
              )}
              <div>
                <h3 className={`text-base font-medium ${themeClasses.text} hover:text-orange-600 dark:hover:text-orange-400`}>
                  {otherUserName}
                </h3>
                <div className={`flex items-center text-xs ${themeClasses.textMuted} mt-1`}>
                  <Calendar className="mr-1 h-3.5 w-3.5" />
                  <span>{typedConnection.status === 'pending' ? 'Requested' : 'Connected'} {formattedDate}</span>
                </div>
              </div>
            </div>
          </ProfileHoverCard>

          {typedConnection.status === 'pending' && !isSender && (
            <span className="inline-flex items-center rounded-full bg-yellow-100 dark:bg-yellow-900/30 px-2.5 py-0.5 text-xs font-medium text-yellow-800 dark:text-yellow-300">
              Pending
            </span>
          )}

          {typedConnection.status === 'pending' && isSender && (
            <span className="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/30 px-2.5 py-0.5 text-xs font-medium text-blue-800 dark:text-blue-300">
              Sent
            </span>
          )}

          {typedConnection.status === 'accepted' && (
            <span className="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/30 px-2.5 py-0.5 text-xs font-medium text-green-800 dark:text-green-300">
              Connected
            </span>
          )}
        </div>

        {typedConnection.message && (
          <div className="mt-4">
            <p className={`text-sm ${themeClasses.textMuted} whitespace-pre-line`}>
              {typedConnection.message}
            </p>
          </div>
        )}

        <div className="mt-4 flex justify-between items-center">
          <div>
            <Link
              to={`/profile/${otherUserId}`}
              className="text-sm text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300"
            >
              View Profile
            </Link>
          </div>

          <div className="flex space-x-2">
            {typedConnection.status === 'accepted' && (
              <>
                <Link
                  to={`/messages?userId=${otherUserId}`}
                  className={`inline-flex items-center px-3 py-1.5 border ${themeClasses.border} rounded-md shadow-sm text-sm font-medium ${themeClasses.text} ${themeClasses.card} hover:bg-gray-50 dark:hover:bg-gray-700 ${themeClasses.focus} ${themeClasses.transition}`}
                >
                  <MessageCircle className="mr-1.5 h-4 w-4" />
                  Message
                </Link>

                <button
                  onClick={() => typedConnection.id && onRemove?.(typedConnection.id)}
                  className={`inline-flex items-center px-3 py-1.5 border ${themeClasses.border} rounded-md shadow-sm text-sm font-medium ${themeClasses.text} ${themeClasses.card} hover:bg-gray-50 dark:hover:bg-gray-700 ${themeClasses.focus} ${themeClasses.transition}`}
                >
                  <UserX className="mr-1.5 h-4 w-4" />
                  Remove
                </button>
              </>
            )}

            {typedConnection.status === 'pending' && !isSender && (
              <>
                <button
                  onClick={() => typedConnection.id && onReject?.(typedConnection.id)}
                  className={`inline-flex items-center px-3 py-1.5 border ${themeClasses.border} rounded-md shadow-sm text-sm font-medium ${themeClasses.text} ${themeClasses.card} hover:bg-gray-50 dark:hover:bg-gray-700 ${themeClasses.focus} ${themeClasses.transition}`}
                >
                  <UserX className="mr-1.5 h-4 w-4" />
                  Decline
                </button>

                <button
                  onClick={() => typedConnection.id && onAccept?.(typedConnection.id)}
                  className={`inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white ${themeClasses.primaryButton} ${themeClasses.focus} ${themeClasses.transition}`}
                >
                  <UserCheck className="mr-1.5 h-4 w-4" />
                  Accept
                </button>
              </>
            )}

            {typedConnection.status === 'pending' && isSender && (
              <button
                onClick={() => typedConnection.id && onRemove?.(typedConnection.id)}
                className={`inline-flex items-center px-3 py-1.5 border ${themeClasses.border} rounded-md shadow-sm text-sm font-medium ${themeClasses.text} ${themeClasses.card} hover:bg-gray-50 dark:hover:bg-gray-700 ${themeClasses.focus} ${themeClasses.transition}`}
              >
                <UserX className="mr-1.5 h-4 w-4" />
                Cancel
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConnectionCard;
