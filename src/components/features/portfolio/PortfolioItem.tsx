// src/components/features/portfolio/PortfolioItem.tsx

import React, { useState } from 'react';
import {
  updatePortfolioItemVisibility,
  updatePortfolioItemFeatured,
  updatePortfolioItemPinned,
  updatePortfolioItemCategory,
  deletePortfolioItem
} from '../../../services/portfolio';
import { PortfolioItem } from '../../../types/portfolio';
import { motion } from 'framer-motion';
import { themeClasses } from '../../../utils/themeUtils';
import { Star, Pin, Eye, EyeOff, Settings, Trash2, Calendar, Users, Award, ExternalLink } from 'lucide-react';

interface PortfolioItemProps {
  item: PortfolioItem;
  isOwnProfile: boolean;
  isManaging: boolean;
  onChange?: () => void;
}

const CATEGORY_OPTIONS = [
  'Web Development',
  'Mobile Development',
  'Design',
  'Data Science',
  'Marketing',
  'Writing',
  'Business',
  'Education',
  'Other'
];

export const PortfolioItemComponent: React.FC<PortfolioItemProps> = ({
  item,
  isOwnProfile,
  isManaging,
  onChange
}) => {
  const [loading, setLoading] = useState(false);
  const [showEvidenceModal, setShowEvidenceModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState(false);
  const [expanded, setExpanded] = useState(false);

  const handleToggleVisibility = async () => {
    if (!isOwnProfile || loading) return;
    setLoading(true);
    try {
      await updatePortfolioItemVisibility(item.userId, item.id, !item.visible);
      onChange && onChange();
    } catch (error) {
      console.error('Failed to update visibility:', error);
    }
    setLoading(false);
  };

  const handleToggleFeatured = async () => {
    if (!isOwnProfile || loading) return;
    setLoading(true);
    try {
      await updatePortfolioItemFeatured(item.userId, item.id, !item.featured);
      onChange && onChange();
    } catch (error) {
      console.error('Failed to update featured status:', error);
    }
    setLoading(false);
  };

  const handleTogglePinned = async () => {
    if (!isOwnProfile || loading) return;
    setLoading(true);
    try {
      await updatePortfolioItemPinned(item.userId, item.id, !item.pinned);
      onChange && onChange();
    } catch (error) {
      console.error('Failed to update pinned status:', error);
    }
    setLoading(false);
  };

  const handleCategoryChange = async (newCategory: string) => {
    if (!isOwnProfile || loading) return;
    setLoading(true);
    try {
      await updatePortfolioItemCategory(item.userId, item.id, newCategory);
      setEditingCategory(false);
      onChange && onChange();
    } catch (error) {
      console.error('Failed to update category:', error);
    }
    setLoading(false);
  };

  const handleDelete = async () => {
    if (!isOwnProfile || loading) return;
    if (!window.confirm('Are you sure you want to delete this portfolio item? This action cannot be undone.')) return;
    setLoading(true);
    try {
      await deletePortfolioItem(item.userId, item.id);
      onChange && onChange();
    } catch (error) {
      console.error('Failed to delete item:', error);
    }
    setLoading(false);
  };

  const formatDate = (date: any) => {
    if (!date) return 'Unknown date';
    const d = date.toDate ? date.toDate() : new Date(date);
    return d.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
      className={`
        portfolio-item relative backdrop-blur-md bg-white/70 dark:bg-gray-800/60
        border border-white/20 dark:border-gray-700/30 rounded-xl shadow-lg
        transition-all duration-300 hover:shadow-xl hover:-translate-y-1
        hover:bg-white/80 dark:hover:bg-gray-800/70
        ${item.featured ? 'ring-2 ring-orange-400/50 shadow-orange-200/20 dark:shadow-orange-900/20' : ''}
        ${item.pinned ? 'bg-blue-50/80 dark:bg-blue-900/30' : ''}
      `}
    >
      {/* Badges */}
      <div className="absolute top-4 left-4 flex gap-2 z-10">
        {item.featured && (
          <span className="inline-flex items-center gap-1 bg-gradient-to-r from-orange-100 to-orange-200 dark:from-orange-900/40 dark:to-orange-800/40 text-orange-800 dark:text-orange-300 text-xs font-medium px-3 py-1.5 rounded-full backdrop-blur-sm border border-orange-200/50 dark:border-orange-800/50">
            <Star className="w-3 h-3" />
            Featured
          </span>
        )}
        {item.pinned && (
          <span className="inline-flex items-center gap-1 bg-gradient-to-r from-blue-100 to-blue-200 dark:from-blue-900/40 dark:to-blue-800/40 text-blue-800 dark:text-blue-300 text-xs font-medium px-3 py-1.5 rounded-full backdrop-blur-sm border border-blue-200/50 dark:border-blue-800/50">
            <Pin className="w-3 h-3" />
            Pinned
          </span>
        )}
        {!item.visible && isOwnProfile && (
          <span className="inline-flex items-center gap-1 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800/40 dark:to-gray-700/40 text-gray-600 dark:text-gray-400 text-xs font-medium px-3 py-1.5 rounded-full backdrop-blur-sm border border-gray-200/50 dark:border-gray-700/50">
            <EyeOff className="w-3 h-3" />
            Hidden
          </span>
        )}
      </div>

      {/* Management Controls */}
      {isOwnProfile && isManaging && (
        <div className="absolute top-3 right-3 flex gap-1">
          <div className="bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 p-2">
            <div className="flex flex-col gap-1">
              <button
                className={`text-xs px-2 py-1 rounded transition-colors ${
                  item.visible 
                    ? 'bg-green-100 text-green-700 hover:bg-green-200' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={handleToggleVisibility}
                disabled={loading}
                title={item.visible ? 'Hide from profile' : 'Show on profile'}
              >
                {item.visible ? '👁️ Visible' : '👁️‍🗨️ Hidden'}
              </button>
              <button
                className={`text-xs px-2 py-1 rounded transition-colors ${
                  item.featured 
                    ? 'bg-orange-100 text-orange-700 hover:bg-orange-200' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={handleToggleFeatured}
                disabled={loading}
                title={item.featured ? 'Remove from featured' : 'Mark as featured'}
              >
                {item.featured ? '⭐ Featured' : '⭐ Feature'}
              </button>
              <button
                className={`text-xs px-2 py-1 rounded transition-colors ${
                  item.pinned 
                    ? 'bg-blue-100 text-blue-700 hover:bg-blue-200' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
                onClick={handleTogglePinned}
                disabled={loading}
                title={item.pinned ? 'Unpin from top' : 'Pin to top'}
              >
                {item.pinned ? '📌 Pinned' : '📌 Pin'}
              </button>
              <button
                className="text-xs px-2 py-1 rounded bg-red-100 text-red-700 hover:bg-red-200 transition-colors"
                onClick={handleDelete}
                disabled={loading}
                title="Delete portfolio item"
              >
                🗑️ Delete
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="p-6">
        {/* Header */}
        <div className="mb-4">
          <div className="flex items-start justify-between">
            <div className="flex-1 mr-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {item.title}
              </h3>
              <div className="flex items-center gap-3 text-sm text-gray-500 dark:text-gray-400 mb-2">
                <span className="flex items-center gap-1">
                  {item.sourceType === 'trade' ? '🤝' : '👥'} 
                  {item.sourceType === 'trade' ? 'Trade' : 'Collaboration'}
                </span>
                <span>•</span>
                <span>📅 {formatDate(item.completedAt)}</span>
                {item.category && (
                  <>
                    <span>•</span>
                    <span className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
                      {item.category}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Category Editing */}
          {isOwnProfile && isManaging && editingCategory && (
            <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Category
              </label>
              <select
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-orange-500 focus:border-orange-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                value={item.category || 'Other'}
                onChange={(e) => handleCategoryChange(e.target.value)}
                disabled={loading}
              >
                {CATEGORY_OPTIONS.map(cat => (
                  <option key={cat} value={cat}>{cat}</option>
                ))}
              </select>
              <div className="flex gap-2 mt-2">
                <button
                  className="text-xs px-3 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-500"
                  onClick={() => setEditingCategory(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Description */}
        <div className="mb-4">
          <p className={`text-gray-700 dark:text-gray-300 ${
            expanded ? '' : 'line-clamp-3'
          }`}>
            {item.description}
          </p>
          {item.description && item.description.length > 150 && (
            <button
              className="text-orange-600 hover:text-orange-700 text-sm mt-1"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? 'Show less' : 'Show more'}
            </button>
          )}
        </div>

        {/* Skills */}
        {item.skills && item.skills.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Skills</h4>
            <div className="flex flex-wrap gap-2">
              {item.skills.map((skill, index) => (
                <span
                  key={index}
                  className="bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 px-3 py-1 rounded-full text-sm font-medium"
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Collaborators */}
        {item.collaborators && item.collaborators.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Collaborators</h4>
            <div className="flex flex-wrap gap-2">
              {item.collaborators.map((collaborator, index) => (
                <div
                  key={index}
                  className="flex items-center gap-2 bg-gray-100 dark:bg-gray-700 px-3 py-2 rounded-lg"
                >
                  {collaborator.photoURL && (
                    <img
                      src={collaborator.photoURL}
                      alt={collaborator.name}
                      className="w-6 h-6 rounded-full"
                    />
                  )}
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {collaborator.name}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    ({collaborator.role})
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Evidence */}
        {item.evidence && item.evidence.length > 0 && (
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-900 dark:text-white">Evidence</h4>
              <button
                className="text-orange-600 hover:text-orange-700 text-sm"
                onClick={() => setShowEvidenceModal(true)}
              >
                View all ({item.evidence.length})
              </button>
            </div>
            <div className="flex gap-2 overflow-x-auto">
              {item.evidence.slice(0, 3).map((evidence, index) => (
                <div
                  key={index}
                  className="flex-shrink-0 w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center"
                >
                  {evidence.type === 'image' ? (
                    <img
                      src={evidence.url}
                      alt="Evidence"
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <div className="text-center">
                      <div className="text-lg">📄</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {evidence.type}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        {isOwnProfile && isManaging && !editingCategory && (
          <div className="flex gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              className="text-sm px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
              onClick={() => setEditingCategory(true)}
            >
              Edit Category
            </button>
          </div>
        )}
      </div>

      {/* Evidence Modal */}
      {showEvidenceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl max-h-3/4 overflow-y-auto p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Evidence</h3>
              <button
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                onClick={() => setShowEvidenceModal(false)}
              >
                ✕
              </button>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {item.evidence?.map((evidence, index) => (
                <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  {evidence.type === 'image' ? (
                    <img
                      src={evidence.url}
                      alt="Evidence"
                      className="w-full h-40 object-cover rounded"
                    />
                  ) : (
                    <div className="h-40 flex items-center justify-center bg-gray-100 dark:bg-gray-700 rounded">
                      <div className="text-center">
                        <div className="text-2xl mb-2">📄</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {evidence.type}
                        </div>
                      </div>
                    </div>
                  )}
                  {evidence.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                      {evidence.description}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default PortfolioItemComponent;