// src/components/features/portfolio/PortfolioTab.tsx

import React, { useEffect, useState, useMemo } from 'react';
import { getUserPortfolioItems } from '../../../services/portfolio';
import { PortfolioItem } from '../../../types/portfolio';
import PortfolioItemComponent from './PortfolioItem';
import { motion, AnimatePresence } from 'framer-motion';
import { themeClasses } from '../../../utils/themeUtils';
import { Grid3X3, List, Filter, Settings, Star, Pin } from 'lucide-react';

interface PortfolioTabProps {
  userId: string;
  isOwnProfile: boolean;
}

export const PortfolioTab: React.FC<PortfolioTabProps> = ({ userId, isOwnProfile }) => {
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filter, setFilter] = useState<'all' | 'trades' | 'collaborations' | 'featured'>('all');
  const [loading, setLoading] = useState(false);
  const [isManaging, setIsManaging] = useState(false);

  const fetchPortfolio = async () => {
    setLoading(true);
    const options = !isOwnProfile ? { onlyVisible: true } : {};
    const items = await getUserPortfolioItems(userId, options);
    setPortfolioItems(items);
    setLoading(false);
  };

  useEffect(() => {
    fetchPortfolio();
    // eslint-disable-next-line
  }, [userId, isOwnProfile]);

  const filteredItems = useMemo(() => {
    switch (filter) {
      case 'trades':
        return portfolioItems.filter(item => item.sourceType === 'trade');
      case 'collaborations':
        return portfolioItems.filter(item => item.sourceType === 'collaboration');
      case 'featured':
        return portfolioItems.filter(item => item.featured);
      default:
        return portfolioItems;
    }
  }, [portfolioItems, filter]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="portfolio-container"
    >
      {/* Enhanced Header */}
      <div className="portfolio-header mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/30">
              <Star className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <h2 className={`text-xl font-semibold ${themeClasses.heading2}`}>
                Portfolio
              </h2>
              <p className={`text-sm ${themeClasses.textMuted}`}>
                {portfolioItems.length} {portfolioItems.length === 1 ? 'item' : 'items'}
                {filteredItems.length !== portfolioItems.length && ` • ${filteredItems.length} shown`}
              </p>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-3">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-white/20 dark:border-gray-700/30 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-all duration-200 ${
                  viewMode === 'grid'
                    ? 'bg-orange-500 text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
                title="Grid view"
              >
                <Grid3X3 className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-all duration-200 ${
                  viewMode === 'list'
                    ? 'bg-orange-500 text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
                title="List view"
              >
                <List className="w-4 h-4" />
              </button>
            </div>

            {/* Filter Dropdown */}
            <div className="relative">
              <select
                value={filter}
                onChange={e => setFilter(e.target.value as any)}
                className={`appearance-none bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-white/20 dark:border-gray-700/30 rounded-lg px-3 py-2 pr-8 text-sm ${themeClasses.text} focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200`}
              >
                <option value="all">All Items</option>
                <option value="trades">Trades</option>
                <option value="collaborations">Collaborations</option>
                <option value="featured">Featured</option>
              </select>
              <Filter className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
            </div>

            {/* Management Toggle */}
            {isOwnProfile && (
              <button
                onClick={() => setIsManaging(v => !v)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                  isManaging
                    ? 'bg-blue-500 text-white shadow-md hover:bg-blue-600'
                    : 'bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-white/20 dark:border-gray-700/30 text-gray-700 dark:text-gray-300 hover:bg-white/70 dark:hover:bg-gray-800/70'
                }`}
                title={isManaging ? 'Exit management mode' : 'Manage portfolio'}
              >
                <Settings className="w-4 h-4" />
                <span className="hidden sm:inline">{isManaging ? 'Done' : 'Manage'}</span>
              </button>
            )}
          </div>
        </div>
      </div>
      {loading ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="py-12 text-center"
        >
          <div className="inline-flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500"></div>
            <span>Loading portfolio...</span>
          </div>
        </motion.div>
      ) : filteredItems.length === 0 ? (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="backdrop-blur-md bg-white/70 dark:bg-gray-800/60 border border-white/20 dark:border-gray-700/30 rounded-xl shadow-lg p-12 text-center"
        >
          <div className="mx-auto w-16 h-16 mb-6 rounded-full bg-gradient-to-br from-orange-100 to-orange-200 dark:from-orange-900/30 dark:to-orange-800/30 flex items-center justify-center">
            <Star className="w-8 h-8 text-orange-600 dark:text-orange-400" />
          </div>
          <h3 className={`text-xl font-semibold ${themeClasses.heading3} mb-3`}>
            {filter === 'all' ? 'No portfolio items yet' : `No ${filter} items found`}
          </h3>
          <p className={`${themeClasses.textMuted} mb-8 max-w-md mx-auto leading-relaxed`}>
            {isOwnProfile
              ? 'Complete trades and collaborations to automatically build your portfolio and showcase your skills.'
              : 'This user hasn\'t added any portfolio items yet.'
            }
          </p>
          {filter !== 'all' && (
            <button
              onClick={() => setFilter('all')}
              className="inline-flex items-center px-4 py-2 rounded-lg text-sm font-medium text-orange-600 dark:text-orange-400 bg-orange-100/50 dark:bg-orange-900/30 hover:bg-orange-100 dark:hover:bg-orange-900/50 transition-all duration-200"
            >
              View all items
            </button>
          )}
        </motion.div>
      ) : (
        <AnimatePresence mode="wait">
          <motion.div
            key={`${viewMode}-${filter}`}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className={`portfolio-items grid gap-6 ${
              viewMode === 'grid'
                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                : 'grid-cols-1'
            }`}
          >
            {filteredItems.map((item, index) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <PortfolioItemComponent
                  item={item}
                  isOwnProfile={isOwnProfile}
                  isManaging={isManaging}
                  onChange={fetchPortfolio}
                />
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>
      )}
    </motion.div>
  );
};

export default PortfolioTab;
