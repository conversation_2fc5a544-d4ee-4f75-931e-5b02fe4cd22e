import React, { useState } from 'react';
import { useAuth } from '../../../AuthContext';
import { createProject, updateProject, Project } from '../../../services/firestore';
import { MultipleImageUploader } from '../../features/uploads/MultipleImageUploader';
import { useToast } from '../../../contexts/ToastContext';
import { Timestamp } from 'firebase/firestore';

interface ProjectFormProps {
  project?: Project;
  onSuccess: (projectId: string) => void;
  onCancel?: () => void;
}

const PROJECT_CATEGORIES = [
  'Web Development',
  'Mobile Development',
  'UI/UX Design',
  'Graphic Design',
  'Content Writing',
  'Marketing',
  'Video Production',
  'Audio Production',
  'Photography',
  'Data Analysis',
  'Other'
];

export const ProjectForm: React.FC<ProjectFormProps> = ({ project, onSuccess, onCancel }) => {
  const { currentUser, userProfile } = useAuth();
  const { addToast } = useToast();

  const [title, setTitle] = useState(project?.title || '');
  const [description, setDescription] = useState(project?.description || '');
  const [category, setCategory] = useState(project?.category || PROJECT_CATEGORIES[0]);
  const [skillsNeeded, setSkillsNeeded] = useState(project?.skillsNeeded?.join(', ') || '');
  const [timeline, setTimeline] = useState(project?.timeline || '');
  const [compensation, setCompensation] = useState(project?.compensation || '');
  const [location, setLocation] = useState(project?.location || '');
  const [isRemote, setIsRemote] = useState(project?.isRemote ?? true);
  const [images, setImages] = useState<string[]>(project?.images || []);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser || !userProfile) {
      setError('You must be logged in to create a project');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Parse skills from comma-separated string to array
      const skillsArray = skillsNeeded
        .split(',')
        .map(skill => skill.trim())
        .filter(skill => skill !== '');

      const projectData = {
        title,
        description,
        category,
        skillsNeeded: skillsArray,
        timeline,
        compensation,
        location,
        isRemote,
        images,
        creatorId: currentUser.uid,
        ownerId: currentUser.uid,
        ownerName: userProfile.displayName || 'Anonymous',
        ownerPhotoURL: userProfile.photoURL,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
        status: 'open' as 'open',
      };

      let result;

      if (project) {
        // Update existing project
        if (project.id) {
          result = await updateProject(project.id, projectData as any);

          if (result.error) {
            throw new Error(result.error.message || 'Failed to update project');
          }

          addToast('success', 'Project updated successfully');
          onSuccess(project.id);
        }
      } else {
        // Create new project
        result = await createProject(projectData as any);

        if (result.error || !result.data) {
          throw new Error(result.error?.message || 'Failed to create project');
        }

        addToast('success', 'Project created successfully');
        onSuccess(result.data);
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      addToast('error', err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      <div>
        <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
          Project Title *
        </label>
        <input
          id="title"
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          required
          className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          placeholder="e.g. Website Redesign for Small Business"
        />
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
          Project Description *
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          required
          rows={5}
          className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          placeholder="Describe your project in detail..."
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
            Category *
          </label>
          <select
            id="category"
            value={category}
            onChange={(e) => setCategory(e.target.value)}
            required
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          >
            {PROJECT_CATEGORIES.map((cat) => (
              <option key={cat} value={cat}>
                {cat}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label htmlFor="timeline" className="block text-sm font-medium text-gray-700 mb-1">
            Timeline *
          </label>
          <input
            id="timeline"
            type="text"
            value={timeline}
            onChange={(e) => setTimeline(e.target.value)}
            required
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="e.g. 2-3 weeks, 1-2 months"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="compensation" className="block text-sm font-medium text-gray-700 mb-1">
            Compensation *
          </label>
          <input
            id="compensation"
            type="text"
            value={compensation}
            onChange={(e) => setCompensation(e.target.value)}
            required
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="e.g. $500-1000, Skill exchange, Negotiable"
          />
        </div>

        <div>
          <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
            Location
          </label>
          <input
            id="location"
            type="text"
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
            placeholder="e.g. New York, NY"
            disabled={isRemote}
          />
          <div className="mt-2">
            <label className="inline-flex items-center">
              <input
                type="checkbox"
                checked={isRemote}
                onChange={(e) => setIsRemote(e.target.checked)}
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="ml-2 text-sm text-gray-600">This is a remote project</span>
            </label>
          </div>
        </div>
      </div>

      <div>
        <label htmlFor="skillsNeeded" className="block text-sm font-medium text-gray-700 mb-1">
          Skills Needed *
        </label>
        <input
          id="skillsNeeded"
          type="text"
          value={skillsNeeded}
          onChange={(e) => setSkillsNeeded(e.target.value)}
          required
          className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          placeholder="e.g. React, UI Design, Content Writing (comma separated)"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Project Images
        </label>
        <MultipleImageUploader
          onImagesChange={setImages}
          folder={`tradeya/projects/${project?.id || 'new'}`}
          initialImageUrls={images}
          maxImages={5}
        />
      </div>

      <div className="flex justify-end space-x-3">
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            Cancel
          </button>
        )}

        <button
          type="submit"
          disabled={loading}
          className="inline-flex justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
        >
          {loading ? 'Saving...' : project ? 'Update Project' : 'Create Project'}
        </button>
      </div>
    </form>
  );
};

export default ProjectForm;
