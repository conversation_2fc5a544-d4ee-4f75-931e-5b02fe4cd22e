import React, { useState } from 'react';
import { useAuth } from '../../../AuthContext';
import { createProjectApplication, Project } from '../../../services/firestore';
import { useToast } from '../../../contexts/ToastContext';

interface ProjectApplicationFormProps {
  project: Project;
  onSuccess: () => void;
  onCancel: () => void;
}

export const ProjectApplicationForm: React.FC<ProjectApplicationFormProps> = ({
  project,
  onSuccess,
  onCancel
}) => {
  const { currentUser, userProfile } = useAuth();
  const { addToast } = useToast();

  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentUser || !userProfile) {
      setError('You must be logged in to apply for a project');
      return;
    }

    if (currentUser.uid === project.ownerId) {
      setError('You cannot apply to your own project');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const applicationData = {
        projectId: project.id,
        message,
        applicantId: currentUser.uid,
        applicantName: userProfile.displayName || 'Anonymous',
        applicantPhotoURL: userProfile.photoURL
      };

      const result = await createProjectApplication(applicationData);

      if (result.error || !result.id) {
        throw new Error(result.error || 'Failed to submit application');
      }

      addToast('success', 'Application submitted successfully');
      onSuccess();
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      addToast('error', err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Apply to "{project.title}"</h3>
        <p className="text-sm text-gray-600 dark:text-gray-300">
          Introduce yourself and explain why you're a good fit for this project.
        </p>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      <div>
        <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Message to Project Owner *
        </label>
        <textarea
          id="message"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          required
          rows={6}
          className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 bg-white dark:text-gray-100 dark:bg-gray-800 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
          placeholder="Introduce yourself, describe your relevant experience, and explain why you're interested in this project..."
        />
      </div>

      <div className="flex justify-end space-x-3 pt-2">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          Cancel
        </button>

        <button
          type="submit"
          disabled={loading}
          className="inline-flex justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
        >
          {loading ? 'Submitting...' : 'Submit Application'}
        </button>
      </div>
    </form>
  );
};

export default ProjectApplicationForm;
