import React from 'react';
import { Link } from 'react-router-dom';
import { Project } from '../../../services/firestore';
import { cn } from '../../../utils/cn';
import { MapPin, Calendar, Clock, DollarSign } from 'lucide-react';
import ProfileHoverCard from '../../ui/ProfileHoverCard';
import { themeClasses } from '../../../utils/themeUtils';

interface ProjectCardProps {
  project: Project;
  className?: string;
  compact?: boolean;
}

const statusColors = {
  'open': 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800',
  'in-progress': 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800',
  'completed': 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600',
  'cancelled': 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800'
};

const statusLabels = {
  'open': 'Open',
  'in-progress': 'In Progress',
  'completed': 'Completed',
  'cancelled': 'Cancelled'
};

export const ProjectCard: React.FC<ProjectCardProps> = ({ project, className, compact = false }) => {
  // Format date
  const formattedDate = project.createdAt && project.createdAt.seconds
    ? new Date(project.createdAt.seconds * 1000).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      })
    : 'Recently posted';

  return (
    <div className={cn(
      `overflow-hidden rounded-lg border ${themeClasses.border} ${themeClasses.card} shadow-sm ${themeClasses.transition} hover:shadow-md hover:-translate-y-1 dark:hover:bg-neutral-700/70 dark:hover:shadow-[0_0_12px_rgba(251,146,60,0.15)]`,
      className
    )}>
      <div className="p-5">
        <div className="flex justify-between items-start">
          <h3 className={`text-lg font-semibold ${themeClasses.text} truncate`}>
            {project.title}
          </h3>
          {project.status ? (
            <span className={cn(
              'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium transition-colors duration-200',
              statusColors[project.status] || 'bg-gray-100 text-gray-800 border-gray-200'
            )}>
              {statusLabels[project.status] || project.status}
            </span>
          ) : (
            <span className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 border-gray-200 dark:border-gray-600 transition-colors duration-200">
              Unknown
            </span>
          )}
        </div>

        {project.ownerId && project.ownerName && (
          <div className="mt-2 flex items-center">
            <ProfileHoverCard
              userId={project.ownerId}
              displayName={project.ownerName}
              photoURL={project.ownerPhotoURL}
            >
              <div className="flex items-center">
                <img
                  src={project.ownerPhotoURL || `https://ui-avatars.com/api/?name=${encodeURIComponent(project.ownerName)}&background=random`}
                  alt={project.ownerName}
                  className="h-6 w-6 rounded-full mr-2"
                />
                <span className="text-sm text-gray-600 dark:text-gray-400 hover:text-orange-600 dark:hover:text-orange-500 transition-colors duration-200">
                  {project.ownerName}
                </span>
              </div>
            </ProfileHoverCard>
          </div>
        )}

        {!compact && project.description && (
          <p className={`mt-3 text-sm ${themeClasses.textMuted} line-clamp-3`}>
            {project.description}
          </p>
        )}

        <div className="mt-4 grid grid-cols-2 gap-2">
          <div className={`flex items-center text-xs ${themeClasses.textMuted}`}>
            <Calendar className="mr-1 h-3.5 w-3.5" />
            <span>Posted {formattedDate}</span>
          </div>

          {project.location && (
            <div className={`flex items-center text-xs ${themeClasses.textMuted}`}>
              <MapPin className="mr-1 h-3.5 w-3.5" />
              <span>{project.isRemote ? 'Remote' : project.location}</span>
            </div>
          )}

          {project.timeline && (
            <div className={`flex items-center text-xs ${themeClasses.textMuted}`}>
              <Clock className="mr-1 h-3.5 w-3.5" />
              <span>{project.timeline}</span>
            </div>
          )}

          {project.compensation && (
            <div className={`flex items-center text-xs ${themeClasses.textMuted}`}>
              <DollarSign className="mr-1 h-3.5 w-3.5" />
              <span>{project.compensation}</span>
            </div>
          )}
        </div>

        {project.skillsNeeded && project.skillsNeeded.length > 0 && (
          <div className="mt-4">
            <div className="flex flex-wrap gap-1.5">
              {project.skillsNeeded.slice(0, compact ? 3 : 5).map((skill, index) => (
                <span
                  key={index}
                  className="inline-flex items-center rounded-full bg-orange-100 dark:bg-orange-900/30 px-2.5 py-0.5 text-xs font-medium text-orange-800 dark:text-orange-300 transition-colors duration-200"
                >
                  {skill}
                </span>
              ))}

              {project.skillsNeeded.length > (compact ? 3 : 5) && (
                <span className="inline-flex items-center rounded-full bg-gray-100 dark:bg-gray-700 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:text-gray-300 transition-colors duration-200">
                  +{project.skillsNeeded.length - (compact ? 3 : 5)} more
                </span>
              )}
            </div>
          </div>
        )}

        <div className="mt-4">
          <Link
            to={`/projects/${project.id}`}
            className={`inline-flex items-center rounded-md ${themeClasses.primaryButton} px-4 py-2 text-sm font-medium shadow-sm ${themeClasses.focus} ${themeClasses.transition}`}
          >
            View Project
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
