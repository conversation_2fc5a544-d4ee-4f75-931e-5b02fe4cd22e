import React from 'react';
import { ProjectApplication } from '../../../services/firestore';
import { cn } from '../../../utils/cn';
import { Calendar } from 'lucide-react';
import ProfileHoverCard from '../../ui/ProfileHoverCard';

interface ProjectApplicationCardProps {
  application: ProjectApplication;
  isOwner: boolean;
  onAccept?: (applicationId: string) => void;
  onReject?: (applicationId: string) => void;
  className?: string;
}

const statusColors = {
  'pending': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'accepted': 'bg-green-100 text-green-800 border-green-200',
  'rejected': 'bg-red-100 text-red-800 border-red-200'
};

const statusLabels = {
  'pending': 'Pending',
  'accepted': 'Accepted',
  'rejected': 'Rejected'
};

export const ProjectApplicationCard: React.FC<ProjectApplicationCardProps> = ({
  application,
  isOwner,
  onAccept,
  onReject,
  className
}) => {
  // Format date
  const formattedDate = new Date(application.createdAt.seconds * 1000).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });

  return (
    <div className={cn(
      'overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm',
      className
    )}>
      <div className="p-5">
        <div className="flex justify-between items-start">
          <ProfileHoverCard
            userId={application.applicantId}
            displayName={application.applicantId ? `User ${application.applicantId.substring(0, 5)}` : 'Unknown User'}
          >
            <div className="flex items-center">
              <img
                src={
                  // ProfileHoverCard should handle fetching, but keeping a fallback just in case
                  application.applicantId ? `https://ui-avatars.com/api/?name=${encodeURIComponent(application.applicantId.substring(0, 5))}&background=random` : undefined
                }
                alt={
                  application.applicantId ? `User ${application.applicantId.substring(0, 5)}` : 'User'
                }
                className="h-10 w-10 rounded-full mr-3"
              />
              <div>
                <h3 className="text-base font-medium text-gray-900 hover:text-orange-600">
                  {application.applicantId ? `User ${application.applicantId.substring(0, 5)}` : 'Unknown User'}
                </h3>
                <div className="flex items-center text-xs text-gray-500 mt-1">
                  <Calendar className="mr-1 h-3.5 w-3.5" />
                  <span>Applied {formattedDate}</span>
                </div>
              </div>
            </div>
          </ProfileHoverCard>
          
          <span className={cn(
            'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-medium',
            statusColors[application.status]
          )}>
            {statusLabels[application.status]}
          </span>
        </div>
        
        <div className="mt-4">
          <p className="text-sm text-gray-600 whitespace-pre-line">
            {application.message}
          </p>
        </div>
        
        {isOwner && application.status === 'pending' && application.id && (
          <div className="mt-4 flex justify-end space-x-3">
            <button
              type="button"
              onClick={() => onReject?.(application.id)}
              className="px-3 py-1.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Decline
            </button>
            
            <button
              type="button"
              onClick={() => onAccept?.(application.id)}
              className="px-3 py-1.5 border border-transparent rounded-md text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Accept
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectApplicationCard;
