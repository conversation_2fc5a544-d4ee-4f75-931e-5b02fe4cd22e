import React, { useState, useRef, useEffect } from 'react';
import {
  uploadImage as cloudinaryUploadImage,
  validateFile,
  ALLOWED_IMAGE_TYPES,
  MAX_FILE_SIZE
} from '../../../services/cloudinary/cloudinaryService';
import { useToast } from '../../../contexts/ToastContext';

interface ImageUploaderProps {
  onUploadSuccess: (url: string) => void;
  onUploadError?: (error: string) => void;
  folder: string;
  initialImageUrl?: string;
  maxWidth?: number;
  maxHeight?: number;
  aspectRatio?: number;
  className?: string;
}

export const ImageUploader: React.FC<ImageUploaderProps> = ({
  onUploadSuccess,
  onUploadError,
  folder,
  initialImageUrl,
  maxWidth = 800,
  maxHeight = 800,
  aspectRatio,
  className = ''
}) => {
  const [imageUrl, setImageUrl] = useState<string | null>(initialImageUrl || null);
  const [isUploading, setIsUploading] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { addToast } = useToast();

  // Update imageUrl when initialImageUrl changes
  useEffect(() => {
    if (initialImageUrl) {
      setImageUrl(initialImageUrl);
    }
  }, [initialImageUrl]);

  // Handle file selection
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    await processFile(file);

    // Reset the input value so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle file drop
  const handleDrop = async (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    await processFile(file);
  };

  // Process and upload the file
  const processFile = async (file: File) => {
    // Validate the file
    const validation = validateFile(file);
    if (!validation.valid) {
      if (onUploadError) onUploadError(validation.error || 'Invalid file');
      addToast('error', validation.error || 'Invalid file');
      return;
    }

    // Resize the image if needed
    try {
      const resizedFile = await resizeImage(file, maxWidth, maxHeight, aspectRatio);
      await uploadToCloudinary(resizedFile);
    } catch (err: any) {
      if (onUploadError) onUploadError(err.message || 'Failed to process image');
      addToast('error', err.message || 'Failed to process image');
    }
  };

  // Resize the image
  const resizeImage = (
    file: File,
    maxWidth: number,
    maxHeight: number,
    aspectRatio?: number
  ): Promise<File> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        const img = new Image();

        img.onload = () => {
          let width = img.width;
          let height = img.height;

          // Maintain aspect ratio if specified
          if (aspectRatio) {
            height = width / aspectRatio;
          }

          // Resize if larger than max dimensions
          if (width > maxWidth || height > maxHeight) {
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            width = width * ratio;
            height = height * ratio;
          }

          // Create canvas and draw resized image
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;

          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('Failed to get canvas context'));
            return;
          }

          ctx.drawImage(img, 0, 0, width, height);

          // Convert canvas to blob
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Failed to create blob'));
                return;
              }

              // Create new file from blob
              const resizedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now()
              });

              resolve(resizedFile);
            },
            file.type,
            0.9 // Quality
          );
        };

        img.onerror = () => {
          reject(new Error('Failed to load image'));
        };

        img.src = e.target?.result as string;
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsDataURL(file);
    });
  };

  // Upload the image to Cloudinary
  const uploadToCloudinary = async (file: File) => {
    setIsUploading(true);
    setProgress(0);

    try {
      // Simulate progress (since Firebase Storage doesn't provide progress updates easily)
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          const newProgress = prev + Math.random() * 10;
          return newProgress > 90 ? 90 : newProgress;
        });
      }, 200);

      const result = await cloudinaryUploadImage(file, folder);

      clearInterval(progressInterval);

      if (result.error) {
        throw new Error(result.error);
      }

      // Prefer transformed URL if available, fallback to base URL
      const finalUrl = result.transformedUrl || result.baseUrl;
      setImageUrl(finalUrl);
      setProgress(100);
      onUploadSuccess(finalUrl);
      addToast('success', 'Image uploaded successfully');
    } catch (err: any) {
      if (onUploadError) onUploadError(err.message || 'Failed to upload image');
      addToast('error', err.message || 'Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  // Handle drag events
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  // Trigger file input click
  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className={`relative ${className}`}>
      <div
        className={`border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-colors ${
          isDragging
            ? 'border-orange-500 bg-orange-50'
            : 'border-gray-300 hover:border-orange-400 hover:bg-gray-50'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        {imageUrl ? (
          <div className="relative">
            <img
              src={imageUrl}
              alt="Uploaded"
              className="mx-auto max-h-64 rounded"
            />
            <button
              type="button"
              className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 focus:outline-none"
              onClick={(e) => {
                e.stopPropagation();
                setImageUrl(null);
              }}
            >
              <svg
                className="h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        ) : (
          <div className="py-4">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="mt-2 text-sm text-gray-600">
              Drag and drop an image, or click to select
            </p>
            <p className="mt-1 text-xs text-gray-500">
              {`Supported formats: ${ALLOWED_IMAGE_TYPES.map(type => type.split('/')[1]).join(', ')}`}
            </p>
            <p className="text-xs text-gray-500">
              {`Max size: ${MAX_FILE_SIZE / 1024 / 1024}MB`}
            </p>
          </div>
        )}

        {isUploading && (
          <div className="mt-2">
            <div className="w-full bg-gray-200 rounded-full h-2.5">
              <div
                className="bg-orange-500 h-2.5 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {progress < 100 ? 'Uploading...' : 'Upload complete'}
            </p>
          </div>
        )}
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept={ALLOWED_IMAGE_TYPES.join(',')}
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};
