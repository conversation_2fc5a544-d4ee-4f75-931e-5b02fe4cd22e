import React from 'react';
import { Message } from '../../../services/firestore';
import { Avatar } from '../../ui/Avatar';
import { useAuth } from '../../../AuthContext';

interface MessageListProps {
  messages: Message[];
  loading: boolean;
  currentUserId: string | undefined;
  messagesEndRef: React.RefObject<HTMLDivElement>;
  usersData?: Record<string, any>;
}

export const MessageList: React.FC<MessageListProps> = ({
  messages,
  loading,
  currentUserId,
  messagesEndRef,
  usersData = {}
}) => {
  const { userProfile } = useAuth();
  // Debug log for messages
  console.log('MessageList received messages:', messages);
  console.log('MessageList currentUserId:', currentUserId);
  console.log('MessageList usersData:', usersData);
  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  return (
    <div className="p-4">
      {loading ? (
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-4 border-gray-200 border-t-orange-500"></div>
        </div>
      ) : messages.length === 0 ? (
        <div className="text-center text-gray-500 my-12">
          <p>No messages yet.</p>
          <p className="mt-2 text-sm">
            Send a message to start the conversation.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {messages.map((message) => {
            const isCurrentUser = currentUserId && message.senderId === currentUserId;
            // Derive sender name and avatar from usersData
            const senderData = usersData[message.senderId] || {};
            const senderName = senderData.displayName || `User ${message.senderId?.substring(0, 5) || 'Unknown'}`;
            const senderAvatar = senderData.profilePicture;

            console.log('Rendering message:', message);
            console.log('Is current user?', isCurrentUser);
            console.log('Message sender data:', usersData[message.senderId]);

            return (
              <div
                key={message.id}
                className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
              >
                <div className="flex items-start gap-2">
                  {!isCurrentUser && (
                    <div className="flex-shrink-0 h-8 w-8">
                      <Avatar
                        src={senderAvatar}
                        alt={senderName}
                        size="sm"
                        fallback={(senderName.charAt(0) || '?').toUpperCase()}
                      />
                    </div>
                  )}
                  <div
                    className={`max-w-[70%] rounded-lg px-4 py-2 ${
                      isCurrentUser
                        ? 'bg-orange-500 text-white'
                        : 'bg-gray-100 text-gray-900'
                    }`}
                  >
                    {!isCurrentUser && (
                      <p className="text-xs font-medium mb-1 text-gray-700">
                        {senderName}
                      </p>
                    )}
                    <p>{message.content}</p>
                    <div className="flex items-center justify-between mt-1">
                      <p className={`text-xs ${isCurrentUser ? 'text-orange-100' : 'text-gray-500'}`}>
                        {message.createdAt && typeof message.createdAt.toDate === 'function'
                          ? formatDate(message.createdAt.toDate())
                          : 'Unknown time'}
                      </p>
                      {isCurrentUser && (
                        <span className="text-xs ml-2">
                          {message.read ? (
                            <span title="Read">✓✓</span>
                          ) : (
                            <span title="Delivered">✓</span>
                          )}
                        </span>
                      )}
                    </div>
                  </div>
                  {isCurrentUser && (
                    <div className="flex-shrink-0 h-8 w-8">
                      <Avatar
                        src={userProfile?.photoURL || userProfile?.profilePicture || usersData[currentUserId || '']?.profilePicture}
                        alt="You"
                        size="sm"
                        fallback="Y"
                      />
                    </div>
                  )}
                </div>
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      )}
    </div>
  );
};
