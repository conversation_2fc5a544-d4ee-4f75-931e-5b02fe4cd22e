import React, { memo } from 'react';
import { Message } from '../../../services/firestore';
import { Avatar } from '../../ui/Avatar';
import { useAuth } from '../../../AuthContext';
import { themeClasses } from '../../../utils/themeUtils';

interface MessageListProps {
  messages: Message[];
  loading: boolean;
  currentUserId: string | undefined;
  messagesEndRef: React.RefObject<HTMLDivElement>;
  usersData?: Record<string, any>;
  otherParticipant?: {
    id: string;
    name: string;
    avatar: string | null;
  } | undefined;
}

export const MessageListNew = memo(({
  messages,
  loading,
  currentUserId,
  messagesEndRef,
  usersData = {},
  otherParticipant
}: MessageListProps) => {
  const { userProfile } = useAuth();

  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  return (
    <div className="p-4">
      {loading ? (
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-4 border-gray-200 dark:border-gray-700 border-t-orange-500"></div>
        </div>
      ) : messages.length === 0 ? (
        <div className="text-center text-gray-500 dark:text-gray-400 my-12">
          <p>No messages yet.</p>
          <p className="mt-2 text-sm">
            Send a message to start the conversation.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {messages.map((message) => {
            const isCurrentUser = currentUserId && message.senderId === currentUserId;

            // Get user data for the message sender
            const senderData = usersData[message.senderId] || {};

            // Derive sender name and avatar from usersData
            const senderName = senderData.displayName || `User ${message.senderId?.substring(0, 5) || 'Unknown'}`;
            const senderAvatar = senderData.profilePicture;

            // Get user data for the current user
            const currentUserData = usersData[currentUserId || ''] || {};
            const currentUserName = userProfile?.displayName || currentUserData.displayName || 'You';
            const currentUserAvatar = userProfile?.photoURL || userProfile?.profilePicture || currentUserData.profilePicture;

            // Get the other participant's name (the one who is not the current user)
            let otherParticipantName = 'Unknown User';
            let otherParticipantAvatar = null;

            // If we have the otherParticipant prop, use that
            if (otherParticipant) {
              otherParticipantName = otherParticipant.name;
              otherParticipantAvatar = otherParticipant.avatar;
            } else if (isCurrentUser) {
              // If this is a message from the current user, we need to find the other participant
              // Try to find the other participant in usersData
              Object.entries(usersData).forEach(([userId, userData]) => {
                if (userId !== currentUserId && userId !== message.senderId) {
                  otherParticipantName = userData.displayName || `User ${userId.substring(0, 5)}`;
                  otherParticipantAvatar = userData.profilePicture;
                }
              });
            } else {
              // If this is a message from someone else, use their data
              otherParticipantName = senderName;
              otherParticipantAvatar = senderAvatar;
            }

            return (
              <div
                key={message.id}
                className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-6`}
              >
                <div className="flex items-start gap-4">
                  {!isCurrentUser && (
                    <div className="flex flex-col items-center">
                      <p className="text-xs font-medium mb-1 text-gray-700 dark:text-gray-300">
                        {otherParticipantName}
                      </p>
                      <div className="flex-shrink-0 h-8 w-8">
                        <Avatar
                          src={otherParticipantAvatar}
                          alt={otherParticipantName}
                          size="sm"
                          fallback={(otherParticipantName.charAt(0) || '?').toUpperCase()}
                        />
                      </div>
                    </div>
                  )}
                  <div>
                    <div
                      className={`max-w-[70%] rounded-lg px-4 py-2 ${themeClasses.transition} ${
                        isCurrentUser
                          ? 'bg-orange-500 text-white'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
                      }`}
                    >
                      <p>{message.content}</p>
                      <div className="flex items-center justify-between mt-1">
                        <p className={`text-xs ${isCurrentUser ? 'text-orange-100' : 'text-gray-500 dark:text-gray-400'}`}>
                          {message.createdAt && typeof message.createdAt.toDate === 'function'
                            ? formatDate(message.createdAt.toDate())
                            : 'Unknown time'}
                        </p>
                        {isCurrentUser && (
                          <span className="text-xs ml-2">
                            {message.read ? (
                              <span title="Read">✓✓</span>
                            ) : (
                              <span title="Delivered">✓</span>
                            )}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  {isCurrentUser && (
                    <div className="flex flex-col items-center">
                      <p className="text-xs font-medium mb-1 text-gray-700 dark:text-gray-300">
                        {currentUserName}
                      </p>
                      <div className="flex-shrink-0 h-8 w-8">
                        <Avatar
                          src={currentUserAvatar}
                          alt={currentUserName}
                          size="sm"
                          fallback={(currentUserName.charAt(0) || 'Y').toUpperCase()}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      )}
    </div>
  );
});
