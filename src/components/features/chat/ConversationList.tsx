import React from 'react';
import { Link } from 'react-router-dom';
// Use Conversation for general messaging UI
import { Conversation } from '../../../services/firestore';
import { Avatar } from '../../ui/Avatar';
import { themeClasses } from '../../../utils/themeUtils';

interface ConversationListProps {
  conversations: Conversation[];
  activeConversation: Conversation | null;
  loading: boolean;
  currentUserId?: string;
  onSelectConversation: (conversation: Conversation) => void;
  getOtherParticipant: (conversation: Conversation) => { id: string; name: string; avatar: string | null };
}

export const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  activeConversation,
  loading,
  currentUserId,
  onSelectConversation,
  getOtherParticipant
}) => {
  // Format date
  const formatDate = (date: Date) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date >= today) {
      return new Intl.DateTimeFormat('en-US', {
        hour: 'numeric',
        minute: 'numeric'
      }).format(date);
    } else if (date >= yesterday) {
      return 'Yesterday';
    } else {
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric'
      }).format(date);
    }
  };

  return (
    <>
      <div className={`p-4 border-b ${themeClasses.border} ${themeClasses.transition}`}>
        <h2 className={`text-lg font-semibold ${themeClasses.text}`}>Conversations</h2>
      </div>

      {loading && conversations.length === 0 ? (
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-4 border-gray-200 dark:border-gray-700 border-t-orange-500 dark:border-t-orange-400"></div>
        </div>
      ) : conversations.length === 0 ? (
        <div className="p-4 text-center text-gray-500 dark:text-gray-400">
          <p>No conversations yet.</p>
          <p className="mt-2 text-sm">
            Start a conversation by contacting a trade owner.
          </p>
        </div>
      ) : (
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {conversations.map((conversation) => {
            const otherParticipant = getOtherParticipant(conversation);
            const isActive = activeConversation?.id === conversation.id;

            return (
              <li
                key={conversation.id}
                className={`p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${isActive ? 'bg-orange-50 dark:bg-orange-900/20' : ''} ${themeClasses.transition}`}
                onClick={() => onSelectConversation(conversation)}
              >
                <div className="flex items-center space-x-3">
                  <Avatar
                    src={otherParticipant.avatar}
                    alt={otherParticipant.name || 'Unknown'}
                    size="md"
                    fallback={otherParticipant.name && otherParticipant.name.length > 0 ?
                      otherParticipant.name.charAt(0).toUpperCase() : '?'}
                  />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                      {otherParticipant.name}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                      {typeof conversation.lastMessage === 'string'
                        ? conversation.lastMessage
                        : conversation.lastMessage?.content || 'No messages yet'}
                    </p>
                  </div>
                  <div className="flex-shrink-0 flex flex-col items-end">
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {conversation.lastMessageTimestamp ? formatDate(conversation.lastMessageTimestamp.toDate()) : ''}
                    </p>
                    {conversation.unreadCount && currentUserId && conversation.unreadCount[currentUserId] > 0 && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 mt-1 transition-colors duration-200">
                        {conversation.unreadCount[currentUserId]}
                      </span>
                    )}
                  </div>
                </div>

                {conversation.tradeId && (
                  <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <span>Trade: </span>
                    <Link
                      to={`/trades/${conversation.tradeId}`}
                      className="text-orange-500 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 transition-colors duration-200"
                      onClick={(e) => e.stopPropagation()}
                    >
                      {conversation.tradeName || 'View trade'}
                    </Link>
                  </div>
                )}
              </li>
            );
          })}
        </ul>
      )}
    </>
  );
};
