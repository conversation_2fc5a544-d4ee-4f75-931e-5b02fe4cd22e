import React, { useState } from 'react';
import { fetchSpecificMessage, fetchSpecificConversation, fetchAllMessagesInConversation } from '../../../utils/fetchMessage';

const MessageFinder: React.FC = () => {
  const [message, setMessage] = useState<any>(null);
  const [conversation, setConversation] = useState<any>(null);
  const [allMessages, setAllMessages] = useState<{nestedMessages: any[], flatMessages: any[]} | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [conversationId, setConversationId] = useState('bcB1UuJ2VHwTXsTFG71g');
  const [messageId, setMessageId] = useState('9U88pB16BSVhy2taDEjH');

  const handleFetchMessage = async () => {
    setAllMessages(null);
    setLoading(true);
    setError(null);

    try {
      // Fetch the conversation first
      const conversationResult = await fetchSpecificConversation(conversationId);

      if (conversationResult.found) {
        setConversation(conversationResult.conversation);
      }

      // Then fetch the message
      const result = await fetchSpecificMessage(conversationId, messageId);

      if (result.found) {
        setMessage(result.message);
      } else {
        setError('Message not found in the database');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to fetch message');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white shadow rounded-lg p-6 max-w-3xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Message Finder</h2>

      <div className="mb-6">
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Conversation ID
          </label>
          <input
            type="text"
            value={conversationId}
            onChange={(e) => setConversationId(e.target.value)}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Message ID
          </label>
          <input
            type="text"
            value={messageId}
            onChange={(e) => setMessageId(e.target.value)}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <p className="text-gray-700 mb-4">
          Looking for message with path: <code className="bg-gray-100 px-2 py-1 rounded">/conversations/{conversationId}/messages/{messageId}</code>
        </p>

        <div className="flex space-x-4">
          <button
            onClick={handleFetchMessage}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            disabled={loading}
          >
            {loading ? 'Searching...' : 'Find Message'}
          </button>

          <button
            onClick={async () => {
              setMessage(null);
              setLoading(true);
              setError(null);

              try {
                const result = await fetchAllMessagesInConversation(conversationId);
                if (result.found) {
                  setAllMessages({
                    nestedMessages: result.nestedMessages || [],
                    flatMessages: result.flatMessages || []
                  });
                } else {
                  setError('No messages found in this conversation');
                }
              } catch (err: any) {
                setError(err.message || 'Failed to fetch all messages');
              } finally {
                setLoading(false);
              }
            }}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
            disabled={loading}
          >
            {loading ? 'Searching...' : 'Find All Messages'}
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
          {error}
        </div>
      )}

      {conversation && (
        <div className="mb-6">
          <h3 className="text-xl font-semibold mb-2">Conversation Details</h3>
          <div className="bg-gray-50 p-4 rounded overflow-auto max-h-60">
            <pre className="text-sm">{JSON.stringify(conversation, null, 2)}</pre>
          </div>
        </div>
      )}

      {allMessages && (
        <div>
          <h3 className="text-xl font-semibold mb-2">All Messages in Conversation</h3>

          <div className="mb-4">
            <h4 className="text-lg font-medium mb-2">Nested Messages ({allMessages.nestedMessages.length})</h4>
            {allMessages.nestedMessages.length > 0 ? (
              <div className="bg-gray-50 p-4 rounded overflow-auto max-h-60">
                <pre className="text-sm">{JSON.stringify(allMessages.nestedMessages, null, 2)}</pre>
              </div>
            ) : (
              <p className="text-gray-600">No messages found in nested collection</p>
            )}
          </div>

          <div>
            <h4 className="text-lg font-medium mb-2">Flat Messages ({allMessages.flatMessages.length})</h4>
            {allMessages.flatMessages.length > 0 ? (
              <div className="bg-gray-50 p-4 rounded overflow-auto max-h-60">
                <pre className="text-sm">{JSON.stringify(allMessages.flatMessages, null, 2)}</pre>
              </div>
            ) : (
              <p className="text-gray-600">No messages found in flat collection</p>
            )}
          </div>
        </div>
      )}

      {message && (
        <div>
          <h3 className="text-xl font-semibold mb-2">Message Details</h3>
          <div className="bg-gray-50 p-4 rounded overflow-auto max-h-60">
            <pre className="text-sm">{JSON.stringify(message, null, 2)}</pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default MessageFinder;
