import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useParams, Link } from 'react-router-dom';
import { useAuth } from '../../../AuthContext';
import { themeClasses } from '../../../utils/themeUtils';
import {
  createMessage,
  Conversation,
  Message,
  User
} from '../../../services/firestore';
import { collection, query, where, orderBy, onSnapshot } from 'firebase/firestore';
import { db } from '../../../firebase-config';
import { ConversationList } from './ConversationList';
// import { MessageList } from './MessageList';
import { MessageListNew } from './MessageListNew';
import { MessageInput } from './MessageInput';
import { MessageHeader } from './MessageHeader';
import { fetchMultipleUsers } from '../../../utils/userUtils';
import { useMessageContext } from '../../../contexts/MessageContext';

export const ChatContainer: React.FC = () => {
  const { conversationId } = useParams<{ conversationId?: string }>();
  const { currentUser, userProfile } = useAuth();
  const { markMessagesAsRead, isUserParticipant } = useMessageContext();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [sendingMessage, setSendingMessage] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [usersData, setUsersData] = useState<Record<string, User>>({});

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const lastMarkAsReadAttemptRef = useRef<number>(0);

  // Fetch user's conversations with real-time updates
  useEffect(() => {
    if (!currentUser) return;

    setLoading(true);
    setError(null);

    // Create a query for the user's conversations
    const conversationsRef = collection(db, 'conversations');
    const q = query(
      conversationsRef,
      where('participantIds', 'array-contains', currentUser.uid),
      orderBy('updatedAt', 'desc')
    );

    // Set up real-time listener
    const unsubscribe = onSnapshot(q, (snapshot) => {
      try {
        const conversationsList: Conversation[] = [];

        snapshot.forEach((doc) => {
          const conversationData = {
            id: doc.id,
            ...doc.data()
          } as Conversation;
          conversationsList.push(conversationData);
        });

        setConversations(conversationsList);

        // If conversationId is provided in URL, set it as active
        if (conversationId) {
          const conversation = conversationsList.find(c => c.id === conversationId);
          if (conversation) {
            setActiveConversation(conversation);
          }
        } else if (conversationsList.length > 0 && !activeConversation) {
          // Otherwise, set the first conversation as active (only if no active conversation)
          setActiveConversation(conversationsList[0]);
        }

        setLoading(false);
      } catch (err: any) {
        setError(err.message || 'Failed to process conversations');
        setLoading(false);
      }
    }, (err: any) => {
      setError(err.message || 'Failed to fetch conversations');
      setLoading(false);
    });

    // Clean up listener on unmount
    return () => unsubscribe();
  }, [currentUser, conversationId]);

  // Fetch messages for active conversation with real-time updates
  useEffect(() => {
    if (!activeConversation || !currentUser) {
      setMessages([]);
      return;
    }

    setLoading(true);
    setError(null);

    // Fetch messages for the active conversation
    const messagesRef = collection(db, 'conversations', activeConversation.id!, 'messages');
    const q = query(messagesRef, orderBy('createdAt', 'asc'));

    // Set up real-time listener
    const unsubscribe = onSnapshot(q, (snapshot) => {
      try {
        const messagesList: Message[] = [];
        let hasUnreadMessages = false;
        const unreadMessageIds: string[] = [];

        snapshot.forEach((doc) => {
          const messageData = {
            id: doc.id,
            ...doc.data()
          } as Message;
          messagesList.push(messageData);

          // Check if this message is unread and not from the current user
          if (!messageData.read && messageData.senderId !== currentUser.uid) {
            hasUnreadMessages = true;
            if (messageData.id) {
              unreadMessageIds.push(messageData.id);
            }
          }
        });

        setMessages(messagesList);
        setLoading(false);

        // Only mark messages as read if there are unread messages, user is a participant, and there are actual unread message IDs
        if (unreadMessageIds.length > 0 && isUserParticipant(activeConversation)) {
          try {
            // Store the last attempt time in a ref to prevent rapid retries
            const now = Date.now();
            const lastAttemptTime = lastMarkAsReadAttemptRef.current || 0;

            // Only attempt to mark as read if it's been at least 5 seconds since the last attempt
            if (now - lastAttemptTime > 5000) {
              lastMarkAsReadAttemptRef.current = now;
              // Pass the array of unread message IDs
              markMessagesAsRead(unreadMessageIds, currentUser.uid)
                .catch((error: any) => {
                  // Log the error but don't rethrow it to prevent the loop
                  console.log('Error marking messages as read (handled):', error.message);
                });
            }
          } catch (error: any) {
            // Log the error but don't rethrow it to prevent the loop
            console.log('Error marking messages as read (caught):', error);
          }
        }

      } catch (err: any) {
        setError(err.message || 'Failed to process messages');
        setLoading(false);
      }
    }, (err: any) => {
      setError(err.message || 'Failed to fetch messages');
      setLoading(false);
    });

    // Clean up listener on unmount
    return () => unsubscribe();
  }, [activeConversation, currentUser, markMessagesAsRead, isUserParticipant]);

  // Memoize the fetchParticipantsData function to prevent unnecessary re-renders
  const fetchParticipantsData = useCallback(async () => {
    if (!conversations.length || !currentUser) return;

    try {
      // Collect all unique user IDs from conversations
      const userIds = new Set<string>();

      // Add current user
      userIds.add(currentUser.uid);

      // Add all participants from conversations
      conversations.forEach((conversation: Conversation) => {
        if (conversation.participants && Array.isArray(conversation.participants)) {
          conversation.participants.forEach((participant: { id: string; name: string }) => userIds.add(participant.id));
        }
      });

      // Add message senders
      messages.forEach((message: Message) => {
        if (message.senderId) {
          userIds.add(message.senderId);
        }
      });

      // If we have an active conversation, make sure we add the other participant
      if (activeConversation && activeConversation.participants) {
        activeConversation.participants.forEach((participant: { id: string; name: string }) => {
          userIds.add(participant.id);
        });
      }

      // Only fetch if there are user IDs to fetch
      if (userIds.size > 0) {
        // Fetch user data for all participants
        const users: Record<string, User> = await fetchMultipleUsers(Array.from(userIds));
        setUsersData(users);
      }
    } catch (error: any) {
      console.error('Error fetching user data:', error);
    }
  }, [conversations, messages, currentUser, activeConversation]);

  // Use a separate useEffect with the memoized function
  useEffect(() => {
    fetchParticipantsData();
  }, [fetchParticipantsData]);

  // Scroll to bottom of messages when new messages are loaded
  // Use a ref to track previous message count to only scroll on new messages
  const prevMessageCountRef = useRef<number>(0);

  useEffect(() => {
    // Only scroll if the number of messages has increased (new message added)
    if (messagesEndRef.current && messages.length > prevMessageCountRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }

    // Update the previous message count
    prevMessageCountRef.current = messages.length;
  }, [messages.length]);

  // Handle sending a new message
  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;
    if (!activeConversation) return;
    if (!currentUser) return;

    setSendingMessage(true);
    setError(null);

    try {
      // Get the best profile picture available
      const profilePicture: string | undefined = userProfile?.profilePicture || userProfile?.photoURL || usersData[currentUser.uid]?.profilePicture;

      const messageData = {
        conversationId: activeConversation.id!,
        senderId: currentUser.uid,
        senderName: userProfile?.displayName || currentUser.email || 'You' as string,
        senderAvatar: profilePicture,
        content: content.trim(),
        read: false,
        status: 'sent' as 'sent' | 'delivered' | 'read' | 'failed',
        type: 'text' as 'text' | 'image' | 'file' | 'link'
      };

      console.log('Sending message with data:', messageData);

      // Call createMessage with conversationId and messageData
      const { error: sendError } = await createMessage(activeConversation.id!, messageData);

      if (sendError) {
        throw new Error(sendError.message);
      }

      // Clear the input after sending
      // The messages will be updated by the real-time listener

    } catch (err: any) {
      console.error('Error sending message:', err);
      setError(err.message || 'Failed to send message');
    } finally {
      setSendingMessage(false);
    }
  };

  // Get other participant in conversation
  const getOtherParticipant = useCallback((conversation: Conversation): { id: string; name: string; avatar: string | null } => {
    if (!currentUser) return { id: '', name: 'Unknown User', avatar: null };

    // If we have participants array with objects
    if (conversation.participants && Array.isArray(conversation.participants) && conversation.participants.length > 0) {
      const participant = conversation.participants.find(p => p.id !== currentUser.uid);
      if (participant) {
        // Fetch avatar from usersData
        const participantUser = usersData[participant.id];
        return {
          id: participant.id,
          name: participant.name,
          avatar: participantUser?.profilePicture || participantUser?.photoURL || null,
        };
      }
      // Return default if other participant not found in participants array
      return { id: '', name: 'Unknown User', avatar: null };
    }

    // If we only have participantIds array (should not happen with updated interface, but keep for robustness)
    // if (conversation.participantIds && Array.isArray(conversation.participantIds)) {
    //   const otherUserId = conversation.participantIds.find(id => id !== currentUser.uid) || '';
    //
    //   if (otherUserId) {
    //     // Check if we have user data for this participant
    //     if (usersData[otherUserId]) {
    //       return {
    //         id: otherUserId,
    //         name: usersData[otherUserId].displayName || `User ${otherUserId.substring(0, 5)}`,
    //         avatar: usersData[otherUserId].profilePicture || null
    //       };
    //     }
    //     return { id: otherUserId, name: `User ${otherUserId.substring(0, 5)}`, avatar: null };
    //   }
    // }

    // Fallback if no participant info is available
    return { id: '', name: 'Unknown User', avatar: null };
  }, [currentUser, usersData]);

  // Memoize the other participant data to avoid redundant calculations
  const memoizedOtherParticipant = useMemo(() => {
    if (!activeConversation) return undefined;
    // The getOtherParticipant function now returns undefined if no other participant is found,
    // and explicitly string | null for avatar when a participant is found.
    return getOtherParticipant(activeConversation);
  }, [activeConversation, getOtherParticipant]);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 h-full">
      {/* Conversations list */}
      <div className={`border-r ${themeClasses.border} overflow-y-auto ${themeClasses.transition}`}>
        <ConversationList
          conversations={conversations}
          activeConversation={activeConversation}
          loading={loading}
          currentUserId={currentUser?.uid}
          onSelectConversation={setActiveConversation}
          getOtherParticipant={getOtherParticipant}
        />
      </div>

      {/* Messages */}
      <div className="col-span-2 flex flex-col h-full">
        {activeConversation ? (
          <>
            {/* Conversation header */}
            <MessageHeader conversation={activeConversation} />

            {/* Messages list */}
            <div className="flex-1 overflow-y-auto">
              {/* Message list container */}
              <MessageListNew
                messages={messages}
                loading={loading}
                currentUserId={currentUser?.uid}
                messagesEndRef={messagesEndRef}
                usersData={usersData}
                otherParticipant={memoizedOtherParticipant}
              />
            </div>

            {/* Message input */}
            <div className={`p-4 border-t ${themeClasses.border} ${themeClasses.transition}`}>
              <MessageInput
                onSendMessage={handleSendMessage}
                disabled={sendingMessage}
                loading={sendingMessage}
              />
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center text-gray-500 dark:text-gray-400 p-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No conversation selected</h3>
            <p className="max-w-sm">
              {conversations.length > 0
                ? 'Select a conversation from the list to view messages.'
                : 'You have no conversations yet. Start a conversation by contacting a trade owner.'}
            </p>
            {conversations.length === 0 && (
              <Link
                to="/trades"
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
              >
                Browse Trades
              </Link>
            )}
          </div>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="absolute bottom-4 right-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg shadow-lg max-w-md transition-colors duration-200">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400 dark:text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-red-700 dark:text-red-400">{error}</p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  onClick={() => setError(null)}
                  className="inline-flex rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 transition-colors duration-200"
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
