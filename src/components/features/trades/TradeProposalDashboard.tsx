import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../AuthContext';
import { getTradeProposals, updateTradeProposalStatus, TradeProposal } from '../../../services/firestore';
import TradeProposalCard from './TradeProposalCard';
import { useToast } from '../../../contexts/ToastContext';
import { motion } from 'framer-motion';
import { themeClasses } from '../../../utils/themeUtils';
import { cn } from '../../../utils/cn';

interface TradeProposalDashboardProps {
  tradeId: string;
  onProposalAccepted: () => void;
}

const TradeProposalDashboard: React.FC<TradeProposalDashboardProps> = ({
  tradeId,
  onProposalAccepted
}) => {
  const { currentUser } = useAuth();
  const { addToast } = useToast();
  const [proposals, setProposals] = useState<TradeProposal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'pending' | 'accepted' | 'rejected'>('pending');
  const [sortBy, setSortBy] = useState<'date' | 'skillMatch'>('date');

  // Fetch proposals
  useEffect(() => {
    const fetchProposals = async () => {
      if (!currentUser) return;

      setLoading(true);
      setError(null);

      try {
        // Correctly destructure 'data' as 'proposals' and 'error' from the service result.
        const { data: proposals, error } = await getTradeProposals(tradeId);

        if (error) throw new Error((error as any).message || 'Unknown error occurred');

        if (proposals) {
          setProposals(proposals);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch proposals');
      } finally {
        setLoading(false);
      }
    };

    fetchProposals();
  }, [tradeId, currentUser]);

  // Handle accept proposal
  const handleAcceptProposal = async (proposalId: string) => {
    if (!currentUser) return;

    try {
      const { error } = await updateTradeProposalStatus(
        tradeId,
        proposalId,
        'accepted'
      );

      if (error) throw new Error(error as any); // Cast to any if error is of unknown type

      // If no error, operation was successful
      // Update local state
      setProposals(prevProposals =>
        prevProposals.map(proposal =>
          proposal.id === proposalId
            ? { ...proposal, status: 'accepted' }
            : proposal.status === 'pending'
              ? { ...proposal, status: 'rejected' }
              : proposal
        )
      );

      addToast('success', 'Proposal accepted successfully!');
      onProposalAccepted();
    } catch (err: any) {
      addToast('error', err.message || 'Failed to accept proposal');
    }
  };

  // Handle reject proposal
  const handleRejectProposal = async (proposalId: string) => {
    if (!currentUser) return;

    try {
      const { error } = await updateTradeProposalStatus(
        tradeId,
        proposalId,
        'rejected'
      );

      if (error) throw new Error(error as any); // Cast to any if error is of unknown type

      // If no error, operation was successful
      // Update local state
      setProposals(prevProposals =>
        prevProposals.map(proposal =>
          proposal.id === proposalId
            ? { ...proposal, status: 'rejected' }
            : proposal
        )
      );

      addToast('success', 'Proposal rejected');
    } catch (err: any) {
      addToast('error', err.message || 'Failed to reject proposal');
    }
  };

  // Sort proposals
  const sortProposals = (proposals: TradeProposal[]) => {
    if (sortBy === 'date') {
      return [...proposals].sort((a, b) => {
        const dateA = a.createdAt?.toDate?.();
        const dateB = b.createdAt?.toDate?.();
        
        // Handle cases where createdAt might be missing or invalid
        if (!dateA && !dateB) return 0;
        if (!dateA) return 1; // Sort items with no date after items with dates
        if (!dateB) return -1; // Sort items with no date after items with dates

        return dateB.getTime() - dateA.getTime(); // Newest first
      });
    } else if (sortBy === 'skillMatch') {
      return [...proposals].sort((a, b) => {
        // Calculate skill match score (number of matching skills)
        const aScore = a.offeredSkills?.length || 0 + a.requestedSkills?.length || 0;
        const bScore = b.offeredSkills?.length || 0 + b.requestedSkills?.length || 0;
        return bScore - aScore; // Higher score first
      });
    }
    return proposals;
  };

  // Filter and sort proposals
  const filteredProposals = sortProposals(
    proposals.filter(proposal => {
      if (filter === 'all') return true;
      return proposal.status === filter;
    })
  );

  // Count proposals by status
  const counts = {
    all: proposals.length,
    pending: proposals.filter(p => p.status === 'pending').length,
    accepted: proposals.filter(p => p.status === 'accepted').length,
    rejected: proposals.filter(p => p.status === 'rejected').length
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <motion.div
      className={cn(
        "rounded-lg shadow-sm p-6",
        themeClasses.card,
        themeClasses.border
      )}
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Trade Proposals</h2>

        <div className="flex flex-col gap-4 w-full md:w-auto">
          {/* Sort options */}
          <div className="flex items-center justify-end">
            <span className="text-sm text-gray-500 dark:text-gray-400 mr-2">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'date' | 'skillMatch')}
              className="bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 text-sm rounded-md focus:ring-orange-500 focus:border-orange-500 p-1.5"
            >
              <option value="date">Date (Newest)</option>
              <option value="skillMatch">Skill Match</option>
            </select>
          </div>

          {/* Filter options */}
          <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setFilter('pending')}
            className={cn(
              "px-3 py-1 text-sm rounded-md flex items-center",
              filter === 'pending'
                ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
            )}
          >
            Pending
            <span className="ml-1.5 bg-yellow-200 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center">
              {counts.pending}
            </span>
          </button>
          <button
            onClick={() => setFilter('accepted')}
            className={cn(
              "px-3 py-1 text-sm rounded-md flex items-center",
              filter === 'accepted'
                ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
            )}
          >
            Accepted
            <span className="ml-1.5 bg-green-200 dark:bg-green-800 text-green-800 dark:text-green-200 text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center">
              {counts.accepted}
            </span>
          </button>
          <button
            onClick={() => setFilter('rejected')}
            className={cn(
              "px-3 py-1 text-sm rounded-md flex items-center",
              filter === 'rejected'
                ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
            )}
          >
            Rejected
            <span className="ml-1.5 bg-red-200 dark:bg-red-800 text-red-800 dark:text-red-200 text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center">
              {counts.rejected}
            </span>
          </button>
          <button
            onClick={() => setFilter('all')}
            className={cn(
              "px-3 py-1 text-sm rounded-md flex items-center",
              filter === 'all'
                ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300'
                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
            )}
          >
            All
            <span className="ml-1.5 bg-blue-200 dark:bg-blue-800 text-blue-800 dark:text-blue-200 text-xs rounded-full px-1.5 py-0.5 min-w-[1.25rem] text-center">
              {counts.all}
            </span>
          </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-200 dark:border-gray-700 border-t-orange-500"></div>
        </div>
      ) : filteredProposals.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-8 text-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-12 w-12 mx-auto text-gray-400 dark:text-gray-500 mb-4"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            {filter === 'all'
              ? 'No proposals yet'
              : filter === 'pending'
              ? 'No pending proposals'
              : filter === 'accepted'
              ? 'No accepted proposals'
              : 'No rejected proposals'}
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {filter === 'all'
              ? 'When users submit proposals for your trade, they will appear here.'
              : filter === 'pending'
              ? 'There are no pending proposals to review at this time.'
              : filter === 'accepted'
              ? 'You haven\'t accepted any proposals yet.'
              : 'You haven\'t rejected any proposals yet.'}
          </p>
          {filter !== 'all' && (
            <button
              onClick={() => setFilter('all')}
              className="text-orange-500 hover:text-orange-600 font-medium"
            >
              View all proposals
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          {filteredProposals.map(proposal => (
            <TradeProposalCard
              key={proposal.id}
              proposal={proposal}
              isCreator={true}
              onAccept={() => handleAcceptProposal(proposal.id)}
              onReject={() => handleRejectProposal(proposal.id)}
            />
          ))}
        </div>
      )}
    </motion.div>
  );
};

export default TradeProposalDashboard;
