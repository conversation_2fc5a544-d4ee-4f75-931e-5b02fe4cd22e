import React, { useState } from 'react';
import { useAuth } from '../../../AuthContext';
import { requestTradeCompletion } from '../../../services/firestore';
import EvidenceSubmitter from '../../evidence/EvidenceSubmitter';
import { EmbeddedEvidence } from '../../../types/evidence';
import { EvidenceGallery } from '../evidence/EvidenceGallery';

interface TradeCompletionFormProps {
  tradeId: string;
  tradeName: string;
  onSuccess: () => void;
  onCancel: () => void;
}

const TradeCompletionForm: React.FC<TradeCompletionFormProps> = ({
  tradeId,
  tradeName,
  onSuccess,
  onCancel
}) => {
  const { currentUser } = useAuth();
  const [notes, setNotes] = useState('');
  const [evidence, setEvidence] = useState<EmbeddedEvidence[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showEvidenceForm, setShowEvidenceForm] = useState<boolean>(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // This function was removed as it's no longer used
  // We now directly update the evidence state in the onChange handler

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      if (!currentUser) {
        throw new Error('You must be logged in to request completion');
      }

      if (!notes.trim()) {
        throw new Error('Please provide completion notes');
      }

      if (evidence.length === 0) {
        throw new Error('Please provide at least one piece of evidence');
      }

      // Request trade completion
      const { success, error } = await requestTradeCompletion(
        tradeId,
        currentUser.uid,
        notes,
        evidence
      );

      if (error) {
        throw new Error(error);
      }

      if (!success) {
        throw new Error('Failed to request completion');
      }

      // Success! Call the onSuccess callback
      onSuccess();

    } catch (err: any) {
      setError(err.message || 'Failed to request completion');
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border-2 border-orange-300 dark:border-orange-700 p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Request Trade Completion</h2>
        <button
          onClick={onCancel}
          className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="mb-6">
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 text-yellow-700 dark:text-yellow-400 px-4 py-3 rounded-lg">
          <p className="font-medium">Important:</p>
          <p>Both participants must submit evidence before the trade can be completed. After you submit your evidence, the other participant will be notified to submit their evidence as well.</p>
          <p className="mt-2">Once both participants have submitted evidence, the trade will move to the "Pending Confirmation" status, and either participant can confirm the trade as completed.</p>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Completion Notes */}
          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Completion Notes
            </label>
            <textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={4}
              className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder={`Describe what you've completed for the trade: "${tradeName}"`}
              required
            />
          </div>

          {/* Completion Evidence */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Completion Evidence
            </label>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
              Add links to your completed work as evidence.
            </p>

            {/* Display added evidence */}
            {evidence.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium mb-2">Added Evidence ({evidence.length}/5)</h4>
                <EvidenceGallery evidence={evidence} />
              </div>
            )}

            {/* Success message */}
            {successMessage && (
              <div className="mb-4 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <p className="text-green-700 dark:text-green-400">{successMessage}</p>
              </div>
            )}

            {/* Evidence form or add button */}
            {showEvidenceForm ? (
              <div className="mb-4">
                <EvidenceSubmitter
                  onChange={(newEvidence) => {
                    // Add the new evidence to the existing array
                    setEvidence([...evidence, ...newEvidence]);

                    // Hide the form after successful submission
                    setShowEvidenceForm(false);

                    // Show success message
                    setSuccessMessage('Evidence added successfully');

                    // Clear the success message after 3 seconds
                    setTimeout(() => {
                      setSuccessMessage(null);
                    }, 3000);
                  }}
                  evidence={[]}
                  maxItems={5 - evidence.length}
                />
              </div>
            ) : (
              <button
                type="button"
                onClick={() => setShowEvidenceForm(true)}
                className="mb-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Add Evidence
              </button>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex space-x-4">
            <button
              type="submit"
              className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Submitting...' : 'Request Completion'}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
              disabled={isSubmitting}
            >
              Cancel
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default TradeCompletionForm;
