import React from 'react';
import { TradeProposal } from '../../../services/firestore';
import ProfileImageWithUser from '../../ui/ProfileImageWithUser';
import { EvidenceGallery } from '../../features/evidence/EvidenceGallery';
import { motion } from 'framer-motion';

interface TradeProposalCardProps {
  proposal: TradeProposal;
  onAccept: () => void;
  onReject: () => void;
  isCreator: boolean;
}

const TradeProposalCard: React.FC<TradeProposalCardProps> = ({
  proposal,
  onAccept,
  onReject,
  isCreator
}) => {
  // Format date
  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'Recently';

    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      }).format(date);
    } catch (err) {
      console.error('Error formatting date:', err);
      return 'Invalid Date';
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      case 'accepted':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'rejected':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      case 'withdrawn':
        return 'bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300';
    }
  };

  return (
    <motion.div
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header with user info and status */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-start">
          <div className="flex items-center">
            <div className="flex-shrink-0 mr-4">
              <ProfileImageWithUser
                userId={proposal._userId}
                profileUrl={proposal.userPhotoURL}
                size="medium"
                className="w-12 h-12 rounded-full"
              />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">{proposal.userName || 'Anonymous'}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Proposed {formatDate(proposal.createdAt)}
              </p>
            </div>
          </div>
          <span className={`${getStatusColor(proposal.status)} text-sm font-medium px-2.5 py-0.5 rounded`}>
            {proposal.status.charAt(0).toUpperCase() + proposal.status.slice(1)}
          </span>
        </div>
      </div>

      {/* Proposal details */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-2">Message</h4>
        <p className="text-gray-700 dark:text-gray-300 mb-6 whitespace-pre-line">{proposal.message}</p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Skills offered */}
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-2">Skills Offered</h4>
            <div className="flex flex-wrap gap-2">
              {proposal.offeredSkills.map((skill, index) => (
                <span
                  key={index}
                  className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-sm font-medium px-2.5 py-0.5 rounded"
                >
                  {skill.name}
                  {skill.level && <span className="ml-1 opacity-75">({skill.level})</span>}
                </span>
              ))}
            </div>
          </div>

          {/* Skills requested */}
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-2">Skills Requested</h4>
            <div className="flex flex-wrap gap-2">
              {proposal.requestedSkills.map((skill, index) => (
                <span
                  key={index}
                  className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-sm font-medium px-2.5 py-0.5 rounded"
                >
                  {skill.name}
                  {skill.level && <span className="ml-1 opacity-75">({skill.level})</span>}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Additional details */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-2">Timeframe</h4>
            <p className="text-gray-700 dark:text-gray-300">{proposal.timeframe}</p>
          </div>

          <div>
            <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-2">Availability</h4>
            <div className="flex flex-wrap gap-2">
              {proposal.availability.map((time, index) => (
                <span
                  key={index}
                  className="bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 text-sm font-medium px-2.5 py-0.5 rounded"
                >
                  {time}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      {isCreator && proposal.status === 'pending' && (
        <div className="p-6 flex justify-end space-x-4">
          <button
            onClick={onReject}
            className="bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border border-gray-300 dark:border-gray-600 px-4 py-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          >
            Reject
          </button>
          <button
            onClick={onAccept}
            className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
          >
            Accept Proposal
          </button>
        </div>
      )}
    </motion.div>
  );
};

export default TradeProposalCard;
