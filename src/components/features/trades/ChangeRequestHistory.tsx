import React from 'react';
import { ChangeRequest } from '../../../services/firestore';
import { formatDate, getRelativeTimeString } from '../../../utils/dateUtils';
import { motion } from 'framer-motion';
import { cn } from '../../../utils/cn';
import { themeClasses } from '../../../utils/themeUtils';

interface ChangeRequestHistoryProps {
  changeRequests: ChangeRequest[];
  className?: string;
}

/**
 * Component to display the history of change requests for a trade
 */
const ChangeRequestHistory: React.FC<ChangeRequestHistoryProps> = ({
  changeRequests,
  className = ''
}) => {
  if (!changeRequests || changeRequests.length === 0) {
    return null;
  }

  // Sort change requests by date (newest first)
  const sortedRequests = [...changeRequests].sort((a, b) => {
    const dateA = a.requestedAt?.toDate?.() || new Date(a.requestedAt);
    const dateB = b.requestedAt?.toDate?.() || new Date(b.requestedAt);
    return dateB.getTime() - dateA.getTime();
  });

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300';
      case 'addressed':
        return 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300';
      case 'rejected':
        return 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400';
    }
  };

  return (
    <motion.div 
      className={cn(
        "space-y-4",
        themeClasses.card,
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Change Request History</h3>
      
      <div className={cn(
        "border rounded-lg overflow-hidden",
        themeClasses.border
      )}>
        <ul className="divide-y divide-gray-200 dark:divide-gray-700">
          {sortedRequests.map((request) => (
            <li key={request.id} className="p-4 bg-white dark:bg-gray-800">
              <div className="flex justify-between items-start">
                <div>
                  <div className="flex items-center mb-2">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(request.status)}`}>
                      {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                    </span>
                    <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                      {getRelativeTimeString(request.requestedAt?.toDate?.() || request.requestedAt)}
                    </span>
                  </div>
                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                    Changes Requested on {formatDate(request.requestedAt?.toDate?.() || request.requestedAt)}
                  </p>
                  <p className="mt-1 text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line">
                    {request.reason}
                  </p>
                </div>
              </div>
              
              {request.resolvedAt && (
                <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {request.status === 'addressed' ? 'Addressed' : 'Rejected'} on {formatDate(request.resolvedAt?.toDate?.() || request.resolvedAt)}
                  </p>
                </div>
              )}
            </li>
          ))}
        </ul>
      </div>
    </motion.div>
  );
};

export default ChangeRequestHistory;
