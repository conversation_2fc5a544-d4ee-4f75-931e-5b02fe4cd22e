import React, { useEffect, useState } from 'react';
import { useAuth } from '../../../AuthContext';
import { confirmTradeCompletion, requestTradeChanges, Trade } from '../../../services/firestore';
import { generateTradePortfolioItem } from '../../../services/portfolio';
import { EvidenceGallery } from '../../features/evidence/EvidenceGallery';

interface TradeConfirmationFormProps {
  trade: Trade;
  initialMode?: 'confirm' | 'requestChanges'; // Add initialMode prop
  onSuccess: () => void;
  onCancel: () => void;
  onRequestChanges: () => void;
}

const TradeConfirmationForm: React.FC<TradeConfirmationFormProps> = ({
  trade,
  initialMode = 'confirm', // Apply default value for initialMode
  onSuccess,
  onCancel,
  onRequestChanges
}) => {
  const { currentUser } = useAuth();
  const [showChangeRequestForm, setShowChangeRequestForm] = useState(initialMode === 'requestChanges');
  const [changeReason, setChangeReason] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    setShowChangeRequestForm(initialMode === 'requestChanges');
  }, [initialMode]);

  // Handle confirmation
  const handleConfirm = async () => {
    setIsSubmitting(true);
    setError(null);

    try {
      if (!currentUser) {
        throw new Error('You must be logged in to confirm completion');
      }

      if (!trade.id) {
        throw new Error('Trade ID is missing');
      }

      // Confirm trade completion
      const { error } = await confirmTradeCompletion(
        trade.id,
        currentUser.uid
      );

      if (error) {
        throw new Error(error.message || 'Failed to confirm completion');
      }

      // Generate portfolio items for both participants
      // Note: We continue even if portfolio generation fails to avoid blocking trade completion
      try {
        // Generate portfolio item for the trade creator
        if (trade.creatorId) {
          await generateTradePortfolioItem(
            {
              id: trade.id,
              title: trade.title,
              description: trade.description,
              offeredSkills: trade.offeredSkills.map(skill => typeof skill === 'string' ? skill : skill.name),
              requestedSkills: trade.requestedSkills.map(skill => typeof skill === 'string' ? skill : skill.name),
              completionConfirmedAt: trade.completionConfirmedAt,
              updatedAt: trade.updatedAt,
              completionEvidence: trade.completionEvidence,
              creatorId: trade.creatorId,
              participantId: trade.participantId || '',
              creatorName: trade.creatorName,
              participantPhotoURL: undefined,
              creatorPhotoURL: undefined,
            },
            trade.creatorId,
            true, // isCreator
            true  // defaultVisibility
          );
        }

        // Generate portfolio item for the trade participant
        if (trade.participantId) {
          await generateTradePortfolioItem(
            {
              id: trade.id,
              title: trade.title,
              description: trade.description,
              offeredSkills: trade.offeredSkills.map(skill => typeof skill === 'string' ? skill : skill.name),
              requestedSkills: trade.requestedSkills.map(skill => typeof skill === 'string' ? skill : skill.name),
              completionConfirmedAt: trade.completionConfirmedAt,
              updatedAt: trade.updatedAt,
              completionEvidence: trade.completionEvidence,
              creatorId: trade.creatorId,
              participantId: trade.participantId,
              creatorName: trade.creatorName,
              participantPhotoURL: undefined,
              creatorPhotoURL: undefined,
            },
            trade.participantId,
            false, // isCreator
            true   // defaultVisibility
          );
        }
      } catch (portfolioError: any) {
        // Log portfolio generation error but don't fail the trade confirmation
        console.warn('Portfolio generation failed:', portfolioError.message);
      }

      // Success! Call the onSuccess callback
      onSuccess();

    } catch (err: any) {
      console.error('Error confirming trade completion:', err);
      setError(err.message || 'Failed to confirm completion');
      setIsSubmitting(false);
    }
  };

  // Handle change request
  const handleRequestChanges = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      if (!currentUser) {
        throw new Error('You must be logged in to request changes');
      }

      if (!trade.id) {
        throw new Error('Trade ID is missing');
      }

      if (!changeReason.trim()) {
        throw new Error('Please provide a reason for the requested changes');
      }

      // Request changes
      const { error } = await requestTradeChanges(
        trade.id,
        currentUser.uid,
        changeReason
      );

      if (error) {
        throw new Error(error.message || 'Failed to request changes');
      }

      // Success! Call the onRequestChanges callback
      onRequestChanges();

    } catch (err: any) {
      setError(err.message || 'Failed to request changes');
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          {showChangeRequestForm ? 'Request Changes' : 'Confirm Trade Completion'}
        </h2>
        <button
          onClick={onCancel}
          className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}

      {!showChangeRequestForm ? (
        <>
          <div className="mb-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-400 px-4 py-3 rounded-lg">
              <p className="font-medium">Completion Request</p>
              <p>The other participant has requested to mark this trade as complete. Please review their work and confirm if the trade is complete.</p>
            </div>
          </div>

          {/* Completion Notes */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Completion Notes</h3>
            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
              <p className="text-gray-700 dark:text-gray-300 whitespace-pre-line">{trade.completionNotes || 'No notes provided.'}</p>
            </div>
          </div>

          {/* Completion Evidence */}
          <div className="mb-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Completion Evidence</h3>

            {/* Creator's Evidence */}
            {trade.creatorEvidence && trade.creatorEvidence.length > 0 && (
              <div className="mb-4">
                <h4 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-2">
                  Creator's Evidence
                  {trade.creatorName && ` (${trade.creatorName})`}
                </h4>
                <EvidenceGallery
                  evidence={trade.creatorEvidence}
                  title=""
                  emptyMessage="No evidence has been provided."
                />
                {trade.creatorCompletionNotes && (
                  <div className="mt-2 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                    <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line">
                      <strong>Notes:</strong> {trade.creatorCompletionNotes}
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Participant's Evidence */}
            {trade.participantEvidence && trade.participantEvidence.length > 0 && (
              <div className="mb-4">
                <h4 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-2">
                  Participant's Evidence
                  {trade.participantId && ` (${trade.participantId})`}
                </h4>
                <EvidenceGallery
                  evidence={trade.participantEvidence}
                  title=""
                  emptyMessage="No evidence has been provided."
                />
                {trade.participantCompletionNotes && (
                  <div className="mt-2 bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
                    <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line">
                      <strong>Notes:</strong> {trade.participantCompletionNotes}
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Legacy Evidence (for backward compatibility) */}
            {(!trade.creatorEvidence || trade.creatorEvidence.length === 0) &&
             (!trade.participantEvidence || trade.participantEvidence.length === 0) &&
             trade.completionEvidence && trade.completionEvidence.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-2">Evidence</h4>
                <EvidenceGallery
                  evidence={trade.completionEvidence}
                  title=""
                  emptyMessage="No evidence has been provided."
                />
              </div>
            )}

            {/* No Evidence Message */}
            {(!trade.creatorEvidence || trade.creatorEvidence.length === 0) &&
             (!trade.participantEvidence || trade.participantEvidence.length === 0) &&
             (!trade.completionEvidence || trade.completionEvidence.length === 0) && (
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                <p className="text-gray-500 dark:text-gray-400 italic">No evidence has been provided.</p>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-4">
            <button
              type="button"
              onClick={() => setShowChangeRequestForm(true)}
              className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
              disabled={isSubmitting}
            >
              Request Changes
            </button>
            <button
              type="button"
              onClick={handleConfirm}
              className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Confirming...' : 'Confirm Completion'}
            </button>
          </div>
        </>
      ) : (
        <form onSubmit={handleRequestChanges}>
          <div className="space-y-6">
            {/* Change Request Reason */}
            <div>
              <label htmlFor="changeReason" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Reason for Requested Changes
              </label>
              <textarea
                id="changeReason"
                value={changeReason}
                onChange={(e) => setChangeReason(e.target.value)}
                rows={4}
                className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                placeholder="Explain what changes are needed before you can confirm completion..."
                required
              />
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => setShowChangeRequestForm(false)}
                className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                disabled={isSubmitting}
              >
                Back
              </button>
              <button
                type="submit"
                className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Submitting...' : 'Submit Change Request'}
              </button>
            </div>
          </div>
        </form>
      )}
    </div>
  );
};

export default TradeConfirmationForm;
