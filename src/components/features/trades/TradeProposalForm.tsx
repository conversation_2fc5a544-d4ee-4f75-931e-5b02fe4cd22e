import React, { useState } from 'react';
import { useAuth } from '../../../AuthContext';
import { createTradeProposal, TradeSkill } from '../../../services/firestore';
import EvidenceSubmitter from '../../evidence/EvidenceSubmitter';
import { EmbeddedEvidence } from '../../../types/evidence';
import { EvidenceGallery } from '../../features/evidence/EvidenceGallery';

interface TradeProposalFormProps {
  tradeId: string;
  tradeName: string;
  offeredSkills: string[];
  requestedSkills: string[];
  onSuccess: () => void;
  onCancel: () => void;
}

const TradeProposalForm: React.FC<TradeProposalFormProps> = ({
  tradeId,
  tradeName,
  offeredSkills,
  requestedSkills,
  onSuccess,
  onCancel
}) => {
  const { currentUser, userProfile } = useAuth();
  const [message, setMessage] = useState('');
  const [timeframe, setTimeframe] = useState('');
  const [availability, setAvailability] = useState<string[]>([]);
  const [portfolioEvidence, setPortfolioEvidence] = useState<EmbeddedEvidence[]>([]);
  const [showEvidenceForm, setShowEvidenceForm] = useState<boolean>(true);
  const [selectedOfferedSkills, setSelectedOfferedSkills] = useState<string[]>([]);
  const [selectedRequestedSkills, setSelectedRequestedSkills] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Availability options
  const availabilityOptions = [
    'Weekdays',
    'Weekends',
    'Mornings',
    'Afternoons',
    'Evenings',
    'Flexible'
  ];

  // Timeframe options
  const timeframeOptions = [
    'Less than a week',
    '1-2 weeks',
    '2-4 weeks',
    '1-2 months',
    'More than 2 months',
    'Flexible'
  ];

  // Handle availability selection
  const handleAvailabilityChange = (option: string) => {
    setAvailability(prev =>
      prev.includes(option)
        ? prev.filter(item => item !== option)
        : [...prev, option]
    );
  };

  // Handle offered skills selection
  const handleOfferedSkillChange = (skill: string) => {
    setSelectedOfferedSkills(prev =>
      prev.includes(skill)
        ? prev.filter(item => item !== skill)
        : [...prev, skill]
    );
  };

  // Handle requested skills selection
  const handleRequestedSkillChange = (skill: string) => {
    setSelectedRequestedSkills(prev =>
      prev.includes(skill)
        ? prev.filter(item => item !== skill)
        : [...prev, skill]
    );
  };

  // This function is no longer used since we're directly updating the state in the onSubmit callback

  // Direct submit function that can be called from the button
  const submitProposal = async () => {
    console.log('Direct submission started');
    setIsSubmitting(true);
    setError(null);
    setSuccessMessage(null);

    // Validate form fields
    let validationError = null;

    // The rest of the submission logic
    try {
      if (!currentUser) {
        validationError = 'You must be logged in to submit a proposal';
        throw new Error(validationError);
      }

      if (!message.trim()) {
        validationError = 'Please provide a message to the trade creator';
        throw new Error(validationError);
      }

      if (!timeframe) {
        validationError = 'Please select a timeframe';
        throw new Error(validationError);
      }

      if (availability.length === 0) {
        validationError = 'Please select at least one availability option';
        throw new Error(validationError);
      }

      if (selectedOfferedSkills.length === 0) {
        validationError = 'Please select at least one skill you are offering';
        throw new Error(validationError);
      }

      if (selectedRequestedSkills.length === 0) {
        validationError = 'Please select at least one skill you are requesting';
        throw new Error(validationError);
      }

      // Convert selected skills to TradeSkill objects
      const offeredSkillObjects: TradeSkill[] = selectedOfferedSkills.map(skill => ({
        name: skill,
        level: 'intermediate' // Default level, could be made selectable in the future
      }));

      const requestedSkillObjects: TradeSkill[] = selectedRequestedSkills.map(skill => ({
        name: skill,
        level: 'intermediate' // Default level, could be made selectable in the future
      }));

      // Create the proposal
      console.log('Submitting proposal with evidence:', portfolioEvidence);

      // Create the proposal data object with null checks to avoid undefined values
      const proposalData = {
        _userId: currentUser.uid,
        userName: userProfile?.displayName || currentUser.displayName || 'Anonymous',
        // Use null instead of undefined for Firestore compatibility
        userPhotoURL: userProfile?.profilePicture || currentUser.photoURL || undefined,
        offeredSkills: offeredSkillObjects,
        requestedSkills: requestedSkillObjects,
        message,
        timeframe,
        availability,
        // Set initial status for a new proposal
        status: 'pending',
        portfolioEvidence: portfolioEvidence || [], // Ensure this is at least an empty array
        tradeId: tradeId, // Added tradeId
      };

      // Clean the portfolioEvidence to ensure no undefined values
      if (proposalData.portfolioEvidence && proposalData.portfolioEvidence.length > 0) {
        proposalData.portfolioEvidence = proposalData.portfolioEvidence.map(evidence => {
          // Create a clean copy of the evidence object
          const cleanEvidence = { ...evidence };

          // Replace any undefined values with null
          Object.keys(cleanEvidence).forEach(key => {
            if ((cleanEvidence as any)[key] === undefined) {
              (cleanEvidence as any)[key] = null;
            }
          });

          return cleanEvidence;
        });
      }

      console.log('Cleaned proposal data:', proposalData);

      console.log('Proposal data being sent:', proposalData);

      console.log('About to call createTradeProposal with data:', proposalData);
      const result = await createTradeProposal(proposalData);
      console.log('createTradeProposal result:', result);

      const { data: proposalId, error } = result; // Correctly destructure data as proposalId

      if (error) {
        console.error('Error from createTradeProposal:', error);
        throw new Error(error.message); // Throw error message
      }

      if (!proposalId) {
        console.error('No proposalId returned');
        throw new Error('Failed to create proposal');
      }

      console.log('Proposal created successfully with ID:', proposalId);
      // Success! Call the onSuccess callback
      onSuccess();

    } catch (err: any) {
      console.error('Error in form submission:', err);
      setError(err.message || 'Failed to submit proposal');

      // Scroll to the error message
      setTimeout(() => {
        const errorElement = document.querySelector('.bg-red-50');
        if (errorElement) {
          errorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);

      setIsSubmitting(false);
    } finally {
      console.log('Form submission completed');
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submission started from handleSubmit');
    submitProposal();
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Submit a Proposal</h2>
        <button
          onClick={onCancel}
          className="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div className="text-sm text-gray-500 dark:text-gray-400 mb-6">
        Fields marked with <span className="text-red-500">*</span> are required.
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-red-500" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span className="font-medium">Error:</span>
          </div>
          <p className="mt-1 ml-7">{error}</p>
        </div>
      )}

      {successMessage && (
        <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 px-4 py-3 rounded-lg mb-6 flex items-center">
          <span className="text-orange-500 dark:text-orange-400 mr-2">✓</span>
          <span className="text-gray-700 dark:text-gray-300">{successMessage}</span>
        </div>
      )}

      <form onSubmit={handleSubmit} id="trade-proposal-form">
        <div className="space-y-6">
          {/* Message */}
          <div>
            <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Message to Trade Creator <span className="text-red-500">*</span>
            </label>
            <textarea
              id="message"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={4}
              className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              placeholder={`Explain why you're a good fit for this trade: "${tradeName}"`}
              required
            />
          </div>

          {/* Skills Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Skills You're Offering */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Skills You're Offering <span className="text-red-500">*</span>
              </label>
              <div className="space-y-2 max-h-60 overflow-y-auto p-2 border border-gray-200 dark:border-gray-700 rounded-md">
                {offeredSkills.map((skill, index) => (
                  <div key={index} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`offered-skill-${index}`}
                      checked={selectedOfferedSkills.includes(skill)}
                      onChange={() => handleOfferedSkillChange(skill)}
                      className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 dark:border-gray-600 rounded"
                    />
                    <label
                      htmlFor={`offered-skill-${index}`}
                      className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
                    >
                      {skill}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Skills You're Requesting */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Skills You're Requesting <span className="text-red-500">*</span>
              </label>
              <div className="space-y-2 max-h-60 overflow-y-auto p-2 border border-gray-200 dark:border-gray-700 rounded-md">
                {requestedSkills.map((skill, index) => (
                  <div key={index} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`requested-skill-${index}`}
                      checked={selectedRequestedSkills.includes(skill)}
                      onChange={() => handleRequestedSkillChange(skill)}
                      className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 dark:border-gray-600 rounded"
                    />
                    <label
                      htmlFor={`requested-skill-${index}`}
                      className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
                    >
                      {skill}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Timeframe */}
          <div>
            <label htmlFor="timeframe" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Timeframe <span className="text-red-500">*</span>
            </label>
            <select
              id="timeframe"
              value={timeframe}
              onChange={(e) => setTimeframe(e.target.value)}
              className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
              required
            >
              <option value="">Select a timeframe</option>
              {timeframeOptions.map((option) => (
                <option key={option} value={option}>{option}</option>
              ))}
            </select>
          </div>

          {/* Availability */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Availability <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {availabilityOptions.map((option) => (
                <div key={option} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`availability-${option}`}
                    checked={availability.includes(option)}
                    onChange={() => handleAvailabilityChange(option)}
                    className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 dark:border-gray-600 rounded"
                  />
                  <label
                    htmlFor={`availability-${option}`}
                    className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
                  >
                    {option}
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* Portfolio Evidence */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Portfolio Evidence (Optional)
            </label>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
              Add links to your previous work to strengthen your proposal.
            </p>

            {/* Display submitted evidence */}
            {portfolioEvidence.length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Submitted Evidence ({portfolioEvidence.length})
                </h4>

                {/* Use EvidenceGallery to display embedded evidence */}
                <EvidenceGallery
                  evidence={portfolioEvidence}
                  title=""
                  className="mb-4"
                />

                {/* Simple list for management */}
                <div className="space-y-2 border border-gray-200 dark:border-gray-700 rounded-md p-3 mb-4">
                  {portfolioEvidence.map((evidence, index) => (
                    <div key={evidence.id} className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <div>
                        <p className="font-medium">{evidence.title}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{evidence.embedService || evidence.embedType || 'Evidence'}</p>
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          const updatedEvidence = [...portfolioEvidence];
                          updatedEvidence.splice(index, 1);
                          setPortfolioEvidence(updatedEvidence);
                        }}
                        className="text-red-500 hover:text-red-700"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {showEvidenceForm ? (
              <EvidenceSubmitter
                evidence={[]}
                onChange={(updatedEvidence) => {
                  // Add the new evidence to the existing array
                  setPortfolioEvidence([...portfolioEvidence, ...updatedEvidence]);

                  // Hide the form after successful submission
                  setShowEvidenceForm(false);

                  // Show success message
                  console.log('Evidence added successfully:', updatedEvidence);

                  // Show a subtle success message to the user
                  setSuccessMessage('Evidence added successfully');

                  // Clear the success message after 3 seconds
                  setTimeout(() => {
                    setSuccessMessage(null);
                  }, 3000);
                }}
              />
            ) : (
              <button
                type="button"
                onClick={() => setShowEvidenceForm(true)}
                className="mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Add Evidence
              </button>
            )}
          </div>

          {/* Submit Button */}
          <div className="flex flex-col space-y-4">
            {portfolioEvidence.length > 0 && !showEvidenceForm && (
              <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md">
                <p className="text-gray-700 dark:text-gray-300">
                  <span className="font-medium text-orange-500 dark:text-orange-400">✓</span> Evidence added successfully
                </p>
              </div>
            )}

            <div className="flex space-x-4">
              <button
                type="button"
                className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-colors duration-200"
                disabled={isSubmitting}
                onClick={() => {
                  console.log('Submit button clicked directly');
                  submitProposal();
                }}
              >
                {isSubmitting ? 'Submitting...' : 'Submit Proposal'}
              </button>
              <button
                type="button"
                onClick={onCancel}
                className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800"
                disabled={isSubmitting}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default TradeProposalForm;
