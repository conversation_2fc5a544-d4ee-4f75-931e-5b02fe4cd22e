import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Trade, User } from '../../../services/firestore';
import { TradeSkillDisplay } from './TradeSkillDisplay';
import ProfileImageWithUser from '../../ui/ProfileImageWithUser';
import { getTradeStatusClasses, formatStatus } from '../../../utils/statusUtils';
import { themeClasses } from '../../../utils/themeUtils';
import { cn } from '../../../utils/cn';

interface TradeCardProps {
  trade: Trade;
  tradeCreator?: User;
  formatDate: (date: Date) => string;
  style?: React.CSSProperties;
}

const TradeCard: React.FC<TradeCardProps> = ({ trade, tradeCreator, formatDate }) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <motion.div
      className={cn(
        themeClasses.card,
        "rounded-lg shadow-sm",
        themeClasses.border,
        "overflow-hidden h-full flex flex-col",
        "transition-all duration-300"
      )}
      initial={{ y: 0 }}
      whileHover={{
        y: -8,
        boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)",
        transition: { duration: 0.2, ease: "easeOut" }
      }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <motion.div
        className="p-6 flex flex-col h-full relative z-10"
        animate={isHovered ? {
          backgroundColor: "rgba(255, 255, 255, 0.03)"
        } : {}}
        transition={{ duration: 0.2 }}
      >
        {/* Header section with user info and category/status */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex items-center">
            <div className="flex-shrink-0 mr-3">
              <ProfileImageWithUser
                userId={trade.creatorId || trade._userId || ''}
                profileUrl={
                  tradeCreator?.profilePicture ||
                  tradeCreator?.photoURL
                }
                size="small"
                className="w-10 h-10 rounded-full"
              />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 line-clamp-1">{trade.title}</h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Posted by {tradeCreator?.displayName || 'Unknown User'}
              </p>
            </div>
          </div>
          <div className="flex flex-col gap-1 items-end ml-2 flex-shrink-0">
            <span className="bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 text-xs font-medium px-2.5 py-0.5 rounded h-fit whitespace-nowrap">
              {trade.category}
            </span>
            <span className={`${getTradeStatusClasses(trade.status)} text-xs font-medium px-2.5 py-0.5 rounded h-fit whitespace-nowrap`}>
              {formatStatus(trade.status)}
            </span>
          </div>
        </div>

        {/* Description with truncation */}
        <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2 overflow-hidden">{trade.description}</p>

        {/* Offering section with better overflow handling */}
        <div className="mb-4">
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Offering:</p>
          <div className="flex flex-wrap gap-1 max-h-16 overflow-y-auto">
            {trade.offeredSkills && trade.offeredSkills.length > 0 ? (
              trade.offeredSkills.map((skill, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.3,
                    delay: index * 0.05,
                    ease: [0.22, 1, 0.36, 1]
                  }}
                >
                  <TradeSkillDisplay
                    skill={skill}
                    className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                  />
                </motion.div>
              ))
            ) : trade.offering ? (
              trade.offering.split(',').map((skill, index) => (
                <motion.span
                  key={index}
                  className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-xs font-medium px-2 py-0.5 rounded whitespace-nowrap"
                  whileHover={{
                    scale: 1.05,
                    backgroundColor: "rgba(74, 222, 128, 0.2)",
                    transition: { duration: 0.2 }
                  }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.3,
                    delay: index * 0.05,
                    ease: [0.22, 1, 0.36, 1]
                  }}
                >
                  {skill.trim()}
                </motion.span>
              ))
            ) : (
              <span className="text-gray-500 dark:text-gray-400">No skills offered</span>
            )}
          </div>
        </div>

        {/* Seeking section with better overflow handling */}
        <div className="mb-4">
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Seeking:</p>
          <div className="flex flex-wrap gap-1 max-h-16 overflow-y-auto">
            {trade.requestedSkills && trade.requestedSkills.length > 0 ? (
              trade.requestedSkills.map((skill, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.3,
                    delay: 0.2 + (index * 0.05),
                    ease: [0.22, 1, 0.36, 1]
                  }}
                >
                  <TradeSkillDisplay
                    skill={skill}
                    className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300"
                  />
                </motion.div>
              ))
            ) : trade.seeking ? (
              trade.seeking.split(',').map((skill, index) => (
                <motion.span
                  key={index}
                  className="bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-xs font-medium px-2 py-0.5 rounded whitespace-nowrap"
                  whileHover={{
                    scale: 1.05,
                    backgroundColor: "rgba(56, 189, 248, 0.2)",
                    transition: { duration: 0.2 }
                  }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.3,
                    delay: 0.2 + (index * 0.05), // Delay after offering skills
                    ease: [0.22, 1, 0.36, 1]
                  }}
                >
                  {skill.trim()}
                </motion.span>
              ))
            ) : (
              <span className="text-gray-500 dark:text-gray-400">No skills requested</span>
            )}
          </div>
        </div>

        {/* Footer section with date and link - pushed to bottom with mt-auto */}
        <motion.div
          className="flex justify-between items-center mt-auto pt-4 border-t border-gray-100 dark:border-gray-700"
          animate={isHovered ? { borderColor: "rgba(251, 146, 60, 0.3)" } : {}}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center">
            <span className="text-sm text-gray-500 dark:text-gray-400 truncate">
              {trade.createdAt ? `Posted ${formatDate(trade.createdAt.toDate())}` : 'Recently posted'}
            </span>
          </div>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link
              to={`/trades/${trade.id}`}
              className={cn(
                "text-orange-500 hover:text-orange-700 dark:hover:text-orange-300 font-medium ml-2 flex-shrink-0",
                "relative group"
              )}
            >
              <span>View Details</span>
              <motion.span
                className="absolute bottom-0 left-0 w-0 h-0.5 bg-orange-500 dark:bg-orange-400"
                animate={isHovered ? { width: "100%" } : { width: "0%" }}
                transition={{ duration: 0.2 }}
              />
            </Link>
          </motion.div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default TradeCard;
