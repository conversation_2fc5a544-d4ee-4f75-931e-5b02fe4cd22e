import React, { useState, useEffect } from 'react';
import { getUserReviews, Review } from '../../../services/firestore';
import { ReviewsList } from './ReviewsList';
// import { themeClasses } from '../../../utils/themeUtils';

interface ReviewsTabProps {
  userId: string;
}

export const ReviewsTab: React.FC<ReviewsTabProps> = ({ userId }) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReviews = async () => {
      setLoading(true);
      setError(null);

      try {
        const reviewResult = await getUserReviews(userId);
if (reviewResult.error) {
  console.error('Error fetching user reviews:', reviewResult.error);
  // Handle the error as needed
}
const { reviews, error: fetchError } = reviewResult;
const data = reviews; // Adjust according to the actual structure

        if (fetchError) {
          throw new Error(fetchError.message);
        }

        if (data) {
          // Sort reviews by date (newest first)
          const sortedReviews = (data as Review[]).sort((a, b) => {
            const dateA = a.createdAt?.toDate() || new Date(0);
            const dateB = b.createdAt?.toDate() || new Date(0);
            return dateB.getTime() - dateA.getTime();
          });

          setReviews(sortedReviews);
        }
      } catch (err: any) {
        setError(err.message || 'Failed to fetch reviews');
      } finally {
        setLoading(false);
      }
    };

    fetchReviews();
  }, [userId]);

  return (
    <div>
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-4 transition-colors duration-200">
          {error}
        </div>
      )}

      <ReviewsList
        reviews={reviews}
        loading={loading}
        emptyMessage="This user has no reviews yet."
      />
    </div>
  );
};
