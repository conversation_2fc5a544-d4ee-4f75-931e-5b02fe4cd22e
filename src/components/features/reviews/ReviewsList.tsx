import React from 'react';
import { Link } from 'react-router-dom';
import { Review } from '../../../services/firestore';
import { StarRating } from './StarRating';
// import { themeClasses } from '../../../utils/themeUtils';

interface ReviewsListProps {
  reviews: Review[];
  loading: boolean;
  emptyMessage?: string;
}

export const ReviewsList: React.FC<ReviewsListProps> = ({
  reviews,
  loading,
  emptyMessage = 'No reviews yet'
}) => {
  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-4 border-gray-200 dark:border-gray-700 border-t-orange-500 dark:border-t-orange-400"></div>
      </div>
    );
  }

  if (reviews.length === 0) {
    return (
      <div className="text-center py-8">
        <svg
          className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1}
            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
          />
        </svg>
        <p className="mt-2 text-gray-500 dark:text-gray-400">{emptyMessage}</p>
      </div>
    );
  }

  // Calculate average rating
  const averageRating = reviews.reduce((acc, review) => acc + review.rating, 0) / reviews.length;

  return (
    <div>
      <div className="flex items-center mb-4">
        <div className="flex items-center">
          <StarRating rating={Math.round(averageRating)} readOnly size="md" />
          <span className="ml-2 text-gray-700 dark:text-gray-300">
            {averageRating.toFixed(1)} out of 5 ({reviews.length} {reviews.length === 1 ? 'review' : 'reviews'})
          </span>
        </div>
      </div>

      <div className="space-y-4">
        {reviews.map((review) => (
          <div key={review.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800 transition-colors duration-200">
            <div className="flex justify-between items-start">
              <div className="flex items-start">
                <div className="flex-shrink-0 mr-3">
                  <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400">
                    {review.reviewerName.charAt(0).toUpperCase()}
                  </div>
                </div>
                <div>
                  <div className="flex items-center">
                    <Link
                      to={`/profile/${review.reviewerId}`}
                      className="text-sm font-medium text-gray-900 dark:text-gray-100 hover:underline transition-colors duration-200"
                    >
                      {review.reviewerName}
                    </Link>
                  </div>
                  <div className="mt-1">
                    <StarRating rating={review.rating} readOnly size="sm" />
                  </div>
                  <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">{review.comment}</p>
                </div>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400">
                {review.createdAt && formatDate(review.createdAt.toDate())}
              </div>
            </div>

            {review.tradeId && (
              <div className="mt-2 pt-2 border-t border-gray-100 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400">
                <span>Trade: </span>
                <Link
                  to={`/trades/${review.tradeId}`}
                  className="text-orange-500 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300 transition-colors duration-200"
                >
                  View trade
                </Link>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
