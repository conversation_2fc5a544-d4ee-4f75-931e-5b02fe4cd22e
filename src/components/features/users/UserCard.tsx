import React from 'react';
import { Link } from 'react-router-dom';
import { User } from '../../../services/firestore';
import ProfileHoverCard from '../../ui/ProfileHoverCard';
import { ReputationBadge } from '../../ui/ReputationBadge';
import SkillBadge from '../../ui/SkillBadge';
import ConnectionButton from '../connections/ConnectionButton';
import ProfileImage from '../../ui/ProfileImage';
import JohnRobertsProfileImage from '../../ui/JohnRobertsProfileImage';
import { MapPin } from '../../../utils/icons';
import { themeClasses } from '../../../utils/themeUtils';

interface UserCardProps {
  user: User;
  currentUserId?: string | null;
  style?: React.CSSProperties;
  parseSkills: (skills?: string | string[] | any) => { name: string; level?: string }[];
}

const UserCard: React.FC<UserCardProps> = ({ user, currentUserId, parseSkills }) => {
  return (
    <div
      className={`${themeClasses.card} rounded-lg shadow-sm ${themeClasses.border} overflow-hidden hover:shadow-md hover:-translate-y-1 dark:hover:bg-neutral-700/70 dark:hover:shadow-[0_0_12px_rgba(251,146,60,0.15)] ${themeClasses.transition}`}
    >
      <div className="p-6">
        <div className="flex items-start justify-between space-x-4">
          <div className="flex items-center flex-1 min-w-0 overflow-hidden">
            <ProfileHoverCard
              userId={user.id}
              displayName={user.displayName || 'Anonymous'}
              photoURL={user.photoURL}
              bio={user.bio}
              reputationScore={user.reputationScore}
            >
              <div className="flex items-center">
                {user.id === 'TozfQg0dAHe4ToLyiSnkDqe3ECj2' ? (
                  <JohnRobertsProfileImage
                    size="md"
                    className="mr-4"
                  />
                ) : (
                  <ProfileImage
                    photoURL={user.photoURL}
                    profilePicture={user.profilePicture}
                    displayName={user.displayName || 'Anonymous'}
                    size="md"
                    className="mr-4"
                  />
                )}
                <div className="min-w-0 flex-1">
                  <h3 className={`text-lg font-medium ${themeClasses.text} hover:text-orange-600 dark:hover:text-orange-500 transition-colors duration-200 truncate block`}>
                    {user.displayName || 'Anonymous'}
                  </h3>
                  {user.reputationScore && (
                    <div className="mt-1">
                      <ReputationBadge score={user.reputationScore} size="sm" />
                    </div>
                  )}
                </div>
              </div>
            </ProfileHoverCard>
          </div>

          {currentUserId && (
            <div className="flex-shrink-0">
              <ConnectionButton
                userId={user.id}
                userName={user.displayName || 'Anonymous'}
                userPhotoURL={user.photoURL}
              />
            </div>
          )}
        </div>

        {user.bio && (
          <p className={`mt-4 text-sm ${themeClasses.textMuted} line-clamp-2`}>
            {user.bio}
          </p>
        )}

        {user.location && (
          <div className={`mt-4 flex items-center text-sm ${themeClasses.textMuted}`}>
            <MapPin className="mr-1.5 h-4 w-4 text-gray-400 dark:text-gray-500" />
            <span>{user.location}</span>
          </div>
        )}

        {user.skills && (
          <div className="mt-4">
            <div className="flex flex-wrap gap-1.5">
              {(() => {
                try {
                  const parsedSkills = parseSkills(user.skills);
                  return (
                    <>
                      {parsedSkills.slice(0, 3).map((skill, index) => (
                        <SkillBadge
                          key={`${user.id}-${skill.name}-${index}`}
                          skill={skill.name}
                          level={skill.level as any}
                        />
                      ))}

                      {parsedSkills.length > 3 && (
                        <span className="inline-flex items-center rounded-full bg-gray-100 dark:bg-gray-700 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:text-gray-300 transition-colors duration-200">
                          +{parsedSkills.length - 3} more
                        </span>
                      )}
                    </>
                  );
                } catch (error) {
                  console.error('Error rendering skills:', error);
                  return null;
                }
              })()}
            </div>
          </div>
        )}

        <div className="mt-6">
          <Link
            to={`/profile/${user.id}`}
            className="text-orange-600 hover:text-orange-700 dark:text-orange-500 dark:hover:text-orange-400 font-medium text-sm transition-colors duration-200"
          >
            View Profile
          </Link>
        </div>
      </div>
    </div>
  );
};

export default UserCard;
