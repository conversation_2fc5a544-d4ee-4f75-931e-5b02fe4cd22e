/**
 * EvidenceDisplay Component
 *
 * A component for displaying embedded evidence with expand/collapse functionality.
 */

import React, { useState } from 'react';
import { EmbeddedEvidence } from '../../../utils/embedUtils';
import { Card } from '../../ui/Card';
import { formatDate } from '../../../utils/dateUtils';

interface EvidenceDisplayProps {
  evidence: EmbeddedEvidence;
  className?: string;
}

export const EvidenceDisplay: React.FC<EvidenceDisplayProps> = ({
  evidence,
  className = ''
}) => {
  const [expanded, setExpanded] = useState(false);

  // Get icon based on evidence type
  const getTypeIcon = () => {
    switch (evidence.embedType) {
      case 'image':
        return <ImageIcon className="h-5 w-5" />;
      case 'video':
        return <VideoIcon className="h-5 w-5" />;
      case 'audio':
        return <AudioIcon className="h-5 w-5" />;
      case 'document':
        return <DocumentIcon className="h-5 w-5" />;
      case 'code':
        return <CodeIcon className="h-5 w-5" />;
      case 'design':
        return <DesignIcon className="h-5 w-5" />;
      default:
        return <LinkIcon className="h-5 w-5" />;
    }
  };

  return (
    <Card className={`overflow-hidden hover:shadow-md transition-shadow duration-300 ${className}`}>
      <Card.Body className="p-5">
        <div className="flex items-start justify-between">
          <div className="flex items-center">
            {getTypeIcon()}
            <h4 className="ml-2 text-lg font-medium">{evidence.title}</h4>
          </div>
          <span className="px-2 py-1 text-xs rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-300 font-medium">
            {evidence.embedService}
          </span>
        </div>

        <p className="mt-2 text-neutral-700 dark:text-neutral-300">
          {evidence.description}
        </p>

        {evidence.userName && (
          <div className="mt-2 flex items-center text-sm text-neutral-500 dark:text-neutral-400">
            {evidence.userPhotoURL && (
              <img
                src={evidence.userPhotoURL}
                alt={evidence.userName}
                className="w-5 h-5 rounded-full mr-1"
              />
            )}
            <span>Submitted by {evidence.userName}</span>
            <span className="mx-1">•</span>
            <span>{formatDate(evidence.createdAt)}</span>
          </div>
        )}

        {!expanded ? (
          <div
            className="mt-4 cursor-pointer relative rounded-lg overflow-hidden"
            onClick={() => setExpanded(true)}
          >
            {evidence.thumbnailUrl ? (
              <img
                src={evidence.thumbnailUrl}
                alt={evidence.title}
                className="w-full h-48 object-cover"
              />
            ) : (
              <div className="w-full h-48 bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center">
                {getTypeIcon()}
              </div>
            )}
            <div className="absolute inset-0 bg-black bg-opacity-30 flex items-center justify-center">
              <span className="text-white font-medium px-4 py-2 rounded-md bg-primary-500 hover:bg-primary-600 shadow-md transition-all duration-200 transform hover:scale-105">
                Click to view
              </span>
            </div>
          </div>
        ) : (
          <div className="mt-4">
            <div className="embed-container rounded-lg overflow-hidden border border-neutral-200 dark:border-neutral-700 bg-white dark:bg-neutral-800 shadow-sm">
              {evidence.embedCode ? (
                <div dangerouslySetInnerHTML={{ __html: evidence.embedCode }} />
              ) : (
                <a
                  href={evidence.originalUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block p-4 text-center bg-neutral-100 dark:bg-neutral-700 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-200 font-medium"
                >
                  View on {evidence.embedService}
                </a>
              )}
            </div>
            <div className="mt-2 flex justify-between">
              <button
                className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-200"
                onClick={() => setExpanded(false)}
              >
                Collapse
              </button>
              <a
                href={evidence.originalUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors duration-200"
              >
                View Original
              </a>
            </div>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

// Icon components
const ImageIcon = ({ className = '' }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

const VideoIcon = ({ className = '' }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
  </svg>
);

const AudioIcon = ({ className = '' }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
  </svg>
);

const DocumentIcon = ({ className = '' }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
  </svg>
);

const CodeIcon = ({ className = '' }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
  </svg>
);

const DesignIcon = ({ className = '' }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
  </svg>
);

const LinkIcon = ({ className = '' }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
  </svg>
);
