/**
 * EvidenceGallery Component
 *
 * A component for displaying multiple evidence items in a gallery format.
 */

import React from 'react';
import { EvidenceDisplay } from './EvidenceDisplay';
import { Card } from '../../ui/Card';
import { EvidenceGalleryProps } from '../../../types/evidence';

export const EvidenceGallery: React.FC<EvidenceGalleryProps> = ({
  evidence,
  onRemove,
  isEditable,
  title = 'Evidence',
  emptyMessage = 'No evidence has been submitted yet.',
  className = ''
}) => {
  if (!evidence || evidence.length === 0) {
    return (
      <div className={className}>
        {title && <h3 className="text-xl font-semibold mb-4 text-neutral-800 dark:text-neutral-200">{title}</h3>}
        <Card className="p-8 text-center border border-neutral-200 dark:border-neutral-700 shadow-sm">
          <p className="text-neutral-500 dark:text-neutral-400">{emptyMessage}</p>
        </Card>
      </div>
    );
  }

  return (
    <div className={className}>
      {title && <h3 className="text-xl font-semibold mb-4 text-neutral-800 dark:text-neutral-200">{title}</h3>}
      <div className="space-y-4">
        {evidence.map((item) => (
          <div key={item.id} className="relative">
            <EvidenceDisplay
              key={item.id}
              evidence={item}
            />
            {isEditable && onRemove && (
              <button
                onClick={() => onRemove(item.id)}
                className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
                aria-label="Remove evidence"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
