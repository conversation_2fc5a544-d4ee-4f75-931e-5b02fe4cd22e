import React, { useState, useEffect } from 'react';
import { useAuth } from '../../../AuthContext';
import { Collaboration, COLLECTIONS } from '../../../services/firestore';
import { collaborationRoleService } from '../../../services/collaborationRoles';
import { db, Timestamp } from '../../../firebase-config';
import { CollaborationRoleData } from '../../../types/collaboration';
import { doc, collection, runTransaction, getDocs } from 'firebase/firestore';
import { logTransaction } from '../../../utils/transactionLogging';
import { useToast } from '../../../contexts/ToastContext';
import { Plus } from 'lucide-react';
import EnhancedInput from '../../ui/EnhancedInput';
import { AnimatePresence } from 'framer-motion';
import { RoleDefinitionForm } from '../../collaboration/RoleDefinitionForm';
import { Modal } from '../../ui/Modal';
import { themeClasses } from '../../../utils/themeUtils';
import { cn } from '../../../utils/cn';

type CollaborationFormProps = {
  collaboration?: Collaboration;
  onSuccess: (collaborationId: string) => void;
  onCancel: () => void;
  isCreating?: boolean;
};

const CollaborationForm: React.FC<CollaborationFormProps> = ({
  collaboration,
  onSuccess,
  onCancel,
  isCreating = false
}) => {
  // Add this log to debug the incoming collaboration object
  useEffect(() => {
    console.log('Collaboration prop:', collaboration);
  }, [collaboration]);

  const { currentUser } = useAuth();
  const { addToast } = useToast();
  const [userProfile, setUserProfile] = useState<any>(null);

  const [title, setTitle] = useState(collaboration?.title || '');
  const [description, setDescription] = useState(collaboration?.description || '');
  const [roles, setRoles] = useState<CollaborationRoleData[]>([]);
  const [roleUpdateCounter, setRoleUpdateCounter] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [editingRoleIndex, setEditingRoleIndex] = useState<number | null>(null);

  // Fetch user profile
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (currentUser) {
        try {
          const { getUserProfile } = await import('../../../services/firestore');
          const { data, error } = await getUserProfile(currentUser.uid);
          if (error) {
            console.error('Error fetching user profile:', error);
            addToast('error', 'Failed to load user profile');
          } else if (data) {
            setUserProfile(data);
          }
        } catch (err) {
          console.error('Error loading user profile:', err);
          addToast('error', 'Failed to load user profile');
        }
      }
    };

    fetchUserProfile();
  }, [currentUser, addToast]);

  useEffect(() => {
    const loadRoles = async () => {
      if (collaboration && collaboration.id) {
        try {
          const { getRoles } = await import('../../../services/collaborations');
          const currentRoles = await getRoles(collaboration.id);
          if (Array.isArray(currentRoles)) {
            setRoles(currentRoles);
          } else {
            console.warn('Received invalid roles data:', currentRoles);
            setRoles([]);
          }
        } catch (err) {
          console.error('Error loading roles:', err);
          addToast('error', 'Failed to load roles');
          setRoles([]);
        }
      }
    };

    loadRoles();
  }, [collaboration, addToast]);

  // Monitor roles updates
  useEffect(() => {
    if (roleUpdateCounter > 0) {
      console.log('Roles updated:', roles);
    }
  }, [roles, roleUpdateCounter]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Basic validation (unchanged)
    if (!currentUser || !userProfile) {
      addToast('error', 'You must be logged in to create or update a collaboration');
      return;
    }

    if (!roles.length) {
      addToast('error', 'Please add at least one role to the collaboration');
      return;
    }

    // Enhance validation of collaboration ID
    if (!isCreating) {
      if (!collaboration) {
        addToast('error', 'Cannot update collaboration: missing collaboration object');
        setError('Collaboration data is missing. Please try again or reload the page.');
        return;
      }
      
      if (!collaboration.id) {
        addToast('error', 'Cannot update collaboration: missing collaboration ID');
        console.error('Collaboration object has no ID:', collaboration);
        setError('Collaboration ID is missing. Please try again or reload the page.');
        return;
      }
    }

    setLoading(true);
    setError(null);

    try {
      if (isCreating) {
        const now = Timestamp.now();

        const collaborationData = {
          title,
          description,
          creatorId: currentUser.uid,
          creatorName: userProfile.displayName || 'Anonymous',
          creatorPhotoURL: userProfile.photoURL || userProfile.profilePicture,
          status: 'recruiting',
          createdAt: now,
          updatedAt: now,
          roleCount: roles.length,
          filledRoleCount: 0,
          completedRoleCount: 0,
          participants: [currentUser.uid]
        };

        const { id } = await runTransaction(db, async (transaction) => {
          logTransaction({
            operation: 'createCollaboration',
            timestamp: Date.now(),
            details: { title, description, roleCount: roles.length },
            status: 'started'
          });

          // Create collaboration document
          const collabRef = doc(collection(db, COLLECTIONS.COLLABORATIONS));
          transaction.set(collabRef, collaborationData);

          // Log collaboration creation
          logTransaction({
            operation: 'createCollaboration',
            timestamp: Date.now(),
            details: { collaborationId: collabRef.id, data: collaborationData },
            status: 'completed'
          });
          
          // Create roles subcollection and documents
          const rolesCollection = collection(collabRef, 'roles');
          for (const role of roles) {
            logTransaction({
              operation: 'createRole',
              timestamp: Date.now(),
              details: { roleTitle: role.title },
              status: 'started'
            });

            const roleRef = doc(rolesCollection);
            // Ensure the role is created with valid data
            transaction.set(roleRef, {
              title: role.title || 'Untitled Role',
              description: role.description || '',
              requiredSkills: Array.isArray(role.requiredSkills) ? role.requiredSkills : [],
              preferredSkills: Array.isArray(role.preferredSkills) ? role.preferredSkills : [],
              collaborationId: collabRef.id, // This is safe now due to our enhanced validation
              status: 'open',
              applicationCount: 0,
              createdAt: now,
              updatedAt: now
            });

            logTransaction({
              operation: 'createRole',
              timestamp: Date.now(),
              details: { roleId: roleRef.id, roleTitle: role.title },
              status: 'completed'
            });
          }
          
          // Update role count in collaboration
          transaction.update(collabRef, {
            roleCount: roles.length,
            updatedAt: now
          });

          return { id: collabRef.id };
        });

        addToast('success', 'Collaboration created successfully');
        onSuccess(id);
      } else if (collaboration && collaboration.id) {
        // Now we're sure collaboration.id exists
        await runTransaction(db, async (transaction) => {
          logTransaction({
            operation: 'updateCollaboration',
            timestamp: Date.now(),
            details: { collaborationId: collaboration.id, title, description },
            status: 'started'
          });

          // Ensure the collaboration ID exists
          if (!collaboration.id) {
            throw new Error('Collaboration ID is missing');
          }
          
          const collaborationRef = doc(db, COLLECTIONS.COLLABORATIONS, collaboration.id);
          const collaborationDoc = await transaction.get(collaborationRef);
          
          if (!collaborationDoc.exists()) {
            throw new Error('Collaboration not found');
          }

          // Update collaboration document
          transaction.update(collaborationRef, {
            title,
            description,
            updatedAt: Timestamp.now()
          });

          // Handle roles (update, create, delete as needed)
          const rolesCollection = collection(collaborationRef, 'roles');
          
          // Get existing roles from Firestore for comparison
          const existingRolesSnapshot = await getDocs(rolesCollection);
          const existingRoles = existingRolesSnapshot.docs.map(doc => {
            const docData = doc.data();
            return {
              id: doc.id,
              ...docData as Record<string, any>
            } as CollaborationRoleData;
          });
          
          console.log('Existing roles in Firestore:', 
            existingRoles.map(r => ({ id: r.id, title: r.title }))
          );
          console.log('Current roles in state:', 
            roles.map(r => ({ id: r.id, title: r.title, isTemp: r.id.startsWith('temp-') }))
          );

          // Process each role in our state
          for (const role of roles) {
            // Skip roles that have already been created in Firebase directly
            if (!role.id.startsWith('temp-')) {
              // Handle updating existing roles if needed
              // This is already covered by the modifyRole function called elsewhere
              continue; 
            }

            // This is a temporary role that needs to be created in Firebase
            console.log('Creating new role in handleSubmit:', role.title);
            
            const roleRef = doc(rolesCollection);
            // Create clean role data without the temp ID
            const { id, ...roleData } = role;
            
            // Add the role document
            transaction.set(roleRef, {
              ...roleData,
              id: roleRef.id,
              collaborationId: collaboration.id,
              createdAt: Timestamp.now(),
              updatedAt: Timestamp.now()
            });

            logTransaction({
              operation: 'createRoleInTransaction',
              timestamp: Date.now(),
              details: { roleId: roleRef.id, roleTitle: role.title },
              status: 'completed'
            });
          }
          
          // Log completion
          logTransaction({
            operation: 'updateCollaboration',
            timestamp: Date.now(),
            details: { 
              collaborationId: collaboration.id, 
              updatedTitle: title,
              roleCount: roles.length 
            },
            status: 'completed'
          });
        });

        addToast('success', 'Collaboration updated successfully');
        onSuccess(collaboration.id);
      }
    } catch (error) {
      console.error('Error with collaboration:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      
      logTransaction({
        operation: isCreating ? 'createCollaboration' : 'updateCollaboration',
        timestamp: Date.now(),
        details: { error: errorMessage },
        status: 'failed',
        error: errorMessage
      });
      
      setError(errorMessage);
      addToast('error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenRoleModal = (roleId?: string): void => {
    const roleIndex = roleId ? roles.findIndex(r => r.id === roleId) : null;
    setEditingRoleIndex(roleIndex);
    setShowRoleModal(true);
  };

  const handleRoleSubmit = async (roleData: Partial<CollaborationRoleData>): Promise<void> => {
    try {
      if (!roleData.title || !roleData.description || !roleData.requiredSkills) {
        throw new Error('Missing required role fields');
      }

      const now = Timestamp.now();
      const editingRole = editingRoleIndex !== null ? roles[editingRoleIndex] : null;

      if (editingRole?.id && !editingRole.id.startsWith('temp-') && collaboration) {
        logTransaction({
          operation: 'updateRole',
          timestamp: Date.now(),
          details: { roleId: editingRole.id, roleTitle: roleData.title },
          status: 'started'
        });

        // Update existing role in Firebase
        const updatedRole = {
          ...editingRole,
          title: roleData.title,
          description: roleData.description,
          requiredSkills: roleData.requiredSkills,
          preferredSkills: roleData.preferredSkills || [],
          updatedAt: now
        };

        setRoles(prevRoles => prevRoles.map(r =>
          r.id === editingRole.id ? updatedRole : r
        ) as CollaborationRoleData[]);

        if (!isCreating) {
          await collaborationRoleService.modifyRole(editingRole.id, {
            title: roleData.title,
            description: roleData.description,
            requiredSkills: roleData.requiredSkills,
            preferredSkills: roleData.preferredSkills || []
          });
          logTransaction({
            operation: 'updateRole',
            timestamp: Date.now(),
            details: { roleId: editingRole.id, roleTitle: roleData.title },
            status: 'completed'
          });
        }
      } else {
        logTransaction({
          operation: 'createRole',
          timestamp: Date.now(),
          details: { roleTitle: roleData.title },
          status: 'started'
        });

        // Create new role with temporary ID
        const tempId = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        const newRole: CollaborationRoleData = {
          id: tempId,
          collaborationId: collaboration?.id || '',
          title: roleData.title,
          description: roleData.description,
          requiredSkills: roleData.requiredSkills,
          preferredSkills: roleData.preferredSkills || [],
          status: 'open',
          applicationCount: 0,
          createdAt: now,
          updatedAt: now,
          childRoleIds: []
        };

        // First update local state
        setRoles(prevRoles => [...prevRoles, newRole]);

        // If editing existing collaboration, create role in Firebase immediately
        if (collaboration?.id && !isCreating) {
          try {
            console.log('Creating role with collaboration ID:', collaboration.id);
            
            // Explicitly using the service with detailed logging
            const roleRef = await collaborationRoleService.createRoleHierarchy([newRole]);
            console.log('Role created successfully in Firebase with ID:', roleRef);
            
            if (roleRef) {
              // Update local state with real Firebase ID
              setRoles(prevRoles => {
                const updatedRoles = prevRoles.map(r => 
                  r.id === tempId ? { ...r, id: roleRef } : r
                ) as CollaborationRoleData[];
                
                console.log('Updated roles after Firebase save:', 
                  updatedRoles.map(r => ({ id: r.id, title: r.title }))
                );
                
                return updatedRoles;
              });
              
              // Log successful creation
              logTransaction({
                operation: 'createRole',
                timestamp: Date.now(),
                details: { roleId: roleRef, roleTitle: roleData.title },
                status: 'completed'
              });
            }
          } catch (error) {
            console.error('Error creating role in Firebase:', error);
            addToast('error', 'Failed to create role in Firebase');
            
            logTransaction({
              operation: 'createRole',
              timestamp: Date.now(),
              details: { error: String(error) },
              status: 'failed'
            });
          }
        }
      }
      
      setShowRoleModal(false);
      setEditingRoleIndex(null);
      
      // Increment counter to trigger useEffect monitoring
      setRoleUpdateCounter(prev => prev + 1);
    } catch (error: unknown) {
      console.error('Error creating role:', error);
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';
      setError(errorMessage);
      addToast('error', errorMessage);
    } finally {
      setShowRoleModal(false);
      setEditingRoleIndex(null);
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    if (window.confirm('Are you sure you want to delete this role?')) {
      try {
        logTransaction({
          operation: 'deleteRole',
          timestamp: Date.now(),
          details: { roleId },
          status: 'started'
        });

        await collaborationRoleService.deleteRole(roleId);

        setRoles(prevRoles => prevRoles.filter(role => role.id !== roleId));

        logTransaction({
          operation: 'deleteRole',
          timestamp: Date.now(),
          details: { roleId },
          status: 'completed'
        });

        addToast('success', 'Role deleted successfully');
      } catch (error) {
        console.error('Error deleting role:', error);
        const errorMessage = error instanceof Error ? error.message : 'An error occurred';
        addToast('error', errorMessage);
      }
    }
  };

  const handleRoleCancel = () => {
    setShowRoleModal(false);
    setEditingRoleIndex(null);
  };

  return (
    <form onSubmit={handleSubmit} className={cn("space-y-4", themeClasses.card, "p-6")}>
      <div>
        <label className={cn("block text-sm font-medium", themeClasses.label)}>
          Collaboration Title
        </label>
        <EnhancedInput
          type="text"
          value={title}
          onChange={e => setTitle(e.target.value)}
          placeholder="Enter collaboration title"
          required
        />
      </div>
      <div>
        <label className={cn("block text-sm font-medium", themeClasses.label)}>
          Description
        </label>
        <textarea
          value={description}
          onChange={e => setDescription(e.target.value)}
          placeholder="Enter collaboration description"
          rows={3}
          required
          className={cn(
            "w-full px-3 py-2 rounded-md",
            themeClasses.input,
            themeClasses.text,
            themeClasses.focus
          )}
        />
      </div>
      <div>
        <label className={cn("block text-sm font-medium", themeClasses.label)}>
          Roles
        </label>
        <div className="space-y-2">
          {roles.map((role) => (
            <div key={role.id} className={cn(
              "flex items-center justify-between p-4 rounded-md",
              themeClasses.cardAlt,
              themeClasses.transition
            )}>
              <div>
                <p className={cn("text-sm font-semibold", themeClasses.text)}>{role.title}</p>
                <p className={cn("text-xs", themeClasses.textMuted)}>{role.description}</p>
              </div>
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={() => handleOpenRoleModal(role.id)}
                  className={cn(
                    "px-3 py-1 text-sm font-medium rounded-md",
                    themeClasses.secondaryButton,
                    themeClasses.transition
                  )}
                >
                  Edit
                </button>
                <button
                  type="button"
                  onClick={() => handleDeleteRole(role.id)}
                  className={cn(
                    "px-3 py-1 text-sm font-medium rounded-md",
                    themeClasses.dangerButton,
                    themeClasses.transition
                  )}
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
          <button
            type="button"
            onClick={() => handleOpenRoleModal()}
            className={cn(
              "flex items-center px-3 py-2 text-sm font-medium rounded-md",
              themeClasses.primaryButton,
              themeClasses.transition
            )}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Role
          </button>
        </div>
      </div>
      <div className="flex justify-end space-x-2">
        <button
          type="button"
          onClick={onCancel}
          className={cn(
            "px-3 py-2 text-sm font-medium rounded-md",
            themeClasses.tertiaryButton,
            themeClasses.transition
          )}
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className={cn(
            "px-3 py-2 text-sm font-medium rounded-md",
            themeClasses.primaryButton,
            themeClasses.transition,
            "disabled:opacity-50"
          )}
        >
          {loading ? 'Saving...' : isCreating ? 'Create Collaboration' : 'Update Collaboration'}
        </button>
      </div>

      <AnimatePresence>
        {showRoleModal && (
          <Modal isOpen={showRoleModal} onClose={() => setShowRoleModal(false)}>
            <RoleDefinitionForm
              initialRole={editingRoleIndex !== null ? roles[editingRoleIndex] : undefined}
              onSubmit={handleRoleSubmit}
              onCancel={handleRoleCancel}
            />
          </Modal>
        )}
      </AnimatePresence>

      {error && (
        <div className="p-4 text-sm rounded-md bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800">
          {error}
        </div>
      )}
    </form>
  );
};

export default CollaborationForm;
