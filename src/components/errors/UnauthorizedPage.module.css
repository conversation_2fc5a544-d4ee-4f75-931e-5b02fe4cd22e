.errorPage {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 2rem;
  text-align: center;
}

.errorActions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.primaryButton {
  composes: button;
  background-color: #3b82f6;
  color: white;
  border: none;
}

.primaryButton:hover {
  background-color: #2563eb;
}

.secondaryButton {
  composes: button;
  background-color: transparent;
  border: 1px solid #d1d5db;
}

.secondaryButton:hover {
  background-color: #f3f4f6;
}
