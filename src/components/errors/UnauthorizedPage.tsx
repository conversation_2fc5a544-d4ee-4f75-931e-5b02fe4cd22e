import React from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './UnauthorizedPage.module.css';

const UnauthorizedPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className={styles.errorPage}>
      <h1>401 - Unauthorized</h1>
      <p>You need to be signed in to access this page.</p>
      <div className={styles.errorActions}>
        <button 
          onClick={() => navigate('/login')}
          className={styles.primaryButton}
        >
          Sign In
        </button>
        <button 
          onClick={() => navigate('/')}
          className={styles.secondaryButton}
        >
          Go to Home
        </button>
      </div>
    </div>
  );
};

export default UnauthorizedPage;
