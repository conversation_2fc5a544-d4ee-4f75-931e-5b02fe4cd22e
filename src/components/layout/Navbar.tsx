import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { Home, ShoppingBag, Briefcase, Users, Award, Menu, X } from '../../utils/icons';
import { NotificationBell } from '../features/notifications/NotificationBell';
import { ThemeToggle } from '../ui/ThemeToggle';
import NavItem from '../ui/NavItem';
import UserMenu from '../ui/UserMenu';
import MobileMenu from '../ui/MobileMenu';
import { themeClasses } from '../../utils/themeUtils';

const Navbar: React.FC = () => {
  const { currentUser } = useAuth();
  const location = useLocation();
  useTheme(); // Using the theme context for dark mode support
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Check if a link is active
  const isActive = (path: string) => {
    return location.pathname === path ||
           (path !== '/' && location.pathname.startsWith(path));
  };

  // Handle scroll events to add shadow when scrolled
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 10;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);

  // Main navigation items
  const mainNavItems = [
    { path: '/', label: 'Home', icon: <Home className="h-5 w-5" /> },
    { path: '/trades', label: 'Trades', icon: <ShoppingBag className="h-5 w-5" /> },
    { path: '/projects', label: 'Projects', icon: <Briefcase className="h-5 w-5" /> },
    { path: '/directory', label: 'Directory', icon: <Users className="h-5 w-5" /> },
    { path: '/challenges', label: 'Challenges', icon: <Award className="h-5 w-5" /> }
  ];

  return (
    <nav
      className={`sticky top-0 z-50 bg-white dark:bg-gray-800 ${
        scrolled ? 'shadow-md dark:shadow-gray-900/30' : 'shadow-sm dark:shadow-gray-700/20'
      } ${themeClasses.transition}`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and desktop navigation */}
          <div className="flex items-center">
            {/* Logo */}
            <div className="flex-shrink-0 flex items-center">
              <Link to="/" className="text-2xl font-bold text-orange-500 hover:text-orange-600 dark:hover:text-orange-400 transition-colors duration-200">
                TradeYa
              </Link>
            </div>

            {/* Desktop navigation */}
            <div className="hidden md:ml-8 md:flex md:space-x-4 lg:space-x-8">
              {mainNavItems.map((item) => (
                <NavItem
                  key={item.path}
                  to={item.path}
                  label={item.label}
                  isActive={isActive(item.path)}
                />
              ))}
            </div>
          </div>

          {/* Right side actions */}
          <div className="hidden md:flex md:items-center md:space-x-4">
            <ThemeToggle />

            {currentUser && <NotificationBell />}

            {currentUser ? (
              <UserMenu />
            ) : (
              <div className="flex items-center space-x-4">
                <Link
                  to="/login"
                  className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                >
                  Log In
                </Link>
                <Link
                  to="/signup"
                  className="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                >
                  Sign Up
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="flex items-center md:hidden">
            <ThemeToggle className="mr-2" />

            {currentUser && (
              <NotificationBell className="mr-2" />
            )}

            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 dark:text-gray-300 hover:text-gray-500 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-orange-500 dark:focus:ring-orange-400"
            >
              <span className="sr-only">Open main menu</span>
              {mobileMenuOpen ? (
                <X className="block h-6 w-6" />
              ) : (
                <Menu className="block h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <MobileMenu isOpen={mobileMenuOpen} onClose={() => setMobileMenuOpen(false)} />
    </nav>
  );
};

export default Navbar;
