import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../AuthContext';

interface AdminRouteProps {
  children: React.ReactNode;
}

/**
 * A wrapper component that only renders its children if the current user is an admin.
 * Otherwise, it redirects to the home page.
 */
export const AdminRoute: React.FC<AdminRouteProps> = ({ children }) => {
  const { currentUser, isAdmin, loading } = useAuth();

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  // If not logged in or not an admin, redirect to home page
  if (!currentUser || !isAdmin) {
    return <Navigate to="/" replace />;
  }

  // If user is an admin, render the children
  return <>{children}</>;
};

export default AdminRoute;
