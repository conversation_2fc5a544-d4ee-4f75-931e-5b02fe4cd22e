import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../../AuthContext';
import { rateLimiter } from '../../firebase-config';
import { useToast } from '../../contexts/ToastContext';

interface LoginFormData {
  email: string;
  password: string;
}

interface SecurityLog {
  timestamp: number;
  action: string;
  email: string;
  success: boolean;
  errorMessage?: string;
  ipAddress?: string;
}

const LoginPage: React.FC = () => {
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [attemptCount, setAttemptCount] = useState(0);
  const [isRedirect, setIsRedirect] = useState(false);

  const navigate = useNavigate();
  const { addToast } = useToast();
  const { signInWithEmail, signInWithGoogle } = useAuth();

  // Handle Google sign-in flow including redirects
  useEffect(() => {
    const handleRedirectCompletion = async () => {
      const hasRedirect = localStorage.getItem('auth_redirect');
      if (!hasRedirect) return;

      setIsRedirect(true);
      setLoading(true);
      
      try {
        await signInWithGoogle();
        
        // After successful sign-in, user will be updated in AuthContext
        localStorage.removeItem('auth_redirect');
        logSecurityEvent({
          timestamp: Date.now(),
          action: 'GOOGLE_SIGNIN_REDIRECT_SUCCESS',
          email: 'unknown', // We don't have access to the user here
          success: true
        });
        
        addToast('success', 'Welcome back!');
        navigate('/dashboard');
      } catch (error) {
        localStorage.removeItem('auth_redirect');
        const errorMessage = error instanceof Error ? error.message : 'Google sign-in failed';
        setError(errorMessage);
        
        logSecurityEvent({
          timestamp: Date.now(),
          action: 'GOOGLE_SIGNIN_REDIRECT_FAILED',
          email: 'unknown',
          success: false,
          errorMessage
        });
      } finally {
        setLoading(false);
        setIsRedirect(false);
      }
    };

    handleRedirectCompletion();
  }, [signInWithGoogle, navigate, addToast]);

  // Security logging function
  const logSecurityEvent = (event: SecurityLog) => {
    const existingLogs = JSON.parse(localStorage.getItem('security_logs') || '[]');
    const updatedLogs = [...existingLogs, event].slice(-100); // Keep last 100 logs
    localStorage.setItem('security_logs', JSON.stringify(updatedLogs));
  };

  // Input validation
  const validateInput = (data: LoginFormData): string | null => {
    if (!data.email.trim()) return 'Email is required';
    if (!data.password.trim()) return 'Password is required';
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) return 'Please enter a valid email address';
    
    if (data.password.length < 8) return 'Password must be at least 8 characters long';
    
    return null;
  };

  // Handle email/password login
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate input first, before clearing error
      const validationError = validateInput(formData);
      if (validationError) {
        setError(validationError);
        setLoading(false);
        return;
      }

      // Clear previous error only after validation passes
      setError(null);

      // Rate limiting check
      const userId = `login_${formData.email}`;
      const isAllowed = await rateLimiter.checkLimit(userId);
      if (!isAllowed) {
        setError(`Too many login attempts. Please wait before trying again.`);
        setLoading(false);
        return;
      }

      await signInWithEmail(formData.email, formData.password);
      
      // After successful sign-in, user will be updated in AuthContext
      logSecurityEvent({
        timestamp: Date.now(),
        action: 'EMAIL_SIGNIN_SUCCESS',
        email: formData.email,
        success: true
      });
      
      addToast('success', 'Login successful!');
      navigate('/dashboard');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setError(errorMessage);
      setAttemptCount(prev => prev + 1);
      
      logSecurityEvent({
        timestamp: Date.now(),
        action: 'EMAIL_SIGNIN_FAILED',
        email: formData.email,
        success: false,
        errorMessage
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle Google sign-in with redirect
  const handleGoogleSignIn = async () => {
    setLoading(true);
    setError(null);
    
    try {
      localStorage.setItem('auth_redirect', 'true');
      await signInWithGoogle();
    } catch (error) {
      localStorage.removeItem('auth_redirect');
      const errorMessage = error instanceof Error ? error.message : 'Google sign-in failed';
      setError(errorMessage);
      
      logSecurityEvent({
        timestamp: Date.now(),
        action: 'GOOGLE_SIGNIN_FAILED',
        email: 'unknown',
        success: false,
        errorMessage
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  };

  // Show loading state for redirects
  if (isRedirect) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Completing sign-in...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Or{' '}
            <Link
              to="/register"
              className="font-medium text-blue-600 hover:text-blue-500"
            >
              create a new account
            </Link>
          </p>
        </div>
        
        <form noValidate className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="email" className="sr-only">
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Email address"
                value={formData.email}
                onChange={handleInputChange}
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">
                Password
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Password"
                value={formData.password}
                onChange={handleInputChange}
              />
            </div>
          </div>

          {error && (
            <div className="text-red-600 text-sm text-center" role="alert">
              {error}
            </div>
          )}

          {attemptCount > 2 && (
            <div className="text-yellow-600 text-sm text-center">
              Multiple failed attempts detected. Please verify your credentials.
            </div>
          )}

          <div>
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? 'Signing in...' : 'Sign in'}
            </button>
          </div>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-gray-50 text-gray-500">Or continue with</span>
              </div>
            </div>

            <div className="mt-6">
              <button
                type="button"
                onClick={handleGoogleSignIn}
                disabled={loading}
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                <svg className="w-5 h-5" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span className="ml-2">Sign in with Google</span>
              </button>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <Link
              to="/forgot-password"
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              Forgot your password?
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default LoginPage;
