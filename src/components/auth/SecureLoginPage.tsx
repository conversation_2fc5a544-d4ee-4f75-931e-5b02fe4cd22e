import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../AuthContext';
import { FirebaseError } from 'firebase/app';

interface SecureLoginPageProps {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

const SecureLoginPage: React.FC<SecureLoginPageProps> = ({ onSuccess, onError }) => {
  const { signInWithEmail, signInWithGoogle } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const from = (location.state as any)?.from?.pathname || '/dashboard';

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleEmailSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await signInWithEmail(email, password);
      onSuccess?.();
      navigate(from, { replace: true });
    } catch (err) {
      const errorMessage = err instanceof FirebaseError 
        ? getFirebaseErrorMessage(err.code)
        : 'An unexpected error occurred';
      setError(errorMessage);
      onError?.(err as Error);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setLoading(true);
    setError(null);

    try {
      await signInWithGoogle();
      onSuccess?.();
      navigate(from, { replace: true });
    } catch (err) {
      const errorMessage = err instanceof FirebaseError 
        ? getFirebaseErrorMessage(err.code)
        : 'An unexpected error occurred';
      setError(errorMessage);
      onError?.(err as Error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="secure-login-container">
      <div className="login-form-wrapper">
        <h2>Sign In</h2>
        {error && (
          <div className="error-message" role="alert">
            {error}
          </div>
        )}
        <form onSubmit={handleEmailSignIn}>
          <div className="form-group">
            <label htmlFor="email">Email</label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
              required
            />
          </div>
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
              required
            />
          </div>
          <button type="submit" disabled={loading}>
            {loading ? 'Signing in...' : 'Sign in with Email'}
          </button>
        </form>
        <div className="divider">
          <span>or</span>
        </div>
        <button
          className="google-sign-in"
          onClick={handleGoogleSignIn}
          disabled={loading}
        >
          Sign in with Google
        </button>
      </div>
    </div>
  );
};

const getFirebaseErrorMessage = (errorCode: string): string => {
  switch (errorCode) {
    case 'auth/invalid-email':
      return 'Invalid email address';
    case 'auth/user-disabled':
      return 'This account has been disabled';
    case 'auth/user-not-found':
      return 'No account found with this email';
    case 'auth/wrong-password':
      return 'Incorrect password';
    case 'auth/popup-closed-by-user':
      return 'Sign in cancelled';
    case 'auth/popup-blocked':
      return 'Sign in popup was blocked by browser';
    case 'auth/cancelled-popup-request':
      return 'Sign in cancelled';
    case 'auth/operation-not-allowed':
      return 'Sign in method not enabled';
    default:
      return 'An error occurred during sign in';
  }
};

export default SecureLoginPage;
