import React from 'react';
import { cn } from '../../utils/cn';
import { themeClasses } from '../../utils/themeUtils';

type CardProps = {
  children: React.ReactNode;
  className?: string;
  variant?: 'elevated' | 'outlined' | 'filled' | 'glass';
  hover?: boolean;
  interactive?: boolean;
  hoverable?: boolean; // For backward compatibility
};

// Define the type for the Card component with subcomponents
type CardComponent = React.FC<CardProps> & {
  Header: React.FC<CardHeaderProps>;
  Body: React.FC<CardBodyProps>;
  Footer: React.FC<CardFooterProps>;
};

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

interface CardBodyProps {
  children: React.ReactNode;
  className?: string;
}

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

// Create the Card component
const CardComponent: React.FC<CardProps> = ({
  children,
  className = '',
  variant = 'elevated',
  hover = false,
  interactive = false,
  hoverable = false, // For backward compatibility
}) => {
  const baseClasses = 'rounded-xl overflow-hidden';

  const variantClasses = {
    elevated: `${themeClasses.card} ${themeClasses.shadowMd}`,
    outlined: `bg-transparent border ${themeClasses.border}`,
    filled: 'bg-neutral-50 dark:bg-neutral-800/50',
    glass: 'backdrop-blur-sm bg-white/70 dark:bg-neutral-800/60 border border-white/20 dark:border-neutral-700/30',
  };

  const hoverClasses = (hover || hoverable)
    ? `transition-all duration-300 ${variant === 'glass' ? themeClasses.hoverGlassCard : themeClasses.hoverCard}`
    : '';

  const interactiveClasses = interactive
    ? 'cursor-pointer focus:outline-none focus:ring-2 focus:ring-primary-500 dark:focus:ring-primary-400'
    : '';

  return (
    <div className={cn(
      baseClasses,
      variantClasses[variant],
      hoverClasses,
      interactiveClasses,
      className
    )}>
      {children}
    </div>
  );
};

// Create the memoized Card component
export const Card = React.memo(CardComponent) as unknown as CardComponent;

const CardHeader: React.FC<CardHeaderProps> = React.memo(({ children, className = '' }) => (
  <div className={cn(`px-6 py-4 border-b ${themeClasses.border}`, className)}>
    {children}
  </div>
));

const CardBody: React.FC<CardBodyProps> = React.memo(({ children, className = '' }) => (
  <div className={cn(`px-6 py-5`, className)}>
    {children}
  </div>
));

const CardFooter: React.FC<CardFooterProps> = React.memo(({ children, className = '' }) => (
  <div className={cn(`px-6 py-4 border-t ${themeClasses.border} bg-neutral-50 dark:bg-neutral-800/30`, className)}>
    {children}
  </div>
));

// Attach subcomponents
Card.Header = CardHeader;
Card.Body = CardBody;
Card.Footer = CardFooter;

export { CardHeader, CardBody, CardFooter };
