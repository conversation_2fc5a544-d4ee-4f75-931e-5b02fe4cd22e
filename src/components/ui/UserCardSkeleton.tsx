import React from 'react';
import { themeClasses } from '../../utils/themeUtils';

const UserCardSkeleton: React.FC = () => (
  <div className={`${themeClasses.card} rounded-lg shadow-sm ${themeClasses.border} overflow-hidden animate-pulse ${themeClasses.transition}`}>
    <div className="p-6">
      <div className="flex items-start justify-between space-x-4">
        <div className="flex items-center flex-1 min-w-0">
          <div className="h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700 mr-4"></div>
          <div className="min-w-0 flex-1">
            <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
            <div className="h-3 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
        <div className="h-8 w-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
      <div className="mt-4 h-12 w-full bg-gray-200 dark:bg-gray-700 rounded"></div>
      <div className="mt-4 flex items-center">
        <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded-full mr-2"></div>
        <div className="h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
      </div>
      <div className="mt-4 flex space-x-2">
        <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
        <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
        <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
      </div>
    </div>
  </div>
);

export default UserCardSkeleton;