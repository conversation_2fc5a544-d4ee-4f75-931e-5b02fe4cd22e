import React from 'react';
import { Link } from 'react-router-dom';
import { Home, ShoppingBag, Briefcase, Users, Award, MessageSquare, Bell, User, Settings, Shield, LogOut } from '../../utils/icons';
import { useAuth } from '../../AuthContext';
import NavItem from './NavItem';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export const MobileMenu: React.FC<MobileMenuProps> = ({ isOpen, onClose }) => {
  const { currentUser, logout, isAdmin } = useAuth();

  if (!isOpen) return null;

  const mainNavItems = [
    { to: '/', label: 'Home', icon: <Home /> },
    { to: '/trades', label: 'Trades', icon: <ShoppingBag /> },
    { to: '/projects', label: 'Projects', icon: <Briefcase /> },
    { to: '/directory', label: 'Directory', icon: <Users /> },
    { to: '/challenges', label: 'Challenges', icon: <Award /> }
  ];

  const userNavItems = currentUser ? [
    { to: '/profile', label: 'Profile', icon: <User /> },
    { to: '/dashboard', label: 'Dashboard', icon: <Settings /> },
    { to: '/connections', label: 'Connections', icon: <Users /> },
    { to: '/messages', label: 'Messages', icon: <MessageSquare /> },
    { to: '/notifications', label: 'Notifications', icon: <Bell /> }
  ] : [];

  // Add admin link if user is admin
  if (currentUser && isAdmin) {
    userNavItems.push({ to: '/admin', label: 'Admin', icon: <Shield /> });
  }

  return (
    <div className="sm:hidden">
      <div className="pt-2 pb-3 space-y-1 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <h3 className="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
          Navigation
        </h3>
        {mainNavItems.map((item, index) => (
          <NavItem
            key={index}
            to={item.to}
            label={item.label}
            icon={item.icon}
            variant="mobile"
            onClick={onClose}
          />
        ))}
      </div>

      {currentUser ? (
        <div className="pt-2 pb-3 space-y-1 bg-white dark:bg-gray-800">
          <h3 className="px-4 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
            Account
          </h3>
          {userNavItems.map((item, index) => (
            <NavItem
              key={index}
              to={item.to}
              label={item.label}
              icon={item.icon}
              variant="mobile"
              onClick={onClose}
            />
          ))}
          <button
            onClick={() => {
              logout();
              onClose();
            }}
            className="flex items-center w-full px-3 py-2 text-base font-medium border-l-4 border-transparent text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-200"
          >
            <LogOut className="mr-2 h-5 w-5" />
            Log Out
          </button>
        </div>
      ) : (
        <div className="pt-4 pb-3 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
          <div className="flex items-center justify-center space-x-4 px-4 py-2">
            <Link
              to="/login"
              className="w-full px-4 py-2 text-center border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
              onClick={onClose}
            >
              Log In
            </Link>
            <Link
              to="/signup"
              className="w-full px-4 py-2 text-center border border-transparent rounded-md text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
              onClick={onClose}
            >
              Sign Up
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

export default MobileMenu;
