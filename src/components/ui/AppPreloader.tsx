import { useEffect } from 'react';
import {
  preconnectToCommonDomains,
  preloadFonts,
  preloadImages,
  preloadStyles
} from '../../utils/preloadUtils';

/**
 * Critical resources that should be preloaded as soon as the application starts
 */
const CRITICAL_FONTS: string[] = [
  // We'll let the Google Fonts stylesheet handle loading the correct font files
  // instead of hardcoding specific font file URLs that might change
];

const CRITICAL_IMAGES: string[] = [
  // Add your critical image URLs here (logo, common UI elements)
  // Example: 'https://res.cloudinary.com/doqqhj2nt/image/upload/v1/tradeya/logo.png'
];

const CRITICAL_STYLES: string[] = [
  // Add your critical CSS URLs here if loading external stylesheets
  'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Outfit:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap'
];

/**
 * AppPreloader Component
 *
 * This component handles preloading critical resources when the application first loads.
 * It should be placed high in the component tree and will run once on initial mount.
 */
const AppPreloader = () => {
  useEffect(() => {
    // Preconnect to common domains
    preconnectToCommonDomains();

    // Preload critical styles (including Google Fonts stylesheet)
    if (CRITICAL_STYLES.length > 0) {
      preloadStyles(CRITICAL_STYLES);
    }

    // Preload critical images
    if (CRITICAL_IMAGES.length > 0) {
      preloadImages(CRITICAL_IMAGES);
    }

    // Preload critical fonts (if any)
    // Note: We're now letting the Google Fonts stylesheet handle font loading
    // instead of manually preloading specific font files
    if (CRITICAL_FONTS.length > 0) {
      preloadFonts(CRITICAL_FONTS);
    }

    // Log preloading activity in development
    if (import.meta.env.DEV) {
      console.log('[AppPreloader] Preloaded critical application resources');
    }
  }, []);

  // This component doesn't render anything
  return null;
};

export default AppPreloader;
