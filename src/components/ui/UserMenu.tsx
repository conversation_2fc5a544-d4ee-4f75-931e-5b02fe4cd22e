import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { cn } from '../../utils/cn';
import { User, LogOut, Settings, Shield, Users, MessageSquare, Bell, ChevronDown, Link as LinkIcon } from '../../utils/icons';
import { useAuth } from '../../AuthContext';
import { themeClasses } from '../../utils/themeUtils';

interface UserMenuProps {
  className?: string;
}

export const UserMenu: React.FC<UserMenuProps> = ({ className }) => {
  const { currentUser, logout, isAdmin } = useAuth();
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close the menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  if (!currentUser) return null;

  const menuItems = [
    {
      label: 'Profile',
      icon: <User className="h-4 w-4" />,
      to: '/profile'
    },
    {
      label: 'Dashboard',
      icon: <Settings className="h-4 w-4" />,
      to: '/dashboard'
    },
    {
      label: 'Connections',
      icon: <Users className="h-4 w-4" />,
      to: '/connections'
    },
    {
      label: 'Messages',
      icon: <MessageSquare className="h-4 w-4" />,
      to: '/messages'
    },
    {
      label: 'Notifications',
      icon: <Bell className="h-4 w-4" />,
      to: '/notifications'
    }
  ];

  // Add admin link if user is admin
  if (isAdmin) {
    menuItems.push({
      label: 'Admin',
      icon: <Shield className="h-4 w-4" />,
      to: '/admin'
    });

    // Add Evidence System link for admins (for testing)
    menuItems.push({
      label: 'Evidence System',
      icon: <LinkIcon className="h-4 w-4" />,
      to: '/evidence-demo'
    });
  }

  return (
    <div className={cn('relative', className)} ref={menuRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium',
          themeClasses.transition,
          isOpen
            ? 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100'
            : 'text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'
        )}
      >
        <span>Account</span>
        <ChevronDown className={cn('h-4 w-4 transition-transform', isOpen ? 'rotate-180' : '')} />
      </button>

      {isOpen && (
        <div className={cn(
          'absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 z-10',
          themeClasses.card,
          themeClasses.border,
          themeClasses.shadow
        )}>
          <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
              {currentUser.email}
            </p>
          </div>

          <div className="py-1">
            {menuItems.map((item, index) => (
              <Link
                key={index}
                to={item.to}
                className="flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                onClick={() => setIsOpen(false)}
              >
                <span className="mr-2">{item.icon}</span>
                {item.label}
              </Link>
            ))}
          </div>

          <div className="py-1 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={() => {
                logout();
                setIsOpen(false);
              }}
              className="flex w-full items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <LogOut className="mr-2 h-4 w-4" />
              Log Out
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserMenu;
