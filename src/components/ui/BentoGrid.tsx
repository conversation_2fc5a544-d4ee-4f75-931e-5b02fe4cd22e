import React from 'react';
import { cn } from '../../utils/cn';

type BentoGridProps = {
  children: React.ReactNode;
  className?: string;
  gap?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  columns?: 1 | 2 | 3 | 4 | 6;
  rows?: number;
};

type BentoItemProps = {
  children: React.ReactNode;
  className?: string;
  colSpan?: 1 | 2 | 3 | 4 | 6;
  rowSpan?: 1 | 2 | 3 | 4;
  featured?: boolean;
};

/**
 * BentoGrid component for creating modern grid layouts
 * 
 * @param children - Grid items (BentoItem components)
 * @param className - Additional CSS classes
 * @param gap - Size of gap between grid items
 * @param columns - Number of columns in the grid (default: 6)
 * @param rows - Number of rows in the grid (optional)
 */
export const BentoGrid: React.FC<BentoGridProps> = ({
  children,
  className = '',
  gap = 'md',
  columns = 6,
  rows,
}) => {
  // Define gap sizes
  const gapSizes = {
    none: 'gap-0',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  // Define grid template columns
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
  };

  // Define grid template rows if provided
  const gridRows = rows ? `grid-rows-${rows}` : '';

  return (
    <div
      className={cn(
        'grid',
        gridCols[columns],
        gridRows,
        gapSizes[gap],
        className
      )}
    >
      {children}
    </div>
  );
};

/**
 * BentoItem component for use within a BentoGrid
 * 
 * @param children - Content of the grid item
 * @param className - Additional CSS classes
 * @param colSpan - Number of columns the item spans
 * @param rowSpan - Number of rows the item spans
 * @param featured - Whether this is a featured item (adds special styling)
 */
export const BentoItem: React.FC<BentoItemProps> = ({
  children,
  className = '',
  colSpan = 1,
  rowSpan = 1,
  featured = false,
}) => {
  // Define column span classes
  const colSpanClasses = {
    1: 'md:col-span-1',
    2: 'md:col-span-2',
    3: 'md:col-span-3',
    4: 'md:col-span-4',
    6: 'md:col-span-6',
  };

  // Define row span classes
  const rowSpanClasses = {
    1: 'md:row-span-1',
    2: 'md:row-span-2',
    3: 'md:row-span-3',
    4: 'md:row-span-4',
  };

  // Featured item gets special styling
  const featuredClasses = featured
    ? 'ring-2 ring-primary-500 dark:ring-primary-400 shadow-lg dark:shadow-primary-500/10'
    : '';

  return (
    <div
      className={cn(
        'rounded-xl overflow-hidden',
        colSpanClasses[colSpan],
        rowSpanClasses[rowSpan],
        featuredClasses,
        className
      )}
    >
      {children}
    </div>
  );
};

export default BentoGrid;
