import React, { forwardRef } from 'react';
import { cn } from '../../utils/cn';
import { themeClasses } from '../../utils/themeUtils';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  helperText?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  helperText,
  error,
  leftIcon,
  rightIcon,
  fullWidth = false,
  className = '',
  disabled,
  id,
  ...props
}, ref) => {
  // Generate a unique ID if not provided
  const inputId = id || `input-${Math.random().toString(36).substring(2, 9)}`;

  // Base classes using theme utilities
  const baseClasses = cn(
    'rounded-md border',
    themeClasses.input,
    themeClasses.text,
    themeClasses.focus
  );

  // Error classes
  const errorClasses = error ? 'border-error-500 focus:ring-error-500 focus:border-error-500 dark:border-error-500' : '';

  // Disabled classes
  const disabledClasses = disabled ? 'bg-neutral-100 dark:bg-neutral-800/50 cursor-not-allowed' : '';

  // Icon padding classes
  const leftIconPadding = leftIcon ? 'pl-10' : '';
  const rightIconPadding = rightIcon ? 'pr-10' : '';

  // Full width class
  const fullWidthClass = fullWidth ? 'w-full' : '';

  return (
    <div className={fullWidth ? 'w-full' : ''}>
      {label && (
        <label 
          htmlFor={inputId} 
          className={cn(
            'block text-sm font-medium mb-1',
            themeClasses.text
          )}
        >
          {label}
        </label>
      )}

      <div className="relative">
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-neutral-500 dark:text-neutral-400">
            {leftIcon}
          </div>
        )}

        <input
          ref={ref}
          id={inputId}
          className={cn(
            baseClasses,
            errorClasses,
            disabledClasses,
            leftIconPadding,
            rightIconPadding,
            fullWidthClass,
            className
          )}
          disabled={disabled}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={error ? `${inputId}-error` : helperText ? `${inputId}-helper` : undefined}
          {...props}
        />

        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none text-neutral-500 dark:text-neutral-400">
            {rightIcon}
          </div>
        )}
      </div>

      {error && (
        <p id={`${inputId}-error`} className="mt-1 text-sm text-error-600 dark:text-error-400">
          {error}
        </p>
      )}

      {!error && helperText && (
        <p id={`${inputId}-helper`} className={cn('mt-1 text-sm', themeClasses.textMuted)}>
          {helperText}
        </p>
      )}
    </div>
  );
});
