import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '../../utils/cn';
import { MapPin, Calendar } from 'lucide-react';
import { ReputationBadge } from './ReputationBadge';
import { SkillBadge } from './SkillBadge';
import { Skill } from '../../types/collaboration';
import ProfileImage from './ProfileImage';
import { themeClasses } from '../../utils/themeUtils';
import { motion } from 'framer-motion';
import Card3D from './Card3D';
import AnimatedList from './AnimatedList';

export interface ProfileCardProps {
  userId: string;
  displayName: string;
  photoURL?: string;
  profilePicture?: string;
  location?: string;
  joinDate: Date;
  bio?: string;
  skills?: Skill[];
  reputationScore?: number;
  className?: string;
  compact?: boolean;
}

export const ProfileCard: React.FC<ProfileCardProps> = ({
  userId,
  displayName,
  photoURL,
  profilePicture,
  location,
  joinDate,
  bio,
  skills = [],
  reputationScore = 0,
  className,
  compact = false
}) => {
  // Format join date
  const formattedJoinDate = new Intl.DateTimeFormat('en-US', {
    month: 'long',
    year: 'numeric'
  }).format(joinDate);

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <Card3D
      className={cn(
        "backdrop-blur-sm bg-white/70 dark:bg-neutral-800/60 border border-white/20 dark:border-neutral-700/30 rounded-lg overflow-hidden",
        className
      )}
      intensity={5}
      glare={true}
      shadow={true}
      border={false}
    >
      <div className="p-5">
        <motion.div
          initial="hidden"
          animate="visible"
          variants={containerVariants}
          className="space-y-4"
        >
          <motion.div variants={itemVariants} className="flex items-center">
            <div className="flex-shrink-0">
              <ProfileImage
                photoURL={photoURL}
                profilePicture={profilePicture}
                displayName={displayName}
                size="lg"
              />
            </div>
            <div className="ml-4 flex-1 min-w-0">
              <motion.h3
                className={`text-lg font-semibold ${themeClasses.text} truncate`}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.1 }}
              >
                {displayName}
              </motion.h3>

              {reputationScore > 0 && (
                <motion.div
                  className="mt-1"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                >
                  <ReputationBadge score={reputationScore} size="sm" />
                </motion.div>
              )}

              <motion.div
                variants={itemVariants}
                className={`mt-1 flex flex-wrap items-center text-sm ${themeClasses.textMuted}`}
              >
                {location && (
                  <div className="flex items-center mr-4">
                    <MapPin className="mr-1 h-3.5 w-3.5" />
                    <span>{location}</span>
                  </div>
                )}

                <div className="flex items-center">
                  <Calendar className="mr-1 h-3.5 w-3.5" />
                  <span>Joined {formattedJoinDate}</span>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {!compact && bio && (
            <motion.div variants={itemVariants} className="mt-4">
              <p className={`text-sm ${themeClasses.textMuted} line-clamp-3`}>{bio}</p>
            </motion.div>
          )}

          {skills.length > 0 && (
            <motion.div variants={itemVariants} className="mt-4">
              <AnimatedList
                className="flex flex-wrap gap-2"
                animation="slideUp"
                staggerDelay={0.03}
                duration={0.3}
              >
                {skills.slice(0, compact ? 3 : 6).map((skill, index) => (
                  <SkillBadge
                    key={index}
                    skill={skill.name}
                    level={skill.level}
                    size="sm"
                  />
                ))}

                {skills.length > (compact ? 3 : 6) && (
                  <span className="inline-flex items-center rounded-full bg-gray-100 dark:bg-gray-800 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:text-gray-300">
                    +{skills.length - (compact ? 3 : 6)} more
                  </span>
                )}
              </AnimatedList>
            </motion.div>
          )}

          <motion.div
            variants={itemVariants}
            whileHover={{ scale: 1.05 }}
            className="mt-5"
          >
            <Link
              to={`/profile/${userId}`}
              className={`inline-flex items-center rounded-md ${themeClasses.primaryButton} px-4 py-2 text-sm font-medium shadow-sm ${themeClasses.focus} ${themeClasses.transition}`}
            >
              View Profile
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </Card3D>
  );
};

export default ProfileCard;
