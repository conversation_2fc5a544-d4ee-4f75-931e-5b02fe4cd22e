import React, { useMemo } from 'react';
import { cn } from '../../utils/cn';
import { useTheme } from '../../contexts/ThemeContext';

// Banner design variants
export type BannerDesign =
  // Classic gradients
  | 'gradient1'      // Orange to blue (complementary)
  | 'gradient2'      // Orange to purple (split-complementary)
  | 'gradient3'      // Teal to orange (triadic)

  // Geometric patterns
  | 'geometric1'     // Geometric pattern with orange
  | 'geometric2'     // Geometric pattern with blue
  | 'waves'          // Wave pattern
  | 'dots'           // Dot pattern

  // Modern trends 2024
  | 'glassmorphism1' // Glassmorphism with blur and transparency
  | 'glassmorphism2' // Glassmorphism with light colors
  | 'neobrutalism1'  // Neobrutalism with bold colors and shadows
  | 'neobrutalism2'  // Neobrutalism with patterns
  | 'abstract3d'     // Abstract 3D shapes
  | 'liquid'         // Liquid/fluid shapes
  | 'memphis'        // Memphis style with geometric shapes
  | 'gradient3d'     // 3D gradient effect
  | 'cyberpunk'      // Cyberpunk-inspired neon
  | 'minimal'        // Minimalist design

  | 'random';        // Random selection from above

interface DefaultBannerProps {
  className?: string;
  height?: 'sm' | 'md' | 'lg';
  design?: BannerDesign;
  children?: React.ReactNode;
}

/**
 * DefaultBanner Component
 *
 * A modern, CSS-based banner component that generates beautiful gradients and patterns
 * without relying on external image services. Designed to be visually appealing and
 * compatible with future mobile app conversion.
 */
export const DefaultBanner: React.FC<DefaultBannerProps> = ({
  className,
  height = 'md',
  design = 'random',
  children
}) => {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // Height classes based on the height prop
  const heightClasses = {
    sm: 'h-32',
    md: 'h-48',
    lg: 'h-64'
  };

  // Randomly select a design if 'random' is specified
  const actualDesign = useMemo(() => {
    if (design === 'random') {
      const designs: BannerDesign[] = [
        // Classic designs
        'gradient1', 'gradient2', 'gradient3',
        'geometric1', 'geometric2', 'waves', 'dots',

        // Modern trends 2024
        'glassmorphism1', 'glassmorphism2',
        'neobrutalism1', 'neobrutalism2',
        'abstract3d', 'liquid', 'memphis',
        'gradient3d', 'cyberpunk', 'minimal'
      ];
      return designs[Math.floor(Math.random() * designs.length)];
    }
    return design;
  }, [design]);

  // Generate the appropriate banner based on the design
  const renderBanner = () => {
    switch (actualDesign) {
      case 'gradient1':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-gradient-to-br from-primary-700 via-primary-600 to-secondary-700"
              : "bg-gradient-to-br from-primary-400 via-primary-500 to-secondary-500",
            className
          )}>
            {/* Subtle overlay patterns */}
            <div className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_20%_30%,_white_0%,_transparent_60%)]"></div>
            <div className="absolute inset-0 opacity-10 bg-[radial-gradient(circle_at_80%_70%,_white_0%,_transparent_60%)]"></div>
            {children}
          </div>
        );

      case 'gradient2':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-gradient-to-r from-primary-700 via-primary-600 to-accent-700"
              : "bg-gradient-to-r from-primary-400 via-primary-500 to-accent-500",
            className
          )}>
            {/* Subtle overlay patterns */}
            <div className="absolute inset-0 opacity-20 bg-[linear-gradient(135deg,_transparent_0%,_white_25%,_transparent_50%)]"></div>
            <div className="absolute inset-0 opacity-10 bg-[radial-gradient(circle_at_80%_20%,_white_0%,_transparent_60%)]"></div>
            {children}
          </div>
        );

      case 'gradient3':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-gradient-to-br from-cyan-700 via-cyan-600 to-primary-700"
              : "bg-gradient-to-br from-cyan-400 via-cyan-500 to-primary-500",
            className
          )}>
            {/* Subtle overlay patterns */}
            <div className="absolute inset-0 opacity-20 bg-[linear-gradient(45deg,_transparent_0%,_white_25%,_transparent_50%)]"></div>
            <div className="absolute inset-0 opacity-10 bg-[radial-gradient(circle_at_30%_70%,_white_0%,_transparent_60%)]"></div>
            {children}
          </div>
        );

      case 'geometric1':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-gradient-to-r from-primary-800 to-primary-600"
              : "bg-gradient-to-r from-primary-600 to-primary-400",
            className
          )}>
            {/* Geometric pattern overlay */}
            <div className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                backgroundSize: '60px 60px'
              }}
            ></div>
            <div className="absolute inset-0 opacity-30 bg-[radial-gradient(circle_at_center,_transparent_30%,_rgba(0,0,0,0.3)_100%)]"></div>
            {children}
          </div>
        );

      case 'geometric2':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-gradient-to-r from-secondary-800 to-secondary-600"
              : "bg-gradient-to-r from-secondary-600 to-secondary-400",
            className
          )}>
            {/* Geometric pattern overlay */}
            <div className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Cpolygon points='0,0 20,0 10,10'/%3E%3Cpolygon points='0,20 10,10 20,20'/%3E%3C/g%3E%3C/svg%3E")`,
                backgroundSize: '20px 20px'
              }}
            ></div>
            <div className="absolute inset-0 opacity-30 bg-[radial-gradient(circle_at_center,_transparent_30%,_rgba(0,0,0,0.3)_100%)]"></div>
            {children}
          </div>
        );

      case 'waves':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-gradient-to-r from-accent-800 via-accent-700 to-primary-800"
              : "bg-gradient-to-r from-accent-500 via-accent-400 to-primary-500",
            className
          )}>
            {/* Wave pattern overlay */}
            <div className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='20' viewBox='0 0 100 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21.184 20c.357-.13.72-.264 1.088-.402l1.768-.661C33.64 15.347 39.647 14 50 14c10.271 0 15.362 1.222 24.629 4.928.955.383 1.869.74 2.75 1.072h6.225c-2.51-.73-5.139-1.691-8.233-2.928C65.888 13.278 60.562 12 50 12c-10.626 0-16.855 1.397-26.66 5.063l-1.767.662c-2.475.923-4.66 1.674-6.724 2.275h6.335zm0-20C13.258 2.892 8.077 4 0 4V2c5.744 0 9.951-.574 14.85-2h6.334zM77.38 0C85.239 2.966 90.502 4 100 4V2c-6.842 0-11.386-.542-16.396-2h-6.225zM0 14c8.44 0 13.718-1.21 22.272-4.402l1.768-.661C33.64 5.347 39.647 4 50 4c10.271 0 15.362 1.222 24.629 4.928C84.112 12.722 89.438 14 100 14v-2c-10.271 0-15.362-1.222-24.629-4.928C65.888 3.278 60.562 2 50 2 39.374 2 33.145 3.397 23.34 7.063l-1.767.662C13.223 10.84 8.163 12 0 12v2z' fill='%23ffffff' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E")`,
                backgroundSize: '100px 20px'
              }}
            ></div>
            <div className="absolute inset-0 opacity-30 bg-[radial-gradient(circle_at_center,_transparent_30%,_rgba(0,0,0,0.3)_100%)]"></div>
            {children}
          </div>
        );

      case 'dots':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-gradient-to-br from-primary-800 via-primary-700 to-secondary-800"
              : "bg-gradient-to-br from-primary-500 via-primary-400 to-secondary-500",
            className
          )}>
            {/* Dots pattern overlay */}
            <div className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E")`,
                backgroundSize: '20px 20px'
              }}
            ></div>
            <div className="absolute inset-0 opacity-30 bg-[radial-gradient(circle_at_center,_transparent_30%,_rgba(0,0,0,0.3)_100%)]"></div>
            {children}
          </div>
        );

      // Glassmorphism designs
      case 'glassmorphism1':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg backdrop-blur-md",
            heightClasses[height],
            isDark
              ? "bg-primary-800/30 border border-white/10"
              : "bg-primary-400/20 border border-white/30",
            className
          )}>
            {/* Glass effect elements */}
            <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
            <div className="absolute -inset-1/2 bg-[radial-gradient(circle_at_70%_60%,_rgba(255,255,255,0.2)_0%,_transparent_50%)]"></div>
            <div className="absolute top-0 left-0 w-full h-full bg-[linear-gradient(120deg,rgba(255,255,255,0.3)_0%,transparent_40%)]"></div>
            {children}
          </div>
        );

      case 'glassmorphism2':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg backdrop-blur-lg",
            heightClasses[height],
            isDark
              ? "bg-secondary-600/20 border border-secondary-400/20"
              : "bg-secondary-300/30 border border-secondary-200/40",
            className
          )}>
            {/* Glass effect elements */}
            <div className="absolute inset-0 bg-[linear-gradient(135deg,rgba(255,255,255,0.2)_0%,transparent_100%)]"></div>
            <div className="absolute -inset-1/2 bg-[radial-gradient(circle_at_30%_40%,_rgba(255,255,255,0.3)_0%,_transparent_60%)]"></div>
            <div className="absolute bottom-0 right-0 w-2/3 h-2/3 bg-[radial-gradient(circle_at_bottom_right,rgba(255,255,255,0.2)_0%,transparent_70%)]"></div>
            {children}
          </div>
        );

      // Neobrutalism designs
      case 'neobrutalism1':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg border-4 border-black translate-y-1 translate-x-1",
            heightClasses[height],
            isDark
              ? "bg-primary-500 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]"
              : "bg-primary-400 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]",
            className
          )}>
            {/* Neobrutalism elements */}
            <div className="absolute top-4 left-4 w-20 h-20 bg-yellow-300 rounded-full border-4 border-black"></div>
            <div className="absolute bottom-8 right-8 w-16 h-16 bg-blue-400 rounded-lg border-4 border-black"></div>
            <div className="absolute top-1/2 left-1/3 w-12 h-12 bg-pink-400 rotate-45 border-4 border-black"></div>
            {children}
          </div>
        );

      case 'neobrutalism2':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg border-4 border-black translate-y-1 translate-x-1",
            heightClasses[height],
            isDark
              ? "bg-accent-500 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]"
              : "bg-accent-400 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]",
            className
          )}>
            {/* Neobrutalism pattern */}
            <div className="absolute inset-0 opacity-30"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/svg%3E")`,
                backgroundSize: '60px 60px'
              }}
            ></div>
            <div className="absolute top-6 right-6 w-24 h-8 bg-yellow-300 border-4 border-black"></div>
            {children}
          </div>
        );

      // 3D and modern designs
      case 'abstract3d':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"
              : "bg-gradient-to-br from-gray-100 via-white to-gray-100",
            className
          )}>
            {/* 3D abstract shapes */}
            <div className="absolute w-40 h-40 top-1/2 left-1/4 -translate-x-1/2 -translate-y-1/2 bg-primary-500 rounded-full blur-xl opacity-60 mix-blend-multiply"></div>
            <div className="absolute w-40 h-40 top-1/2 left-2/4 -translate-x-1/2 -translate-y-1/2 bg-secondary-500 rounded-full blur-xl opacity-60 mix-blend-multiply"></div>
            <div className="absolute w-40 h-40 top-1/2 left-3/4 -translate-x-1/2 -translate-y-1/2 bg-accent-500 rounded-full blur-xl opacity-60 mix-blend-multiply"></div>
            <div className="absolute inset-0 bg-[linear-gradient(0deg,rgba(255,255,255,0.2)_0%,transparent_100%)]"></div>
            {children}
          </div>
        );

      case 'liquid':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-gradient-to-r from-primary-900 to-primary-800"
              : "bg-gradient-to-r from-primary-100 to-primary-50",
            className
          )}>
            {/* Liquid shapes */}
            <div className="absolute top-0 left-0 right-0 bottom-0 opacity-70">
              <div className="absolute w-1/2 h-1/2 top-0 left-0 bg-primary-400 dark:bg-primary-600 rounded-[100%] blur-2xl transform -translate-x-1/4 -translate-y-1/4"></div>
              <div className="absolute w-1/2 h-1/2 bottom-0 right-0 bg-secondary-400 dark:bg-secondary-600 rounded-[100%] blur-2xl transform translate-x-1/4 translate-y-1/4"></div>
              <div className="absolute w-1/3 h-1/3 top-1/2 left-1/2 bg-accent-400 dark:bg-accent-600 rounded-[100%] blur-2xl transform -translate-x-1/2 -translate-y-1/2"></div>
            </div>
            <div className="absolute inset-0 bg-white/5 backdrop-blur-[2px]"></div>
            {children}
          </div>
        );

      case 'memphis':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-neutral-800"
              : "bg-neutral-100",
            className
          )}>
            {/* Memphis style patterns */}
            <div className="absolute top-4 left-4 w-16 h-16 bg-primary-400 rounded-full"></div>
            <div className="absolute top-8 right-8 w-20 h-20 border-4 border-secondary-400 rounded-none"></div>
            <div className="absolute bottom-6 left-1/4 w-12 h-12 bg-accent-400 rotate-45"></div>
            <div className="absolute top-1/3 right-1/3 w-8 h-24 bg-yellow-400 rounded-full"></div>
            <div className="absolute bottom-8 right-1/4 w-20 h-8 bg-blue-400 rounded-none"></div>
            <div className="absolute inset-0"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23${isDark ? 'ffffff' : '000000'}' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E")`,
                backgroundSize: '100px 100px'
              }}
            ></div>
            {children}
          </div>
        );

      case 'gradient3d':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-gradient-to-br from-primary-800 via-primary-700 to-primary-900"
              : "bg-gradient-to-br from-primary-400 via-primary-300 to-primary-500",
            className
          )}>
            {/* 3D gradient effect */}
            <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_rgba(255,255,255,0.2)_0%,_transparent_70%)]"></div>
            <div className="absolute inset-0 bg-[linear-gradient(40deg,transparent_0%,rgba(255,255,255,0.3)_45%,rgba(255,255,255,0.3)_55%,transparent_100%)]"></div>
            <div className="absolute inset-0 bg-[linear-gradient(130deg,transparent_0%,rgba(255,255,255,0.1)_45%,rgba(255,255,255,0.1)_55%,transparent_100%)]"></div>
            <div className="absolute inset-0 bg-[linear-gradient(to_right,transparent_0%,rgba(0,0,0,0.1)_45%,rgba(0,0,0,0.1)_55%,transparent_100%)]"></div>
            {children}
          </div>
        );

      case 'cyberpunk':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-black"
              : "bg-gray-900",
            className
          )}>
            {/* Cyberpunk elements */}
            <div className="absolute inset-0 bg-[linear-gradient(90deg,rgba(255,0,255,0.1)_0%,rgba(0,255,255,0.1)_100%)]"></div>
            <div className="absolute h-px w-full top-1/3 left-0 bg-[linear-gradient(90deg,transparent_0%,#ff00ff_50%,transparent_100%)] animate-pulse"></div>
            <div className="absolute h-px w-full top-2/3 left-0 bg-[linear-gradient(90deg,transparent_0%,#00ffff_50%,transparent_100%)] animate-pulse" style={{ animationDelay: '0.5s' }}></div>
            <div className="absolute h-full w-px left-1/3 top-0 bg-[linear-gradient(0deg,transparent_0%,#ff00ff_50%,transparent_100%)] animate-pulse" style={{ animationDelay: '0.25s' }}></div>
            <div className="absolute h-full w-px left-2/3 top-0 bg-[linear-gradient(0deg,transparent_0%,#00ffff_50%,transparent_100%)] animate-pulse" style={{ animationDelay: '0.75s' }}></div>
            <div className="absolute top-4 left-4 text-[#ff00ff] text-xs font-mono opacity-70">SYSTEM.INIT</div>
            <div className="absolute bottom-4 right-4 text-[#00ffff] text-xs font-mono opacity-70">CONNECT.STATUS</div>
            {children}
          </div>
        );

      case 'minimal':
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-neutral-900"
              : "bg-white",
            className
          )}>
            {/* Minimal elements */}
            <div className="absolute left-8 top-1/2 -translate-y-1/2 w-1 h-1/2 bg-primary-500"></div>
            <div className="absolute right-8 top-1/2 -translate-y-1/2 w-px h-1/3 bg-primary-500 opacity-50"></div>
            <div className="absolute left-1/4 bottom-8 w-1/2 h-px bg-primary-500 opacity-30"></div>
            {children}
          </div>
        );

      default:
        return (
          <div className={cn(
            "relative w-full overflow-hidden rounded-lg",
            heightClasses[height],
            isDark
              ? "bg-gradient-to-br from-primary-700 to-primary-900"
              : "bg-gradient-to-br from-primary-400 to-primary-600",
            className
          )}>
            {/* Fallback pattern */}
            <div className="absolute inset-0 opacity-20 bg-[radial-gradient(circle_at_20%_30%,_white_0%,_transparent_60%)]"></div>
            <div className="absolute inset-0 opacity-10 bg-[radial-gradient(circle_at_80%_70%,_white_0%,_transparent_60%)]"></div>
            {children}
          </div>
        );
    }
  };

  return renderBanner();
};

export default DefaultBanner;
