import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '../../utils/cn';

interface NavItemProps {
  to: string;
  label: string;
  icon?: React.ReactNode;
  isActive?: boolean;
  onClick?: () => void;
  className?: string;
  variant?: 'desktop' | 'mobile';
}

export const NavItem: React.FC<NavItemProps> = ({
  to,
  label,
  icon,
  isActive = false,
  onClick,
  className,
  variant = 'desktop'
}) => {
  const baseClasses = {
    desktop: cn(
      'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium transition-colors duration-200',
      isActive
        ? 'border-orange-500 text-gray-900 dark:text-gray-100'
        : 'border-transparent text-gray-500 dark:text-gray-300 hover:border-orange-300 hover:text-gray-700 dark:hover:text-gray-200',
      className
    ),
    mobile: cn(
      'flex items-center w-full px-3 py-2 text-base font-medium border-l-4 transition-colors duration-200',
      isActive
        ? 'bg-orange-50 dark:bg-orange-900/20 border-orange-500 text-orange-700 dark:text-orange-300'
        : 'border-transparent text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-300 dark:hover:border-gray-600 hover:text-gray-700 dark:hover:text-gray-200',
      className
    )
  };

  return (
    <Link to={to} className={baseClasses[variant]} onClick={onClick}>
      {icon && <span className={`${variant === 'desktop' ? 'mr-1' : 'mr-2'} h-5 w-5`}>{icon}</span>}
      <span>{label}</span>
    </Link>
  );
};

export default NavItem;
