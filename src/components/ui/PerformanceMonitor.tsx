import { useEffect, useState } from 'react';
import {
  getBasicPerformanceMetrics,
  observeLCP,
  observeCLS,
  observeFID,
  PerformanceMetrics,
  logPerformanceMetrics
} from '../../utils/performanceMetrics';

interface PerformanceMonitorProps {
  pageName: string;
  enabled?: boolean;
  onMetricsCollected?: (metrics: Partial<PerformanceMetrics>) => void;
}

// Access the global metrics collection function if available
const globalMetricsCollector = (window as any).__COLLECT_METRICS__;

/**
 * PerformanceMonitor component
 *
 * This component collects performance metrics for the page it's included on.
 * It doesn't render anything visible but collects metrics in the background.
 *
 * @param pageName Name of the page being monitored
 * @param enabled Whether monitoring is enabled (default: true)
 * @param onMetricsCollected Callback function called when metrics are collected
 */
const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  pageName,
  enabled = true,
  onMetricsCollected
}) => {
  const [metrics, setMetrics] = useState<Partial<PerformanceMetrics>>({});

  useEffect(() => {
    if (!enabled) return;

    // Collect basic metrics after the page has loaded
    const handleLoad = () => {
      // Wait a bit to ensure all metrics are available
      setTimeout(() => {
        const basicMetrics = getBasicPerformanceMetrics();

        setMetrics(prev => ({
          ...prev,
          ...basicMetrics
        }));

        // Log metrics to console
        logPerformanceMetrics(basicMetrics, pageName);

        // Call the callback if provided
        if (onMetricsCollected) {
          onMetricsCollected(basicMetrics);
        }

        // Call the global metrics collector if available
        if (globalMetricsCollector) {
          globalMetricsCollector(pageName, basicMetrics);
        }
      }, 1000);
    };

    // Observe Largest Contentful Paint
    observeLCP((lcp) => {
      setMetrics(prev => ({
        ...prev,
        largestContentfulPaint: lcp
      }));

      // Update logs and callback
      if (onMetricsCollected) {
        onMetricsCollected({
          ...metrics,
          largestContentfulPaint: lcp
        });
      }

      // Call the global metrics collector if available
      if (globalMetricsCollector) {
        globalMetricsCollector(pageName, {
          ...metrics,
          largestContentfulPaint: lcp
        });
      }
    });

    // Observe Cumulative Layout Shift
    observeCLS((cls) => {
      setMetrics(prev => ({
        ...prev,
        cumulativeLayoutShift: cls
      }));

      // Update logs and callback
      if (onMetricsCollected) {
        onMetricsCollected({
          ...metrics,
          cumulativeLayoutShift: cls
        });
      }

      // Call the global metrics collector if available
      if (globalMetricsCollector) {
        globalMetricsCollector(pageName, {
          ...metrics,
          cumulativeLayoutShift: cls
        });
      }
    });

    // Observe First Input Delay
    observeFID((fid) => {
      setMetrics(prev => ({
        ...prev,
        firstInputDelay: fid
      }));

      // Update logs and callback
      if (onMetricsCollected) {
        onMetricsCollected({
          ...metrics,
          firstInputDelay: fid
        });
      }

      // Call the global metrics collector if available
      if (globalMetricsCollector) {
        globalMetricsCollector(pageName, {
          ...metrics,
          firstInputDelay: fid
        });
      }
    });

    // Add load event listener
    window.addEventListener('load', handleLoad);

    // Clean up
    return () => {
      window.removeEventListener('load', handleLoad);
    };
  }, [enabled, pageName, onMetricsCollected, metrics]);

  // This component doesn't render anything visible
  return null;
};

export default PerformanceMonitor;
