import React from 'react';
import { cn } from '../../../utils/cn';
import { themeClasses } from '../../../utils/themeUtils';
import { Skeleton, SkeletonText, SkeletonCircle } from './Skeleton';

interface CardSkeletonProps {
  hasImage?: boolean;
  hasFooter?: boolean;
  className?: string;
}

export const CardSkeleton: React.FC<CardSkeletonProps> = ({
  hasImage = false,
  hasFooter = false,
  className,
}) => {
  return (
    <div className={cn(
      `overflow-hidden rounded-xl ${themeClasses.card} ${themeClasses.shadowMd}`,
      className
    )}>
      {hasImage && (
        <div className="w-full h-48">
          <Skeleton className="w-full h-full rounded-none" />
        </div>
      )}
      
      <div className="p-6 space-y-4">
        <div className="flex items-center space-x-4">
          <SkeletonCircle />
          <div className="space-y-2 flex-1">
            <SkeletonText className="h-5 w-3/4" />
            <SkeletonText className="h-4 w-1/2" />
          </div>
        </div>
        
        <div className="space-y-2">
          <SkeletonText />
          <SkeletonText />
          <SkeletonText className="w-3/4" />
        </div>
        
        <div className="flex flex-wrap gap-2">
          <Skeleton className="h-6 w-16 rounded-full" />
          <Skeleton className="h-6 w-20 rounded-full" />
          <Skeleton className="h-6 w-14 rounded-full" />
        </div>
      </div>
      
      {hasFooter && (
        <div className={`px-6 py-4 border-t ${themeClasses.border} bg-neutral-50 dark:bg-neutral-800/30`}>
          <div className="flex justify-between items-center">
            <SkeletonText className="w-1/3" />
            <Skeleton className="h-9 w-24 rounded-md" />
          </div>
        </div>
      )}
    </div>
  );
};
