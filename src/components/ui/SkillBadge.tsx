import React from 'react';
import { cn } from '../../utils/cn';
import { themeClasses } from '../../utils/themeUtils';

export type SkillLevel = 'beginner' | 'intermediate' | 'expert';

export interface SkillBadgeProps {
  skill: string;
  level?: SkillLevel;
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  className?: string;
  removable?: boolean;
  onRemove?: () => void;
}

const levelColors = {
  beginner: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800',
  intermediate: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800',
  advanced: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800',
  expert: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-800'
};

const sizeClasses = {
  sm: 'text-xs px-2 py-0.5',
  md: 'text-sm px-2.5 py-0.5',
  lg: 'text-base px-3 py-1'
};

export const SkillBadge: React.FC<SkillBadgeProps> = ({
  skill,
  level = 'intermediate',
  size = 'md',
  onClick,
  className,
  removable = false,
  onRemove
}) => {
  const handleRemoveClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onRemove?.();
  };

  return (
    <span
      className={cn(
        'inline-flex items-center rounded-full border font-medium transition-colors',
        themeClasses.transition,
        levelColors[level],
        sizeClasses[size],
        onClick ? 'cursor-pointer hover:opacity-80' : '',
        className
      )}
      onClick={onClick}
    >
      {skill}
      {removable && onRemove && (
        <button
          type="button"
          onClick={handleRemoveClick}
          className="ml-1.5 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 hover:text-gray-500 dark:hover:text-gray-400 focus:outline-none transition-colors duration-200"
        >
          <span className="sr-only">Remove</span>
          <svg
            className="h-3 w-3"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      )}
    </span>
  );
};

export default SkillBadge;
