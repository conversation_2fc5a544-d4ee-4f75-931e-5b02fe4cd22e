import React, { useCallback, memo } from 'react';
import { createPortal } from 'react-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../contexts/ThemeContext';
import { MOTION_VARIANTS } from '../../utils/animations';

interface SimpleModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const SimpleModalComponent: React.FC<SimpleModalProps> = ({ isOpen, onClose, title, children }) => {
  // Get the current theme using the useTheme hook
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // Memoize event handlers
  const handleBackdropClick = useCallback(() => {
    onClose();
  }, [onClose]);

  const handleModalClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  const handleCloseClick = useCallback(() => {
    onClose();
  }, [onClose]);

  // Define styles based on the current theme
  const modalStyles = {
    backgroundColor: isDarkMode ? '#1f2937' : 'white',
    color: isDarkMode ? '#f3f4f6' : '#1f2937',
    padding: '20px',
    borderRadius: '8px',
    maxWidth: '500px',
    width: '100%',
    boxShadow: isDarkMode
      ? '0 4px 6px rgba(0, 0, 0, 0.3)'
      : '0 4px 6px rgba(0, 0, 0, 0.1)',
  };

  const titleStyles = {
    margin: 0,
    color: isDarkMode ? '#f3f4f6' : '#1f2937',
    fontWeight: 600,
  };

  const closeButtonStyles = {
    background: 'none',
    border: 'none',
    fontSize: '20px',
    cursor: 'pointer',
    color: isDarkMode ? '#9ca3af' : '#6b7280',
  };

  const backdropStyles = {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 9999,
    backdropFilter: 'blur(2px)',
  };

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <motion.div
          style={backdropStyles}
          onClick={handleBackdropClick}
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={MOTION_VARIANTS.backdrop}
        >
          <motion.div
            style={modalStyles}
            onClick={handleModalClick}
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={MOTION_VARIANTS.modal}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
              <motion.h2
                style={titleStyles}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2, duration: 0.3 }}
              >
                {title}
              </motion.h2>
              <motion.button
                onClick={handleCloseClick}
                style={closeButtonStyles}
                whileHover={{ scale: 1.2, rotate: 90 }}
                whileTap={{ scale: 0.9 }}
                transition={{ duration: 0.2 }}
              >
                ×
              </motion.button>
            </div>
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.3 }}
            >
              {children}
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>,
    document.body
  );
};

// Memoize the SimpleModal component to prevent unnecessary re-renders
export const SimpleModal = memo(SimpleModalComponent);
