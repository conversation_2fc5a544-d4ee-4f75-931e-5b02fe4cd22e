import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react()
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    // Security headers for development server
    headers: {
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Content-Security-Policy': `
        default-src 'self';
        script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://*.firebaseio.com https://*.googleapis.com;
        style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
        img-src 'self' data: blob: https: https://res.cloudinary.com https://*.googleusercontent.com https://ui-avatars.com;
        font-src 'self' data: https://fonts.gstatic.com;
        connect-src 'self' wss://*.firebaseio.com https://*.googleapis.com https://firestore.googleapis.com https://*.cloudfunctions.net https://api.cloudinary.com https://res.cloudinary.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com;
        frame-src 'self' https://*.firebaseapp.com https://*.google.com;
      `.replace(/\s+/g, ' ').trim(),
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    },
    // CORS configuration
    cors: {
      origin: process.env['VITE_ALLOWED_ORIGINS']?.split(',') || ['http://localhost:3000'],
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      credentials: true,
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    },
    // Rate limiting in development
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        configure: (proxy) => {
          proxy.on('error', (err) => {
            console.error('Proxy error:', err);
          });
        }
      }
    }
  },
  build: {
    target: 'es2015',
    outDir: 'dist',
    sourcemap: process.env['NODE_ENV'] !== 'production',
    // Security-related build options
    rollupOptions: {
      output: {
        // Add security headers to generated assets
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.');
          const ext = info?.[info.length - 1];
          if (ext === 'css') {
            return `assets/styles/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
      }
    },
    // Minification options
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: process.env['NODE_ENV'] === 'production',
        drop_debugger: process.env['NODE_ENV'] === 'production'
      }
    }
  },
  // Test configuration
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./jest.setup.ts'],
    coverage: {
      reporter: ['text', 'lcov', 'html'],
      exclude: [
        'node_modules/',
        'src/utils/types/',
        'src/assets/',
        'src/**/*.d.ts'
      ]
    },
    include: ['src/**/*.{test,spec}.{js,jsx,ts,tsx}']
  },
  define: {
    // Global constants
    'process.env.NODE_ENV': JSON.stringify(process.env['NODE_ENV']),
    'process.env.VITE_API_KEY': JSON.stringify(process.env['VITE_API_KEY']),
    'process.env.VITE_AUTH_DOMAIN': JSON.stringify(process.env['VITE_AUTH_DOMAIN']),
    'process.env.VITE_PROJECT_ID': JSON.stringify(process.env['VITE_PROJECT_ID']),
    'process.env.VITE_STORAGE_BUCKET': JSON.stringify(process.env['VITE_STORAGE_BUCKET']),
    'process.env.VITE_MESSAGING_SENDER_ID': JSON.stringify(process.env['VITE_MESSAGING_SENDER_ID']),
    'process.env.VITE_APP_ID': JSON.stringify(process.env['VITE_APP_ID']),
    'process.env.VITE_MEASUREMENT_ID': JSON.stringify(process.env['VITE_MEASUREMENT_ID'])
  },
  optimizeDeps: {
    include: ['firebase/app', 'firebase/auth']
  }
});
