{"rules": {"maxRuleLength": 500, "minRequiredChecks": 3, "requiredPatterns": ["request.auth != null", "resource.data", "request.resource.data"], "forbiddenPatterns": ["allow .* if true", "allow .* if request.auth != null"]}, "validation": {"coverage": {"statements": 90, "branches": 85, "functions": 90, "lines": 90}, "complexity": {"maxNestedRules": 3, "maxFunctionLength": 15, "maxConditions": 5}}, "testing": {"requiredTestTypes": ["authentication", "authorization", "validation", "edge-cases", "negative-cases"], "minTestsPerRule": 2, "timeout": 10000}, "security": {"maxDocumentSize": 1048576, "maxBatchSize": 500, "rateLimits": {"reads": 1000, "writes": 500, "deletes": 100}, "authentication": {"requireEmailVerification": true, "minPasswordLength": 12, "requireMFA": false}}, "storage": {"maxFileSize": {"profile": 5242880, "evidence": 20971520, "general": 10485760}, "allowedContentTypes": {"images": ["image/jpeg", "image/png", "image/gif", "image/webp", "image/svg+xml"], "documents": ["application/pdf", "text/plain", "application/json"]}}, "monitoring": {"alerts": {"ruleChanges": true, "permissionDenials": true, "highLatencyRules": true, "suspiciousActivity": true}, "thresholds": {"permissionDenials": 100, "ruleLatency": 500, "batchOperations": 50}, "reporting": {"enabled": true, "frequency": "daily", "retentionDays": 30}}, "deployment": {"requireApproval": true, "requireTests": true, "stagingRequired": true, "backupBeforeUpdate": true, "rollbackEnabled": true, "environments": {"development": {"emulatorPorts": {"firestore": 8080, "storage": 9199}}, "staging": {"projectId": "staging-project-id", "region": "us-central1"}, "production": {"projectId": "prod-project-id", "region": "us-central1", "requireMFA": true}}}, "ci": {"requireCodeReview": true, "requiredApprovals": 2, "securityScanners": ["gitleaks", "sonarqube", "firebase-rules-analyzer"], "notifications": {"slack": true, "email": true, "pullRequests": true}}, "documentation": {"required": true, "templates": {"ruleChanges": true, "securityImpact": true, "testCoverage": true, "rollback": true}, "requiredSections": ["security-impact", "testing-evidence", "rollback-plan", "monitoring-plan"]}, "maintenance": {"schedule": {"ruleReview": "monthly", "securityAudit": "quarterly", "penetrationTesting": "annually"}, "automation": {"backups": true, "monitoring": true, "reporting": true, "cleanup": true}}}