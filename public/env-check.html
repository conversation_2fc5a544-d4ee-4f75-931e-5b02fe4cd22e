<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Environment Check</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .debug-info {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .success {
      color: green;
    }
    .error {
      color: red;
    }
    pre {
      background-color: #f0f0f0;
      padding: 10px;
      border-radius: 5px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>TradeYa Environment Check</h1>
  
  <div class="debug-info">
    <h2>Build Mode</h2>
    <div id="build-mode"></div>
  </div>

  <div class="debug-info">
    <h2>Environment Variables</h2>
    <div id="env-vars"></div>
  </div>

  <div class="debug-info">
    <h2>Browser Information</h2>
    <div id="browser-info"></div>
  </div>

  <script>
    // Check build mode
    const buildMode = document.getElementById('build-mode');
    const isDevelopment = window.location.hostname === 'localhost' || 
                          window.location.hostname === '127.0.0.1';
    
    buildMode.innerHTML = `
      <p>Hostname: ${window.location.hostname}</p>
      <p>Environment: <span class="${isDevelopment ? 'error' : 'success'}">${isDevelopment ? 'Development' : 'Production'}</span></p>
    `;

    // Check environment variables
    const envVars = document.getElementById('env-vars');
    const envVarNames = [
      'VITE_FIREBASE_API_KEY',
      'VITE_FIREBASE_AUTH_DOMAIN',
      'VITE_FIREBASE_PROJECT_ID',
      'VITE_FIREBASE_STORAGE_BUCKET',
      'VITE_FIREBASE_MESSAGING_SENDER_ID',
      'VITE_FIREBASE_APP_ID',
      'VITE_FIREBASE_MEASUREMENT_ID',
      'VITE_CLOUDINARY_CLOUD_NAME'
    ];

    let envVarsHtml = '<ul>';
    
    // Check if import.meta.env is available (Vite specific)
    if (typeof import !== 'undefined' && import.meta && import.meta.env) {
      envVarsHtml += `<p>import.meta.env is available</p>`;
      
      for (const varName of envVarNames) {
        const value = import.meta.env[varName];
        const isPresent = value !== undefined && value !== '';
        
        envVarsHtml += `<li>${varName}: <span class="${isPresent ? 'success' : 'error'}">${isPresent ? 'Present' : 'Missing'}</span></li>`;
      }
    } else {
      envVarsHtml += `<p class="error">import.meta.env is NOT available - this indicates the build process might not be working correctly</p>`;
    }
    
    envVarsHtml += '</ul>';
    envVars.innerHTML = envVarsHtml;

    // Browser information
    const browserInfo = document.getElementById('browser-info');
    browserInfo.innerHTML = `
      <p>User Agent: ${navigator.userAgent}</p>
      <p>Window Size: ${window.innerWidth} x ${window.innerHeight}</p>
      <p>Device Pixel Ratio: ${window.devicePixelRatio}</p>
    `;
  </script>
</body>
</html>
