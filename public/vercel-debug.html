<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Vercel Debug Page</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
    .debug-info {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .debug-info pre {
      margin: 0;
      white-space: pre-wrap;
    }
    .success {
      color: green;
    }
    .error {
      color: red;
    }
  </style>
</head>
<body>
  <h1>TradeYa Vercel Debug Page</h1>
  
  <div class="debug-info">
    <h2>Environment Check</h2>
    <div id="env-check"></div>
  </div>

  <div class="debug-info">
    <h2>Build Info</h2>
    <div id="build-info"></div>
  </div>

  <div class="debug-info">
    <h2>Route Check</h2>
    <div id="route-check"></div>
  </div>

  <script>
    // Check if environment variables are loaded
    const envCheck = document.getElementById('env-check');
    try {
      const envVars = [
        'VITE_FIREBASE_API_KEY',
        'VITE_FIREBASE_AUTH_DOMAIN',
        'VITE_FIREBASE_PROJECT_ID',
        'VITE_FIREBASE_STORAGE_BUCKET',
        'VITE_FIREBASE_MESSAGING_SENDER_ID',
        'VITE_FIREBASE_APP_ID'
      ];
      
      let envOutput = '<ul>';
      let allPresent = true;
      
      envVars.forEach(varName => {
        const isPresent = window.hasOwnProperty(varName) || 
                         (window.process && window.process.env && window.process.env[varName]);
        envOutput += `<li>${varName}: <span class="${isPresent ? 'success' : 'error'}">${isPresent ? 'Present' : 'Missing'}</span></li>`;
        if (!isPresent) allPresent = false;
      });
      
      envOutput += '</ul>';
      envOutput += `<p>Overall: <span class="${allPresent ? 'success' : 'error'}">${allPresent ? 'All environment variables present' : 'Some environment variables missing'}</span></p>`;
      
      envCheck.innerHTML = envOutput;
    } catch (error) {
      envCheck.innerHTML = `<p class="error">Error checking environment variables: ${error.message}</p>`;
    }

    // Display build info
    const buildInfo = document.getElementById('build-info');
    buildInfo.innerHTML = `
      <p>Build Time: ${new Date().toISOString()}</p>
      <p>User Agent: ${navigator.userAgent}</p>
    `;

    // Check if routes are working
    const routeCheck = document.getElementById('route-check');
    routeCheck.innerHTML = `
      <p>Current Path: ${window.location.pathname}</p>
      <p>Query String: ${window.location.search}</p>
      <p>Hash: ${window.location.hash}</p>
    `;
  </script>
</body>
</html>
