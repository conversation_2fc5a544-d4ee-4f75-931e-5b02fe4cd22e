# TradeYa

A platform for trading skills and services, connecting people who want to exchange their expertise.

## Live Demo

Check out the live demo at:

- V<PERSON><PERSON> (Primary): [https://silver-fortnight-3xswhkfqv-johnny-maconnys-projects.vercel.app](https://silver-fortnight-3xswhkfqv-johnny-maconnys-projects.vercel.app)
- Firebase (Alternative): [https://tradeya-45ede.web.app](https://tradeya-45ede.web.app)

For detailed deployment information, see [DEPLOYMENT.md](DEPLOYMENT.md).

## Project Setup

This project is built with:

- React 18
- TypeScript
- Vite
- Firebase (Authentication, Firestore, Storage, Hosting)
- Vercel (Hosting)
- Cloudinary (Image storage and management)
- Tailwind CSS

### Firebase Configuration

This project uses Firebase for authentication, database, and storage. The Firebase configuration is stored in `src/firebase-config.ts` and uses environment variables to keep sensitive information secure.

The following Firebase services are used:

- **Authentication**: For user login and registration
- **Firestore Database**: For storing user data, trades, projects, collaborations, and more
- **Storage**: For storing user-uploaded files

### Firestore Database Structure

The application uses the following collections in Firestore:

- **users**: User profiles and account information
- **trades**: Trade listings with details about what users are offering and seeking
  - Includes evidence fields for showcasing work through embedded content
  - Supports both general evidence and completion-specific evidence
- **conversations**: Message threads between users
  - **messages**: Nested subcollection of individual messages within conversations
- **notifications**: User notifications for various activities
- **projects**: Project listings for collaboration
- **collaborations**: Collaboration projects with detailed role information
  - Includes evidence fields for showcasing work through embedded content
  - Supports both general evidence and role-specific evidence
- **reviews**: User reviews and ratings
- **connections**: User connections and network
- **challenges**: Community challenges and competitions

The application uses nested collections for better organization and security. For example, messages are stored as a subcollection within conversations using the path `conversations/{conversationId}/messages/{messageId}`.

### Firebase Services

- **Authentication**: For user login and registration
- **Firestore Database**: For storing application data
- **Storage**: For storing user-uploaded files
- **Hosting**: For deploying the application
- **Security Rules**: For controlling access to Firestore data

### Cloudinary Integration

The application uses Cloudinary for image storage and management. This provides several benefits:

- **Optimized Image Delivery**: Cloudinary automatically optimizes images for different devices and network conditions
- **Image Transformations**: Easily resize, crop, and transform images on-the-fly
- **Responsive Images**: Automatically generate responsive images for different screen sizes
- **CDN Delivery**: Fast image delivery through Cloudinary's global CDN

## Documentation

Detailed documentation is available for various aspects of the application:

- [Cloudinary Integration](CLOUDINARY.md) - Information about how Cloudinary is integrated for image uploads
- [Profile Pictures](PROFILE_PICTURES.md) - Detailed documentation on profile picture handling
- [Firebase Security Rules](docs/FIREBASE_SECURITY_RULES.md) - Comprehensive documentation of Firebase security rules for all collections
- [Authentication](docs/AUTHENTICATION.md) - Detailed documentation on the authentication system including Google login
- [Authentication Implementation](docs/AUTHENTICATION_IMPLEMENTATION.md) - Technical implementation guide with recent fixes and current state
- [Testing](docs/TESTING.md) - Comprehensive testing documentation including current test coverage and strategies
- [Evidence Embed System](docs/EVIDENCE_EMBED_SYSTEM_SUMMARY.md) - Summary of the Evidence Embed System for showcasing work through embedded content
  - [Detailed Implementation](docs/EVIDENCE_EMBED_SYSTEM_IMPLEMENTATION.md) - Comprehensive technical documentation of the Evidence Embed System
- [Real-time Listener Best Practices](docs/REALTIME_LISTENER_BEST_PRACTICES.md) - Best practices for working with Firebase Firestore real-time listeners, including how to avoid feedback loops

## Current Status

### Completed

- ✅ Basic project structure set up
- ✅ Package.json configuration
- ✅ TypeScript configuration
- ✅ Vite configuration
- ✅ Tailwind CSS integration
- ✅ Firebase configuration
- ✅ Authentication context
- ✅ Basic routing
- ✅ Layout components (Navbar, Footer)
- ✅ Basic page components (Home, Profile, Trades)
- ✅ Sign-up page with validation
- ✅ Profile page with editing functionality
- ✅ Trades listing page with filtering
- ✅ Trade creation form
- ✅ Trade detail page with contact form
- ✅ User dashboard with activity overview
- ✅ Edit and delete trades functionality
- ✅ Password reset functionality
- ✅ Real data storage with Firebase
- ✅ Messaging functionality with real-time updates
- ✅ Notifications system with real-time updates
- ✅ User ratings and reviews system
- ✅ Enhanced user interface with animations and transitions
- ✅ Image upload functionality with Cloudinary
- ✅ CI/CD pipeline with GitHub Actions and Firebase
- ✅ Basic testing setup with Vitest
- ✅ Enhanced user profiles with skill badges and reputation system
- ✅ Project collaboration system with applications
- ✅ Deploy to production (Firebase)
- ✅ Deploy to Vercel for multi-device testing
- ✅ Add admin panel
- ✅ Connections system
- ✅ Advanced user directory
- ✅ Challenges system
- ✅ Robust error handling for data fetching
- ✅ Firebase security rules implementation
- ✅ Collaboration projects system with role management
- ✅ Added creator profile pictures to collaboration projects
- ✅ Fixed notification system type conflicts
- ✅ Improved profile page skills handling for different data formats
- ✅ Created required Firebase indexes for notifications and users collections
- ✅ Fixed Cloudinary integration for profile pictures and image uploads
- ✅ Fixed profile pictures not appearing by supporting both photoURL and profilePicture fields
- ✅ Fixed profile placeholder image and SweetAlert initialization issues
- ✅ Added specialized component for specific user profile picture
- ✅ Updated profile picture handling to prioritize profilePicture field in Firebase
- ✅ Implemented proper Content Security Policy for Vercel deployment
- ✅ Added Google authentication with account linking for existing users
- ✅ Implemented Evidence Embed System for showcasing work through embedded content

### In Progress

- 🔄 Write more comprehensive tests
- 🔄 Implement email notifications
- 🔄 Enhance collaboration projects with more detailed role management

### To Do

- 📝 Add payment integration (if needed)
- 📝 Optimize performance
- 📝 Add analytics
- 📝 Mobile app support

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository

```bash
git clone <repository-url>
cd tradeya
```

1. Install dependencies

```bash
npm install
```

1. Set up environment variables

Create a `.env` file in the root directory with the following variables:

```env
VITE_FIREBASE_API_KEY=your-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
VITE_FIREBASE_APP_ID=your-app-id
VITE_CLOUDINARY_CLOUD_NAME=your-cloud-name
VITE_CLOUDINARY_UPLOAD_PRESET=your-upload-preset
VITE_CLOUDINARY_API_KEY=your-api-key
VITE_CLOUDINARY_API_SECRET=your-api-secret
```

> **Important**: Never commit your `.env` file to version control. It contains sensitive API keys and credentials. The `.env.example` file is provided as a template.

1. Start the development server

```bash
npm run dev
```

### Building for Production

```bash
npm run build
```

### Preview Production Build

```bash
npm run preview
```

## Project Structure

```text
tradeya/
├── public/              # Static assets
├── src/                 # Source code
│   ├── components/      # Reusable components
│   │   ├── layout/      # Layout components (Navbar, Footer)
│   │   ├── ui/          # UI components (Button, Input, etc.)
│   │   └── features/    # Feature-specific components
│   │       ├── chat/    # Chat and messaging components
│   │       ├── connections/ # User connections components
│   │       ├── evidence/   # Evidence embed components
│   │       ├── notifications/ # Notification components
│   │       ├── projects/ # Project-related components
│   │       ├── reviews/ # Review components
│   │       └── uploads/ # File upload components
│   ├── pages/           # Page components
│   │   ├── DashboardPage.tsx  # User dashboard
│   │   ├── ProfilePage.tsx    # User profile
│   │   ├── SignUpPage.tsx     # Sign up form
│   │   ├── TradeDetailPage.tsx # Trade details
│   │   ├── TradesPage.tsx     # Trades listing
│   │   ├── ProjectsPage.tsx   # Projects listing
│   │   ├── ProjectDetailPage.tsx # Project details
│   │   ├── CollaborationDetailPage.tsx # Collaboration project details
│   │   ├── ConnectionsPage.tsx # User connections
│   │   ├── UserDirectoryPage.tsx # User directory
│   │   ├── ChallengesPage.tsx # Challenges listing
│   │   └── admin/            # Admin pages
│   ├── contexts/        # Context providers
│   ├── services/        # Service modules (API calls, etc.)
│   ├── utils/           # Utility functions
│   ├── hooks/           # Custom React hooks
│   ├── App.tsx          # Main App component
│   ├── AuthContext.tsx  # Authentication context
│   ├── firebase-config.ts # Firebase configuration
│   ├── main.tsx         # Entry point
│   └── index.css        # Global styles
├── .env                 # Environment variables (not in repo)
├── .env.example         # Example environment variables
├── firestore.rules      # Firebase security rules
├── firebase.json        # Firebase configuration
├── index.html           # HTML template
├── package.json         # Dependencies and scripts
├── postcss.config.js    # PostCSS configuration
├── tailwind.config.js   # Tailwind CSS configuration
├── tsconfig.json        # TypeScript configuration
├── tsconfig.node.json   # TypeScript Node configuration
└── vite.config.ts       # Vite configuration
```

### Key Files

- **firebase-config.ts**: Contains Firebase initialization and utility functions for authentication, database operations, and storage. Uses environment variables for configuration.
- **AuthContext.tsx**: Provides authentication state and methods throughout the application.
- **App.tsx**: Sets up routing and global providers for the application.
- **firestore.ts**: Contains all Firestore database operations and interfaces.
- **firestore.rules**: Contains Firebase security rules for controlling access to Firestore data.

## Recent Improvements

### Evidence Embed System

- Implemented a comprehensive system for showcasing work through embedded content from third-party platforms
- Added support for multiple media types (videos, images, documents, code, design)
- Created reusable components for submitting and displaying evidence
- Integrated with Firebase for storing metadata without hosting media files directly
- Implemented security measures including content sanitization and CSP updates
- Added service functions for associating evidence with trades and collaborations
- Created detailed documentation for the Evidence Embed System

### Authentication System Improvements

- **LoginPage Component Restoration**: Fixed file corruption and restored full functionality with comprehensive test coverage
- **Enhanced Security**: Implemented rate limiting, security logging, and improved password validation (8+ characters)
- **Test Coverage**: All 7 LoginPage component tests passing with proper mocking and error handling
- **Navigation Updates**: Changed post-login navigation target from `/profile` to `/dashboard`
- **Google Authentication**: Complete Google sign-in integration with redirect handling and localStorage management
- **Form Validation**: Added `noValidate` attribute for custom validation with proper error messaging
- **TypeScript Integration**: Fixed AuthContext integration with proper Promise<void> return types
- **Toast Integration**: Corrected toast notification parameter order throughout authentication flow

### Vercel Deployment

- Successfully deployed the application to Vercel for multi-device testing
- Implemented proper Content Security Policy (CSP) for Vercel deployment
- Created comprehensive deployment documentation
- Set up environment variables and build configuration for Vercel
- Configured proper routing for single-page application

### Enhanced Collaboration Projects

- Added creator profile pictures to collaboration projects
- Improved the display of creator information on both preview cards and detail pages
- Enhanced the visual presentation of collaboration listings

### Improved Notification System

- Fixed type conflicts between different notification interfaces
- Implemented a more robust real-time notification system
- Added better error handling for notification processing
- Created required Firebase indexes for efficient notification queries

### Enhanced Profile Page

- Improved skills handling to support different data formats (strings and arrays)
- Added better error handling for profile data
- Enhanced the display of user skills with more robust rendering

### Connections System

- Implemented a comprehensive user connections system similar to LinkedIn
- Added connection requests with accept/reject functionality
- Created a connections management page
- Integrated connection status into user profiles

### Advanced User Directory

- Developed a searchable user directory with filtering capabilities
- Implemented skill-based and location-based filtering
- Added user cards with profile previews
- Integrated connection functionality directly into the directory
- Created required Firebase indexes for efficient user queries

### Challenges System

- Created a challenges system for users to participate in task trading and collaboration challenges
- Implemented filtering by category, difficulty, and status
- Added challenge details page with participation tracking

### Robust Error Handling

- Implemented comprehensive error handling for data fetching
- Added null checks and fallbacks for missing data
- Improved user experience by preventing crashes due to unexpected data formats

### Firebase Security Rules

- Implemented security rules to control access to Firestore data
- Added rules for specific collections like users, projects, trades, connections, conversations, and messages
- Enhanced security while maintaining functionality
- Implemented nested collection rules for messages within conversations
- Added detailed documentation in [Firebase Security Rules](docs/FIREBASE_SECURITY_RULES.md)
- Implemented best practices for real-time listeners to prevent feedback loops
- Added comprehensive error handling for Firestore operations

## Next Steps

### Immediate Tasks

1. Write more comprehensive tests
2. Implement email notifications
3. Optimize performance
4. Add analytics
5. Enhance mobile responsiveness

### Medium-term Tasks

1. Enhance the messaging system between users
2. Improve notifications for new messages, trade requests, etc.
3. Expand user ratings and reviews system
4. Enhance the dashboard with analytics and statistics
5. Add more features to the challenges system

### Long-term Tasks

1. Implement advanced search and filtering with AI recommendations
2. Add payment integration (if needed)
3. Expand admin panel capabilities for better moderation
4. Set up comprehensive analytics and reporting
5. Optimize performance and accessibility
6. Develop dedicated mobile app
7. Implement internationalization and localization
8. Expand collaboration features with team management and progress tracking

## Features

### Evidence Embed System Feature

The platform includes a comprehensive system for showcasing work through embedded content from third-party platforms:

- **Multiple Media Types**: Support for videos, images, documents, code, and design work
- **Third-Party Integration**: Embed content from popular services like YouTube, Vimeo, Google Docs, GitHub, CodePen, and more
- **Metadata Storage**: Store only metadata in Firebase, avoiding storage costs for media files
- **Secure Embedding**: Implement content security policy and HTML sanitization for safe embedding
- **Responsive Display**: Automatically adapt embedded content to different screen sizes
- **Trade Integration**: Associate evidence with trades for verification and showcase
- **Collaboration Integration**: Associate evidence with collaboration roles for progress tracking

The Evidence Embed System is designed to be:

- **Cost-effective**: No storage costs for media files
- **Scalable**: Support for a wide range of media types and services
- **Secure**: Proper sanitization and content security policy
- **User-friendly**: Simple submission and display components
- **Extensible**: Easy to add support for new services

#### Evidence Data Structure

Each evidence item contains:

```typescript
interface EmbeddedEvidence {
  id: string;           // Unique identifier
  userId: string;       // ID of the user who submitted the evidence
  userName?: string;    // Name of the user
  userPhotoURL?: string; // Profile picture URL of the user
  createdAt: Timestamp; // Creation timestamp
  title: string;        // Evidence title
  description: string;  // Evidence description
  embedUrl: string;     // URL to embed
  embedCode?: string;   // HTML embed code
  embedType: string;    // Type of embed (image, video, document, etc.)
  embedService: string; // Service name (youtube, vimeo, etc.)
  thumbnailUrl?: string; // URL to thumbnail image
  originalUrl: string;  // Original URL for direct access
}
```

### Collaboration Projects

The platform includes a robust collaboration system that allows users to create and join projects with specific roles:

- **Create Collaboration Projects**: Users can create projects and define multiple roles needed (e.g., guitarist, drummer, developer, designer)
- **Role Management**: Each role can have specific skills requirements and filled/unfilled status
- **Apply for Roles**: Users can browse available projects and apply for specific roles that match their skills
- **Project Status Tracking**: Projects can have different statuses (recruiting, in-progress, all-positions-filled, completed)
- **Project Completion**: Project owners can mark projects as completed when all work is done

The collaboration system is integrated with the user profile system, allowing project creators to view applicants' skills and experience before accepting them for roles.

#### Collaboration Data Structure

Each collaboration document in Firestore contains:

```typescript
interface CollaborationRole {
  title: string;        // Role title (e.g., "guitarist", "developer")
  filled: boolean;      // Whether the role has been filled
  skills: string[];     // Required skills for the role
  assignedUserId?: string;    // ID of user assigned to the role (if filled)
  assignedUserName?: string;  // Name of user assigned to the role (if filled)
}

interface Collaboration {
  id: string;           // Unique identifier
  title: string;        // Project title
  description: string;  // Project description
  creatorId: string;    // ID of the user who created the project
  creatorName?: string; // Name of the creator
  creatorPhotoURL?: string; // Profile picture URL of the creator
  roles: CollaborationRole[]; // Array of roles needed for the project
  status: string;       // Project status (recruiting, in-progress, completed, etc.)
  createdAt: Timestamp; // Creation timestamp
  updatedAt: Timestamp; // Last update timestamp
}
```

## CI/CD Pipeline

The project uses GitHub Actions for continuous integration and continuous deployment:

- **Continuous Integration**: Automatically runs on every push and pull request to the `main` branch
  - Installs dependencies
  - Runs linting checks
  - Runs tests
  - Builds the application

- **Continuous Deployment**: Automatically deploys to Firebase Hosting when changes are pushed to the `main` branch
  - Only runs after all tests and builds pass
  - Deploys to the production environment

### Local Deployment

To deploy the application for testing:

#### Firebase Deployment

```bash
# Login to Firebase (only needed once)
npm run firebase:login

# Deploy to preview channel
npm run deploy:firebase:preview

# Deploy to production
npm run deploy:firebase
```

#### Vercel Deployment Commands

```bash
# Deploy to preview (for testing)
npm run deploy:preview

# Deploy to production
npm run deploy
```

For more detailed deployment instructions, see [DEPLOYMENT.md](DEPLOYMENT.md).

## Development Guidelines

### Environment Variables

The application uses environment variables for configuration, especially for Firebase. These are loaded through Vite's built-in environment variable support.

In the code, environment variables are accessed using:

```typescript
import.meta.env.VITE_FIREBASE_API_KEY
```

Make sure to create a `.env` file based on the `.env.example` template before starting development.

### Code Style

- Use TypeScript for type safety
- Follow React best practices with functional components and hooks
- Use Tailwind CSS for styling
- Implement responsive design for all components
- Write meaningful comments for complex logic

## Contributing

[Add contribution guidelines here]

## License

[Add license information here]
