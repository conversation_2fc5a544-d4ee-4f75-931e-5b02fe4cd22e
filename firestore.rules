rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function hasRole(role) {
      return isAuthenticated() && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.roles.hasAny([role]);
    }
    
    function isAdmin() {
      return hasRole('admin');
    }

    function isModerator() {
      return hasRole('moderator') || isAdmin();
    }
    
    function isValidTimestamp(timestamp) {
      return timestamp is timestamp && 
        timestamp.date() <= request.time.date();
    }

    function validateUserData(data) {
      return data.size() <= 1000000 && // 1MB limit
             data.name is string &&
             data.email is string &&
             data.createdAt is timestamp &&
             (!('roles' in data) || isAdmin());
    }

    function validateTradeData(data) {
      return data.creatorId == request.auth.uid &&
             data.createdAt is timestamp &&
             data.status in ['pending', 'active', 'completed', 'cancelled'] &&
             data.participantIds is list &&
             data.participantIds.size() >= 2 &&
             data.participantIds.size() <= 10;
    }

    function validateTradeUpdate(old, new) {
      let validTransitions = {
        'pending': ['active', 'cancelled'],
        'active': ['completed', 'cancelled'],
        'completed': [],
        'cancelled': []
      };
      return old.creatorId == new.creatorId &&
             old.participantIds == new.participantIds &&
             new.status in validTransitions[old.status];
    }

    // User profiles
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow create: if isOwner(userId) && validateUserData(request.resource.data);
      allow update: if (isOwner(userId) || isAdmin()) && validateUserData(request.resource.data);
      allow delete: if isAdmin();

      // User private data
      match /private/{document=**} {
        allow read, write: if isOwner(userId);
      }
    }

    // Trade records
    match /trades/{tradeId} {
      allow read: if isAuthenticated() &&
        (resource.data.participantIds.hasAny([request.auth.uid]) || isModerator());
      allow create: if isAuthenticated() && validateTradeData(request.resource.data);
      allow update: if isAuthenticated() &&
        (resource.data.participantIds.hasAny([request.auth.uid]) || isModerator()) &&
        validateTradeUpdate(resource.data, request.resource.data);
      allow delete: if isAdmin();

      // Trade messages
      match /messages/{messageId} {
        allow read: if isAuthenticated() &&
          get(/databases/$(database)/documents/trades/$(tradeId)).data.participantIds.hasAny([request.auth.uid]);
        allow create: if isAuthenticated() &&
          get(/databases/$(database)/documents/trades/$(tradeId)).data.participantIds.hasAny([request.auth.uid]) &&
          request.resource.data.senderId == request.auth.uid &&
          request.resource.data.content.size() <= 5000;
        allow update: if false; // Messages are immutable
        allow delete: if isModerator();
      }

      // Trade evidence
      match /evidence/{evidenceId} {
        allow read: if isAuthenticated() &&
          get(/databases/$(database)/documents/trades/$(tradeId)).data.participantIds.hasAny([request.auth.uid]);
        allow create: if isAuthenticated() &&
          get(/databases/$(database)/documents/trades/$(tradeId)).data.participantIds.hasAny([request.auth.uid]) &&
          request.resource.data.uploaderId == request.auth.uid;
        allow delete: if isModerator();
      }
    }

    // System settings
    match /settings/{document=**} {
      allow read: if isAuthenticated();
      allow write: if isAdmin();
    }

    // Audit logs
    match /audit/{logId} {
      allow read: if isModerator();
      allow create: if isAuthenticated();
      allow update, delete: if false; // Audit logs are immutable
    }

    // Rate limiting
    match /ratelimits/{userId} {
      allow read: if isOwner(userId);
      allow write: if false; // Only managed by server
    }
  }
}
