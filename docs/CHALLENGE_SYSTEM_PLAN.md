# TradeYa Challenge System Implementation Plan

This document outlines the comprehensive plan for implementing the challenge system in TradeYa, including both AI-generated personalized challenges and scheduled community challenges. This plan is designed to integrate seamlessly with existing features and planned future enhancements.

## Table of Contents

1. [System Overview](#system-overview)
2. [Challenge Types](#challenge-types)
3. [Data Structure](#data-structure)
4. [Core Components](#core-components)
5. [AI Integration with OpenRouter](#ai-integration-with-openrouter)
6. [Scheduled Challenges System](#scheduled-challenges-system)
7. [User Interface](#user-interface)
8. [Integration Points](#integration-points)
9. [Implementation Phases](#implementation-phases)
10. [Technical Considerations](#technical-considerations)
11. [Compatibility Guidelines](#compatibility-guidelines)

## System Overview

The TradeYa Challenge System will provide users with two complementary types of challenges:

1. **AI-Generated Personalized Challenges**: Cost-efficient, template-based challenges tailored to individual users' skills and interests
2. **Scheduled Community Challenges**: Regular challenges available to all users on daily, weekly, and monthly cycles

This dual approach balances personalization with community engagement while managing API costs and system resources.

## Challenge Types

### Personalized Challenges

- **Skill-Based Challenges**: Focused on specific skills the user wants to develop
- **Industry-Specific Challenges**: Tailored to user's professional field
- **Quick Challenges**: Short tasks completable in under an hour
- **Comprehensive Challenges**: More involved projects for deeper skill development

### Scheduled Community Challenges

- **Daily Challenges**: Quick tasks refreshed every 24 hours
- **Weekly Challenges**: More substantial challenges running Monday-Sunday
- **Monthly Competitions**: Major challenges with significant depth
- **Themed Series**: Connected challenges around seasonal or industry themes

### Challenge Categories

- **Design**: UI/UX, graphic design, illustration, branding
- **Development**: Web, mobile, game development
- **Audio**: Music production, sound design, mixing, podcasting
- **Video**: Filmmaking, editing, animation, motion graphics
- **Writing**: Copywriting, content creation, storytelling
- **Photography**: Portrait, product, landscape, conceptual
- **3D**: Modeling, texturing, animation, rendering
- **Mixed Media**: Challenges combining multiple disciplines

## Data Structure

### Challenge Interface

```typescript
export interface Challenge {
  id: string;
  title: string;
  description: string;
  category: string;
  subcategory?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  createdBy: string; // 'ai', 'system', or user ID
  creatorName?: string;
  creatorPhotoURL?: string;
  createdAt: any; // Timestamp
  updatedAt: any; // Timestamp
  status: 'draft' | 'scheduled' | 'active' | 'completed' | 'archived';
  
  // Challenge type information
  type: 'skill' | 'industry' | 'quick' | 'comprehensive' | 'daily' | 'weekly' | 'monthly' | 'series';
  isPersonalized: boolean;
  isScheduled: boolean;
  forUserId?: string; // For personalized challenges
  
  // Scheduling information
  startDate?: any; // Timestamp
  endDate?: any; // Timestamp
  
  // Challenge content
  objectives: string[];
  resources?: {
    title: string;
    description?: string;
    url: string;
    type: 'article' | 'video' | 'tool' | 'template';
  }[];
  evaluationCriteria?: string[];
  
  // Series information
  seriesId?: string;
  seriesOrder?: number;
  
  // Rewards
  xpReward: number;
  badgeId?: string;
  
  // Participation
  participants?: string[]; // User IDs
  participantCount?: number;
  
  // Submissions
  submissions?: {
    id: string;
    userId: string;
    userName?: string;
    userPhotoURL?: string;
    content: string;
    evidenceUrls?: string[];
    createdAt: any;
    status: 'pending' | 'approved' | 'featured';
    rating?: number;
    feedback?: {
      userId: string;
      comment: string;
      rating: number;
      createdAt: any;
    }[];
  }[];
}

export interface ChallengeSeries {
  id: string;
  title: string;
  description: string;
  category: string;
  createdBy: string;
  createdAt: any;
  updatedAt: any;
  status: 'active' | 'completed' | 'archived';
  challenges: string[]; // IDs of challenges in this series
  completionBadge?: string;
  completionXpBonus?: number;
}

export interface UserChallenge {
  userId: string;
  challengeId: string;
  joinedAt: any;
  status: 'joined' | 'in_progress' | 'submitted' | 'completed' | 'abandoned';
  submissionId?: string;
  submittedAt?: any;
  completedAt?: any;
  xpEarned?: number;
  badgesEarned?: string[];
}

export interface ChallengeTemplate {
  id: string;
  title: string; // With placeholders like {{skill}} or {{medium}}
  baseDescription: string; // With placeholders
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  type: 'skill' | 'industry' | 'quick' | 'comprehensive' | 'daily' | 'weekly' | 'monthly';
  baseObjectives: string[]; // With placeholders
  baseEvaluationCriteria?: string[]; // With placeholders
  placeholders: string[]; // List of placeholders used in this template
  baseXpReward: number;
  timeEstimate: string;
}
```

### Firestore Collections

- `/challenges`: All challenges (both personalized and scheduled)
- `/challengeSeries`: Series of related challenges
- `/challengeTemplates`: Templates for AI-generated challenges
- `/users/{userId}/challenges`: User-challenge relationships
- `/users/{userId}/submissions`: User challenge submissions

## Core Components

### Challenge Service

The central service managing challenge creation, retrieval, and lifecycle:

```typescript
// Key functions to implement

// Create a new challenge (base function)
createChallenge(challengeData: Partial<Challenge>): Promise<{ challengeId: string | null; error: string | null }>

// Get challenge by ID
getChallenge(challengeId: string): Promise<{ challenge: Challenge | null; error: string | null }>

// Get challenges with filtering
getChallenges(filters: ChallengeFilters): Promise<{ challenges: Challenge[]; error: string | null }>

// Join a challenge
joinChallenge(challengeId: string, userId: string): Promise<{ success: boolean; error: string | null }>

// Submit to a challenge
submitToChallenge(challengeId: string, userId: string, submission: ChallengeSubmission): Promise<{ submissionId: string | null; error: string | null }>

// Get user challenges
getUserChallenges(userId: string, status?: string): Promise<{ challenges: UserChallenge[]; error: string | null }>

// Update challenge status
updateChallengeStatus(challengeId: string, status: string): Promise<{ success: boolean; error: string | null }>
```

### Challenge Template System

A system for managing templates used by the AI to generate challenges:

```typescript
// Key functions to implement

// Create a challenge template
createChallengeTemplate(templateData: Partial<ChallengeTemplate>): Promise<{ templateId: string | null; error: string | null }>

// Get templates by category
getTemplatesByCategory(category: string): Promise<{ templates: ChallengeTemplate[]; error: string | null }>

// Select appropriate template for user
selectTemplateForUser(userSkills: string[], difficulty?: string): Promise<ChallengeTemplate>

// Fill template placeholders
fillTemplatePlaceholders(template: ChallengeTemplate, fillers: Record<string, string>): Challenge
```

### User Challenge Management

Functions for managing a user's relationship with challenges:

```typescript
// Key functions to implement

// Get user's active challenges
getUserActiveChallenges(userId: string): Promise<{ challenges: Challenge[]; error: string | null }>

// Get user's completed challenges
getUserCompletedChallenges(userId: string): Promise<{ challenges: Challenge[]; error: string | null }>

// Get user's challenge submissions
getUserChallengeSubmissions(userId: string): Promise<{ submissions: ChallengeSubmission[]; error: string | null }>

// Track user challenge progress
updateUserChallengeStatus(userId: string, challengeId: string, status: string): Promise<{ success: boolean; error: string | null }>
```

## AI Integration with OpenRouter

### Efficient AI Challenge Generation

To minimize token usage while still providing personalized challenges:

1. **Template-Based Generation**:
   - Use pre-defined templates with placeholders
   - AI only fills in specific details rather than generating entire challenges
   - Significantly reduces token consumption

2. **User Clustering**:
   - Group users with similar skills/interests
   - Generate one challenge per cluster instead of per user
   - Share challenges among similar users

3. **Caching Strategy**:
   - Cache generated challenges for reuse
   - Implement variation system instead of generating completely new challenges
   - Store previously generated challenges as references

### OpenRouter Integration Service

```typescript
// Key functions to implement

// Get completion from OpenRouter (base function)
getOpenRouterCompletion(prompt: string, model: string = 'mistralai/mistral-7b-instruct'): Promise<string>

// Generate challenge fillers for template
generateChallengeFillers(template: ChallengeTemplate, userSkills: string[], userLevel: number): Promise<Record<string, string>>

// Generate personalized challenge
generatePersonalizedChallenge(userId: string, params?: ChallengeGenerationParams): Promise<{ challenge: Challenge | null; error: string | null }>

// Generate scheduled challenge
generateScheduledChallenge(config: ScheduledChallengeConfig): Promise<{ challenge: Challenge | null; error: string | null }>
```

### Prompt Templates

Efficient prompts for different challenge types:

```typescript
// Daily challenge prompt template
const DAILY_CHALLENGE_PROMPT = `
Create a quick daily challenge for creative professionals.
Theme: {{theme}}
Category: {{category}}
Difficulty: {{difficulty}}

Return a JSON object with:
- title: Catchy, specific title
- objectives: 2-3 clear goals
- timeEstimate: "15-30 minutes"
`;

// Personalized challenge prompt template
const PERSONALIZED_CHALLENGE_PROMPT = `
Fill in the placeholders for this challenge template:
Title: "{{templateTitle}}"
Description: "{{templateDescription}}"

User skills: {{userSkills}}
User level: {{userLevel}}

Return ONLY a JSON object with these fields:
- fillers: object with key-value pairs for each placeholder
- difficulty: suggested difficulty level
- timeEstimate: estimated hours to complete
`;
```

## Scheduled Challenges System

### Challenge Scheduling Service

```typescript
// Key functions to implement

// Schedule a challenge
scheduleChallenge(challengeData: ScheduledChallengeData): Promise<{ challengeId: string | null; error: string | null }>

// Get active scheduled challenges
getActiveScheduledChallenges(): Promise<{ challenges: Challenge[]; error: string | null }>

// Get upcoming scheduled challenges
getUpcomingScheduledChallenges(limit?: number): Promise<{ challenges: Challenge[]; error: string | null }>

// Activate scheduled challenges (when start time is reached)
activateScheduledChallenges(): Promise<{ activated: number; error: string | null }>

// Complete expired challenges (when end time is reached)
completeExpiredChallenges(): Promise<{ completed: number; error: string | null }>

// Schedule recurring challenges (daily, weekly, monthly)
scheduleRecurringChallenges(): Promise<{ scheduled: number; error: string | null }>
```

### Challenge Automation

Cloud functions to manage the challenge lifecycle:

```typescript
// Daily function to activate scheduled challenges
exports.activateChallenges = functions.pubsub
  .schedule('every 1 hours')
  .onRun(async () => {
    await activateScheduledChallenges();
    return null;
  });

// Daily function to complete expired challenges
exports.completeChallenges = functions.pubsub
  .schedule('every 1 hours')
  .onRun(async () => {
    await completeExpiredChallenges();
    return null;
  });

// Weekly function to schedule upcoming challenges
exports.scheduleWeeklyChallenges = functions.pubsub
  .schedule('every monday 00:00')
  .onRun(async () => {
    await scheduleRecurringChallenges();
    return null;
  });
```

## User Interface

### Challenge Hub Page

Central location for discovering and participating in challenges:

- **Active Challenges**: Currently available challenges
- **Challenge Calendar**: Visual calendar of scheduled challenges
- **Leaderboards**: Rankings for different challenge types
- **Upcoming Challenges**: Preview of future challenges
- **Completed Challenges**: Archive of past challenges

### Challenge Detail Page

Comprehensive view of a single challenge:

- **Challenge Information**: Title, description, objectives
- **Participation Controls**: Join button, submission form
- **Timeline**: Start/end dates, time remaining
- **Resources**: Helpful links and materials
- **Submissions Gallery**: View other participants' work
- **Leaderboard**: Top participants for this challenge

### Challenge Submission Components

Specialized components for submitting challenge work:

- **Submission Form**: Title, description, reflection
- **Evidence Uploader**: Interface for embedding evidence from external sources
- **Feedback Request**: Option to request specific feedback
- **Preview**: Review submission before finalizing

### Challenge Discovery Components

Components for finding relevant challenges:

- **Challenge Cards**: Compact preview of challenges
- **Challenge Filters**: Category, difficulty, time commitment
- **Challenge Search**: Find specific challenges
- **Recommended Challenges**: Personalized suggestions

## Integration Points

### Profile System Integration

```typescript
// Add to user profile service

// Get user's challenge stats
getUserChallengeStats(userId: string): Promise<{
  completed: number;
  active: number;
  xpEarned: number;
  badges: string[];
}>

// Add challenge tab to profile page
// In ProfilePage.tsx, add a new tab for challenges
```

### Notification System Integration

```typescript
// Add to notification service

// Send challenge start notifications
sendChallengeStartNotifications(challengeId: string): Promise<{ success: boolean; error: string | null }>

// Send challenge reminder notifications
sendChallengeReminderNotifications(challengeId: string): Promise<{ success: boolean; error: string | null }>

// Send challenge completion notifications
sendChallengeCompletionNotifications(challengeId: string): Promise<{ success: boolean; error: string | null }>
```

### Gamification System Integration

```typescript
// Add to XP service

// Award XP for challenge completion
awardChallengeCompletionXP(userId: string, challengeId: string): Promise<{ success: boolean; error: string | null }>

// Award badge for challenge completion
awardChallengeBadge(userId: string, challengeId: string): Promise<{ success: boolean; error: string | null }>

// Track challenge streak
updateChallengeStreak(userId: string, challengeType: string): Promise<{ streak: number; error: string | null }>
```

### Portfolio System Integration

```typescript
// Add to portfolio service

// Add challenge submission to portfolio
addChallengeToPortfolio(userId: string, submissionId: string, challengeId: string): Promise<{ success: boolean; error: string | null }>

// Get portfolio items from challenges
getChallengePortfolioItems(userId: string): Promise<{ items: PortfolioItem[]; error: string | null }>
```

### Evidence System Integration

```typescript
// Integrate with planned evidence embed system

// Add evidence to challenge submission
addEvidenceToSubmission(submissionId: string, evidence: EmbeddedEvidence): Promise<{ success: boolean; error: string | null }>

// Get evidence for submission
getSubmissionEvidence(submissionId: string): Promise<{ evidence: EmbeddedEvidence[]; error: string | null }>
```

## Implementation Phases

### Phase 1: Core Challenge System (2-3 weeks)

1. **Database Setup**:
   - Create challenge-related collections
   - Set up necessary indexes
   - Define security rules

2. **Basic Challenge Service**:
   - Implement core CRUD operations
   - Create challenge retrieval functions
   - Build challenge participation tracking

3. **Simple UI Components**:
   - Challenge cards
   - Challenge detail page
   - Basic submission form

### Phase 2: Scheduled Challenges (2-3 weeks)

1. **Scheduling System**:
   - Implement challenge scheduling functions
   - Create automation for challenge lifecycle
   - Build recurring challenge generation

2. **Challenge Calendar**:
   - Develop calendar view component
   - Implement active/upcoming challenge displays
   - Create challenge type indicators

3. **Challenge Hub Page**:
   - Build central challenge discovery page
   - Implement filtering and sorting
   - Create leaderboard components

### Phase 3: AI-Generated Challenges (3-4 weeks)

1. **Template System**:
   - Create challenge templates
   - Implement template selection logic
   - Build placeholder filling system

2. **OpenRouter Integration**:
   - Set up API connection
   - Implement efficient prompting
   - Create response parsing and validation

3. **User Clustering**:
   - Develop skill-based clustering
   - Implement challenge sharing logic
   - Build caching system

### Phase 4: Integration and Polish (2-3 weeks)

1. **Notification Integration**:
   - Add challenge-related notifications
   - Implement reminders and alerts
   - Create notification preferences

2. **Gamification Integration**:
   - Connect XP system to challenges
   - Implement badge awarding
   - Create streak tracking

3. **Portfolio Integration**:
   - Add challenge submissions to portfolio
   - Create portfolio showcase components
   - Implement skill verification

## Technical Considerations

### Performance Optimization

1. **Query Optimization**:
   - Create indexes for common queries:
     - challenges by status
     - challenges by category
     - challenges by user participation
     - active scheduled challenges

2. **Caching Strategy**:
   - Cache active challenges
   - Cache challenge templates
   - Cache user challenge stats

3. **Batch Processing**:
   - Process challenge status updates in batches
   - Generate multiple challenges in single operations
   - Update leaderboards periodically rather than in real-time

### Security Rules

```
// Firestore security rules for challenges

match /challenges/{challengeId} {
  // Anyone can read public challenges
  allow read: if resource.data.isPersonalized == false;
  
  // Users can read their personalized challenges
  allow read: if resource.data.isPersonalized == true && 
              resource.data.forUserId == request.auth.uid;
  
  // Only system can create scheduled challenges
  allow create: if request.auth.token.admin == true || 
                request.resource.data.createdBy == 'system' || 
                request.resource.data.createdBy == 'ai';
  
  // Users can update challenges they've joined (for participation)
  allow update: if request.auth != null && 
                resource.data.participants.hasAny([request.auth.uid]) &&
                onlyUpdatingParticipationFields();
}

match /users/{userId}/challenges/{userChallengeId} {
  // Users can read and write their own challenge data
  allow read, write: if request.auth.uid == userId;
  
  // Admin can read all user challenge data
  allow read: if request.auth.token.admin == true;
}

match /users/{userId}/submissions/{submissionId} {
  // Users can read and write their own submissions
  allow read, write: if request.auth.uid == userId;
  
  // Anyone can read public submissions
  allow read: if resource.data.isPublic == true;
  
  // Challenge participants can read submissions for that challenge
  allow read: if isChallengeParticipant(resource.data.challengeId);
}
```

### Error Handling

1. **Graceful Degradation**:
   - Fallback to template challenges if AI generation fails
   - Show cached challenges if fetching fails
   - Provide default values for missing data

2. **Retry Logic**:
   - Implement exponential backoff for API calls
   - Retry failed challenge generation
   - Queue failed operations for later processing

3. **User Feedback**:
   - Clear error messages for users
   - Loading states for all operations
   - Fallback UI for error states

## Compatibility Guidelines

To ensure this feature integrates well with the existing app and doesn't break anything, follow these guidelines:

### 1. Non-Invasive Integration

- **Add, Don't Modify**: Create new components and functions rather than modifying existing ones
- **Use Subcollections**: Store challenge data in dedicated collections to avoid affecting existing data
- **Separate Concerns**: Keep challenge logic separate from other system logic

### 2. Backward Compatibility

- **Handle Missing Data**: Gracefully handle cases where challenge data doesn't exist
- **Progressive Enhancement**: Add challenge features incrementally
- **Fallback Displays**: Provide fallbacks for users without challenges

### 3. Error Handling

- **Fail Gracefully**: If challenge operations fail, they shouldn't affect other app functionality
- **Retry Mechanisms**: Implement retry logic for critical operations
- **Error Logging**: Log errors for debugging without disrupting user experience

### 4. Testing Strategy

- **Isolated Testing**: Test challenge components in isolation
- **Integration Testing**: Test integration with other systems
- **Regression Testing**: Ensure existing functionality isn't affected

### 5. Implementation Checklist

Before implementing:
- [ ] Ensure Firebase security rules are properly configured
- [ ] Verify OpenRouter API access and quotas
- [ ] Check that user profile system supports additional data
- [ ] Confirm notification system can handle new notification types

During implementation:
- [ ] Add new components without modifying existing ones
- [ ] Create new service functions that don't affect existing ones
- [ ] Test each component in isolation before integration
- [ ] Implement proper error handling

After implementation:
- [ ] Verify existing features still work correctly
- [ ] Check performance with many challenges
- [ ] Test on different devices and screen sizes
- [ ] Ensure dark/light mode compatibility
