/**
 * Footer Component
 * 
 * A beautiful footer for the application.
 */

import React from 'react';
import { Link } from 'react-router-dom';

const Footer: React.FC = () => {
  return (
    <footer className="bg-white border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and description */}
          <div className="col-span-1 md:col-span-2">
            <Link to="/" className="text-2xl font-bold text-orange-500">
              SkillSwap
            </Link>
            <p className="mt-2 text-sm text-gray-500">
              A platform for exchanging skills, collaborating on projects, and participating in challenges.
              Connect with others, learn new skills, and showcase your talents.
            </p>
          </div>
          
          {/* Links */}
          <div>
            <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
              Features
            </h3>
            <ul className="mt-4 space-y-4">
              <li>
                <Link to="/trades" className="text-base text-gray-500 hover:text-gray-900">
                  Trades
                </Link>
              </li>
              <li>
                <Link to="/projects" className="text-base text-gray-500 hover:text-gray-900">
                  Projects
                </Link>
              </li>
              <li>
                <Link to="/challenges" className="text-base text-gray-500 hover:text-gray-900">
                  Challenges
                </Link>
              </li>
              <li>
                <Link to="/directory" className="text-base text-gray-500 hover:text-gray-900">
                  Directory
                </Link>
              </li>
            </ul>
          </div>
          
          {/* More links */}
          <div>
            <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
              Support
            </h3>
            <ul className="mt-4 space-y-4">
              <li>
                <Link to="/help" className="text-base text-gray-500 hover:text-gray-900">
                  Help Center
                </Link>
              </li>
              <li>
                <Link to="/faq" className="text-base text-gray-500 hover:text-gray-900">
                  FAQ
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-base text-gray-500 hover:text-gray-900">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="text-base text-gray-500 hover:text-gray-900">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        {/* Bottom section */}
        <div className="mt-12 border-t border-gray-200 pt-8">
          <p className="text-base text-gray-400 text-center">
            &copy; {new Date().getFullYear()} SkillSwap. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
