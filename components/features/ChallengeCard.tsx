/**
 * ChallengeCard Component
 * 
 * A beautiful card component for displaying challenge information.
 */

import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, CardHeader, CardBody, CardFooter } from '../ui/Card';
import { Button } from '../ui/Button';

// Icons (you can replace these with your preferred icon library)
const CalendarIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

const ClockIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const UsersIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
  </svg>
);

const AwardIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
  </svg>
);

const StarIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
  </svg>
);

interface ChallengeCardProps {
  challenge: {
    id: string;
    title: string;
    description: string;
    status: string;
    type: string;
    difficulty: string;
    points: number;
    startDate: any; // Timestamp or Date
    endDate: any; // Timestamp or Date
    participants: string[];
    requiredSkills?: string[];
    xpReward?: number;
  };
  onJoin?: () => void;
  onLeave?: () => void;
  isParticipating?: boolean;
  currentUserId?: string;
}

export const ChallengeCard: React.FC<ChallengeCardProps> = ({
  challenge,
  onJoin,
  onLeave,
  isParticipating = false,
  currentUserId
}) => {
  // Format date
  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    
    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch (err) {
      console.error('Error formatting date:', err);
      return 'Invalid Date';
    }
  };
  
  // Calculate days remaining
  const getDaysRemaining = () => {
    if (!challenge.endDate) return 'No deadline';
    
    try {
      const endDate = challenge.endDate.toDate ? challenge.endDate.toDate() : new Date(challenge.endDate);
      const now = new Date();
      const diffTime = endDate.getTime() - now.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays < 0) return 'Expired';
      if (diffDays === 0) return 'Last day';
      return `${diffDays} day${diffDays !== 1 ? 's' : ''} left`;
    } catch (err) {
      console.error('Error calculating days remaining:', err);
      return 'Unknown';
    }
  };
  
  // Get type badge color
  const getTypeBadgeColor = () => {
    switch (challenge.type.toLowerCase()) {
      case 'weekly':
        return 'bg-blue-100 text-blue-800';
      case 'monthly':
        return 'bg-purple-100 text-purple-800';
      case 'special':
        return 'bg-pink-100 text-pink-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  // Get difficulty color
  const getDifficultyColor = () => {
    switch (challenge.difficulty.toLowerCase()) {
      case 'easy':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'hard':
        return 'bg-orange-100 text-orange-800';
      case 'expert':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  return (
    <Card hoverable>
      <CardHeader>
        <div className="flex justify-between items-start mb-2">
          <div className="flex gap-2">
            <span className={`inline-block px-2 py-1 text-xs font-medium rounded ${getTypeBadgeColor()}`}>
              {challenge.type.charAt(0).toUpperCase() + challenge.type.slice(1)}
            </span>
            <span className={`inline-block px-2 py-1 text-xs font-medium rounded ${getDifficultyColor()}`}>
              {challenge.difficulty.charAt(0).toUpperCase() + challenge.difficulty.slice(1)}
            </span>
          </div>
          <div className="flex items-center text-sm font-medium text-orange-500">
            <AwardIcon />
            <span className="ml-1">{challenge.xpReward || challenge.points || 100} XP</span>
          </div>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-1">{challenge.title}</h3>
        <div className="flex items-center text-sm text-gray-500 mb-2">
          <ClockIcon />
          <span className="ml-1">{getDaysRemaining()}</span>
          <span className="mx-2">•</span>
          <CalendarIcon />
          <span className="ml-1">{formatDate(challenge.startDate)}</span>
        </div>
      </CardHeader>
      
      <CardBody>
        <p className="text-gray-600 mb-4 line-clamp-3">{challenge.description}</p>
        
        {/* Required Skills */}
        {challenge.requiredSkills && challenge.requiredSkills.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Required Skills:</h4>
            <div className="flex flex-wrap gap-2">
              {challenge.requiredSkills.map((skill, index) => (
                <span key={index} className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded">
                  {skill}
                </span>
              ))}
            </div>
          </div>
        )}
        
        {/* Participants */}
        <div className="flex justify-between items-center text-sm text-gray-600 mb-4">
          <div className="flex items-center">
            <UsersIcon />
            <span className="ml-1">
              {challenge.participants.length} participant{challenge.participants.length !== 1 ? 's' : ''}
            </span>
          </div>
          {isParticipating && (
            <div className="flex items-center text-green-600">
              <StarIcon />
              <span className="ml-1">You're participating</span>
            </div>
          )}
        </div>
      </CardBody>
      
      <CardFooter className="flex justify-between items-center">
        <Link
          to={`/challenges/${challenge.id}`}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
        >
          View Details
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </Link>
        
        {currentUserId && (
          isParticipating ? (
            <Button
              variant="outline"
              size="sm"
              onClick={onLeave}
            >
              Leave Challenge
            </Button>
          ) : (
            <Button
              variant="primary"
              size="sm"
              onClick={onJoin}
            >
              Join Challenge
            </Button>
          )
        )}
      </CardFooter>
    </Card>
  );
};
