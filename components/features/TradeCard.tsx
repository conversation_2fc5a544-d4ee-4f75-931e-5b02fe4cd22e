/**
 * TradeCard Component
 * 
 * A beautiful card component for displaying trade information.
 */

import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, CardHeader, CardBody, CardFooter } from '../ui/Card';
import { Button } from '../ui/Button';

// Icons (you can replace these with your preferred icon library)
const TagIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
  </svg>
);

const CalendarIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

const UserIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
  </svg>
);

interface TradeCardProps {
  trade: {
    id: string;
    title: string;
    description: string;
    status: string;
    author: {
      id: string;
      name: string;
      avatar?: string;
    };
    skillsOffered: string[];
    skillsWanted: string[];
    createdAt: any; // Timestamp or Date
    isPublic: boolean;
  };
  onJoin?: () => void;
  onLeave?: () => void;
  isParticipating?: boolean;
}

export const TradeCard: React.FC<TradeCardProps> = ({
  trade,
  onJoin,
  onLeave,
  isParticipating = false
}) => {
  // Format date
  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    
    try {
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch (err) {
      console.error('Error formatting date:', err);
      return 'Invalid Date';
    }
  };
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-purple-100 text-purple-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  return (
    <Card hoverable>
      <CardHeader>
        <div className="flex justify-between items-start mb-2">
          <span className={`inline-block px-2 py-1 text-xs font-medium rounded ${getStatusColor(trade.status)}`}>
            {trade.status.charAt(0).toUpperCase() + trade.status.slice(1)}
          </span>
          <div className="flex items-center text-sm text-gray-500">
            <CalendarIcon />
            <span className="ml-1">{formatDate(trade.createdAt)}</span>
          </div>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-1">{trade.title}</h3>
        <div className="flex items-center text-sm text-gray-500">
          <UserIcon />
          <span className="ml-1">by {trade.author.name}</span>
        </div>
      </CardHeader>
      
      <CardBody>
        <p className="text-gray-600 mb-4 line-clamp-3">{trade.description}</p>
        
        {/* Skills Offered */}
        {trade.skillsOffered.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Skills Offered:</h4>
            <div className="flex flex-wrap gap-2">
              {trade.skillsOffered.map((skill, index) => (
                <span key={index} className="inline-flex items-center bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded">
                  <TagIcon />
                  <span className="ml-1">{skill}</span>
                </span>
              ))}
            </div>
          </div>
        )}
        
        {/* Skills Wanted */}
        {trade.skillsWanted.length > 0 && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Skills Wanted:</h4>
            <div className="flex flex-wrap gap-2">
              {trade.skillsWanted.map((skill, index) => (
                <span key={index} className="inline-flex items-center bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                  <TagIcon />
                  <span className="ml-1">{skill}</span>
                </span>
              ))}
            </div>
          </div>
        )}
      </CardBody>
      
      <CardFooter className="flex justify-between items-center">
        <Link
          to={`/trades/${trade.id}`}
          className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center"
        >
          View Details
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </Link>
        
        {isParticipating ? (
          <Button
            variant="outline"
            size="sm"
            onClick={onLeave}
          >
            Leave Trade
          </Button>
        ) : (
          <Button
            variant="primary"
            size="sm"
            onClick={onJoin}
          >
            Join Trade
          </Button>
        )}
      </CardFooter>
    </Card>
  );
};
