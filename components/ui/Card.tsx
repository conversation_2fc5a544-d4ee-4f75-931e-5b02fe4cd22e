import React, { ReactNode } from 'react';

interface CardProps {
  children: ReactNode;
  className?: string;
  hoverable?: boolean;
}

interface CardSectionProps {
  children: ReactNode;
  className?: string;
}

export const Card: React.FC<CardProps> = ({ 
  children, 
  className = '', 
  hoverable = false 
}) => {
  return (
    <div 
      className={`bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden
      ${hoverable ? 'transition-shadow hover:shadow-md' : ''} 
      ${className}`}
    >
      {children}
    </div>
  );
};

export const CardHeader: React.FC<CardSectionProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`px-6 py-4 border-b border-gray-200 ${className}`}>
      {children}
    </div>
  );
};

export const CardBody: React.FC<CardSectionProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`px-6 py-4 ${className}`}>
      {children}
    </div>
  );
};

export const CardFooter: React.FC<CardSectionProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className={`px-6 py-3 bg-gray-50 border-t border-gray-200 ${className}`}>
      {children}
    </div>
  );
};
