name: Firebase Security Rules CI/CD

on:
  push:
    branches: [ main ]
    paths:
      - 'firestore.rules'
      - 'storage.rules'
      - 'src/__tests__/firebase-security.test.ts'
      - 'src/__mocks__/firebaseRules.js'
  pull_request:
    branches: [ main ]
    paths:
      - 'firestore.rules'
      - 'storage.rules'
      - 'src/__tests__/firebase-security.test.ts'
      - 'src/__mocks__/firebaseRules.js'
  workflow_dispatch:

env:
  FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
  FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
  NODE_ENV: test

jobs:
  validate-and-test:
    name: Validate and Test Rules
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install Dependencies
        run: |
          npm ci
          npm install -g firebase-tools@latest

      - name: Cache Firebase Emulators
        uses: actions/cache@v3
        with:
          path: ~/.cache/firebase/emulators
          key: ${{ runner.os }}-firebase-emulators-${{ hashFiles('firebase.json') }}
          restore-keys: |
            ${{ runner.os }}-firebase-emulators-

      - name: Lint Rules
        run: |
          firebase firestore:rules:lint firestore.rules
          firebase storage:rules:lint storage.rules

      - name: Run Security Tests
        run: npm run test:security

      - name: Save Test Results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: test-results
          path: |
            reports/security/
            coverage/
          retention-days: 30

  deploy:
    name: Deploy Rules
    needs: validate-and-test
    if: github.ref == 'refs/heads/main' && github.event_name != 'pull_request'
    runs-on: ubuntu-latest
    environment: production
    timeout-minutes: 10

    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install Firebase CLI
        run: npm install -g firebase-tools@latest

      - name: Create Backup Directory
        run: mkdir -p backups/security-rules

      - name: Backup Current Rules
        run: |
          firebase firestore:get .rules --project $FIREBASE_PROJECT_ID > backups/security-rules/firestore_$(date +%Y%m%d_%H%M%S).rules
          firebase storage:get .rules --project $FIREBASE_PROJECT_ID > backups/security-rules/storage_$(date +%Y%m%d_%H%M%S).rules
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

      - name: Deploy Rules
        run: |
          # Deploy Firestore rules
          firebase deploy --only firestore:rules --project $FIREBASE_PROJECT_ID
          
          # Deploy Storage rules
          firebase deploy --only storage:rules --project $FIREBASE_PROJECT_ID
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

      - name: Save Rule Backups
        uses: actions/upload-artifact@v3
        with:
          name: security-rules-backup
          path: backups/security-rules/
          retention-days: 30

      - name: Notify on Success
        if: success()
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '✅ Security rules deployed successfully!'
            })

      - name: Notify on Failure
        if: failure()
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '❌ Security rules deployment failed. Please check the logs for details.'
            })

  security-analysis:
    name: Security Analysis
    needs: validate-and-test
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install Dependencies
        run: npm ci

      - name: Run Security Scans
        run: |
          npm audit
          npm run security-audit

      - name: Check for Sensitive Data
        uses: zricethezav/gitleaks-action@v2
        with:
          config-path: .gitleaks.toml

      - name: Generate Security Report
        run: |
          echo "# Security Analysis Report" > security-report.md
          echo "## Firebase Rules Analysis" >> security-report.md
          echo "Generated on: $(date)" >> security-report.md
          echo "\`\`\`" >> security-report.md
          firebase firestore:rules:lint firestore.rules >> security-report.md
          firebase storage:rules:lint storage.rules >> security-report.md
          echo "\`\`\`" >> security-report.md

      - name: Upload Security Report
        uses: actions/upload-artifact@v3
        with:
          name: security-analysis
          path: security-report.md
          retention-days: 30
