name: Security Checks

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    - cron: '0 0 * * *' # Run daily at midnight UTC

jobs:
  security:
    name: Security Checks
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      actions: read
      contents: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci
        env:
          NODE_ENV: development

      - name: Make security script executable
        run: chmod +x ./scripts/security-checks.sh

      - name: Run security checks
        run: ./scripts/security-checks.sh
        env:
          CI: true

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: javascript
          queries: security-extended

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2

      - name: Run Snyk to check for vulnerabilities
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

      - name: Upload security report
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: security-report
          path: security-report.md

      - name: Check for sensitive data
        uses: zricethezav/gitleaks-action@v2
        env:
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}

      - name: Start local server for ZAP scan
        run: |
          npm run build
          npm run preview &
          sleep 10

      - name: OWASP ZAP Scan
        uses: zaproxy/action-baseline@v0.7.0
        with:
          target: 'http://localhost:4173'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'

  dependency-review:
    name: Dependency Review
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Review Dependencies
        uses: actions/dependency-review-action@v3
        with:
          fail-on-severity: high

  secret-detection:
    name: Secret Detection
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Detect Secrets
        uses: gitleaks/gitleaks-action@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}

  compliance:
    name: Compliance Checks
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install license checker
        run: npm install -g license-checker

      - name: Check License Compliance
        run: |
          license-checker --production --json > license-report.json
          if grep -i "GPL" license-report.json; then
            echo "GPL license found in dependencies!"
            exit 1
          fi

      - name: Upload License Report
        uses: actions/upload-artifact@v3
        with:
          name: license-report
          path: license-report.json

      - name: Check for Banned Dependencies
        run: |
          BANNED_DEPS=(
            "banned-package"
            "unsafe-module"
            "deprecated-library"
          )
          for dep in "${BANNED_DEPS[@]}"; do
            if grep -q "\"$dep\":" package.json; then
              echo "Banned dependency $dep found!"
              exit 1
            fi
          done

  notify:
    name: Notify Security Team
    needs: [security, dependency-review, secret-detection, compliance]
    runs-on: ubuntu-latest
    if: failure()

    steps:
      - name: Send Notification
        uses: actions/github-script@v6
        with:
          script: |
            const issueBody = `
            # Security Check Failure Alert
            
            Security checks have failed in the pipeline.
            
            - Workflow: ${{ github.workflow }}
            - Run: ${{ github.run_id }}
            - Trigger: ${{ github.event_name }}
            - Failed Jobs: ${{ join(needs.*.result) }}
            
            Please investigate immediately.
            `;
            
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: '🚨 Security Check Failure',
              body: issueBody,
              labels: ['security', 'high-priority']
            });
