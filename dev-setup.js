#!/usr/bin/env node

/**
 * Development Setup Script
 * 
 * This script helps set up the development environment for the TradeYa project.
 * It performs the following tasks:
 * 1. Checks for required dependencies
 * 2. Sets up environment variables
 * 3. Fixes common TypeScript errors
 * 4. Runs the development server
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// ANSI color codes for terminal output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  
  fg: {
    black: '\x1b[30m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    crimson: '\x1b[38m'
  },
  
  bg: {
    black: '\x1b[40m',
    red: '\x1b[41m',
    green: '\x1b[42m',
    yellow: '\x1b[43m',
    blue: '\x1b[44m',
    magenta: '\x1b[45m',
    cyan: '\x1b[46m',
    white: '\x1b[47m',
    crimson: '\x1b[48m'
  }
};

// Helper functions
const log = {
  info: (msg) => console.log(`${colors.fg.blue}ℹ ${colors.reset}${msg}`),
  success: (msg) => console.log(`${colors.fg.green}✓ ${colors.reset}${msg}`),
  warning: (msg) => console.log(`${colors.fg.yellow}⚠ ${colors.reset}${msg}`),
  error: (msg) => console.log(`${colors.fg.red}✗ ${colors.reset}${msg}`),
  title: (msg) => console.log(`\n${colors.bright}${colors.fg.cyan}${msg}${colors.reset}\n`)
};

const runCommand = (command, options = {}) => {
  try {
    return execSync(command, { encoding: 'utf8', stdio: options.silent ? 'pipe' : 'inherit', ...options });
  } catch (error) {
    if (options.ignoreError) {
      return error.stdout || '';
    }
    throw error;
  }
};

const askQuestion = (question) => {
  return new Promise((resolve) => {
    rl.question(`${colors.fg.yellow}? ${colors.reset}${question} `, (answer) => {
      resolve(answer);
    });
  });
};

// Main script
async function main() {
  log.title('TradeYa Development Setup');
  
  // Check Node.js version
  const nodeVersion = process.version;
  log.info(`Node.js version: ${nodeVersion}`);
  
  const versionMatch = nodeVersion.match(/^v(\d+)\./);
  const majorVersion = versionMatch ? parseInt(versionMatch[1], 10) : 0;
  
  if (majorVersion < 16) {
    log.error('Node.js version 16 or higher is required');
    process.exit(1);
  } else {
    log.success('Node.js version is compatible');
  }
  
  // Check for npm
  try {
    const npmVersion = runCommand('npm --version', { silent: true }).trim();
    log.info(`npm version: ${npmVersion}`);
    log.success('npm is installed');
  } catch (error) {
    log.error('npm is not installed or not in PATH');
    process.exit(1);
  }
  
  // Check if dependencies are installed
  if (!fs.existsSync('node_modules')) {
    log.warning('node_modules not found, installing dependencies...');
    runCommand('npm install');
    log.success('Dependencies installed');
  } else {
    log.success('Dependencies already installed');
  }
  
  // Check for .env file
  if (!fs.existsSync('.env')) {
    log.warning('.env file not found, creating from template...');
    
    if (fs.existsSync('.env.example')) {
      fs.copyFileSync('.env.example', '.env');
      log.success('Created .env file from .env.example');
    } else {
      // Create basic .env file
      fs.writeFileSync('.env', 'NODE_ENV=development\n');
      log.warning('Created basic .env file. You may need to add Firebase configuration.');
    }
  } else {
    log.success('.env file exists');
  }
  
  // Fix TypeScript errors
  const fixTypeScript = await askQuestion('Would you like to fix TypeScript errors? (y/n) ');
  if (fixTypeScript.toLowerCase() === 'y') {
    log.info('Running TypeScript error fixer...');
    try {
      runCommand('node fix-ts-errors.js');
      log.success('TypeScript errors fixed');
    } catch (error) {
      log.error('Failed to fix TypeScript errors');
      console.error(error);
    }
  }
  
  // Start development server
  const startDev = await askQuestion('Would you like to start the development server? (y/n) ');
  if (startDev.toLowerCase() === 'y') {
    log.info('Starting development server...');
    runCommand('npm run dev');
  } else {
    log.info('Setup complete. To start the development server, run: npm run dev');
  }
  
  rl.close();
}

main().catch((error) => {
  log.error('An error occurred during setup:');
  console.error(error);
  process.exit(1);
});
