{"indexes": [{"collectionGroup": "trades", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participantIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "trades", "queryScope": "COLLECTION", "fields": [{"fieldPath": "creatorId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participantIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "roles", "arrayConfig": "CONTAINS"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "trades", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "private", "fieldPath": "userId", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}]}