{"version": 2, "public": true, "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Cross-Origin-Opener-Policy", "value": "same-origin-allow-popups"}, {"key": "Cross-Origin-Embedder-Policy", "value": "credentialless"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://*.firebaseio.com https://*.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: https://res.cloudinary.com https://*.googleusercontent.com https://ui-avatars.com; font-src 'self' data: https://fonts.gstatic.com; connect-src 'self' wss://*.firebaseio.com https://*.googleapis.com https://firestore.googleapis.com https://*.cloudfunctions.net https://api.cloudinary.com https://res.cloudinary.com; frame-src 'self' https://*.firebaseapp.com https://*.google.com;"}]}], "buildCommand": "npm run vercel-build", "outputDirectory": "dist", "framework": "vite", "github": {"silent": true}}