import js from '@eslint/js';
import tseslint from 'typescript-eslint';
import reactHooksPlugin from 'eslint-plugin-react-hooks';
import reactRefreshPlugin from 'eslint-plugin-react-refresh';
import globals from 'globals';

export default [
  // Global ignores
  {
    ignores: ['dist', '.eslintrc.{js,cjs}'],
  },

  // ESLint recommended rules
  js.configs.recommended,

  // TypeScript recommended rules and parser
  ...tseslint.configs.recommended,
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        project: './tsconfig.json', // Assuming a tsconfig.json in the project root
      },
    },
  },

  // React Hooks recommended rules
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      'react-hooks': reactHooksPlugin,
    },
    rules: {
      ...reactHooksPlugin.configs.recommended.rules,
    },
  },

  // React Refresh rules
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    plugins: {
      'react-refresh': reactRefreshPlugin,
    },
    rules: {
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
    },
  },

  // Node.js environment for script files, with explicitly defined browser globals
  {
    files: ['src/scripts/**/*.js'],
    languageOptions: {
      globals: {
        ...globals.node,
        // Explicitly add commonly used browser globals
        'window': 'writable',
        'localStorage': 'writable',
        'sessionStorage': 'writable',
        'console': 'readonly', // console is a common global
      },
    },
    rules: {
      '@typescript-eslint/no-require-imports': 'off', // Allow require in Node.js scripts
      'no-undef': 'off', // Turn off no-undef as we are defining necessary globals
    },
  },

  // Jest/Test environment for test files
  {
    files: ['src/utils/__tests__/**/*.js', 'src/**/*.test.ts', 'src/**/*.test.tsx'],
    languageOptions: {
      globals: {
        ...globals.jest,
        // Explicitly add globals used in test setup files
        'module': 'writable',
        'require': 'readonly',
        '__dirname': 'readonly',
        // Browser globals for mocking
        'window': 'writable',
        'localStorage': 'writable',
        'sessionStorage': 'writable',
        'process': 'readonly', // Process is used in setEnvVars.js
        'global': 'writable', // Global is used in setEnvVars.js

      },
    },
    rules: {
      '@typescript-eslint/no-require-imports': 'off', // Allow require in test setup files
      'no-undef': 'off', // Turn off no-undef as we are defining necessary globals
      '@typescript-eslint/no-unused-vars': ['warn', { varsIgnorePattern: '^(module|require|__dirname)$' }], // Allow specific unused vars in test setup
    },
  },
];
